# 性能分析：从 handleTaskLoop 到第一个字符返回的耗时统计

## 概述

本文档描述了在 `AgentManager.ts` 中添加的详细性能统计功能，用于分析从 `handleTaskLoop` 函数开始执行到第一个字符返回的完整耗时。所有性能数据都会记录到专用的性能日志文件 `perf-{sessionId}.log` 中，便于后续分析和优化。

## 性能日志系统

### 专用性能日志记录器

新增了 `PerfLogger` 类，专门用于记录各个异步函数的耗时信息：

- **日志文件**: `logs/perf-{sessionId}.log`
- **日志格式**: `[timestamp] [level] message`
- **自动轮转**: 支持文件大小和时间轮转
- **会话隔离**: 每个会话有独立的性能日志文件

### 日志级别说明

- `START`: 函数开始执行
- `END`: 函数执行结束，包含耗时
- `MILESTONE`: 关键性能里程碑
- `PHASE_START/PHASE_END`: 阶段性能汇总
- `RECORD`: 单次性能记录
- `ERROR/WARN/DEBUG`: 错误、警告、调试信息

## 关键性能指标

### 1. 主要耗时统计

- **任务总耗时**: 从 `handleTaskLoop` 开始到任务完成的总时间
- **第一个字符返回总耗时**: 从 `recursivelyMakeLLMRequests` 开始到第一个字符返回的时间
- **API 请求到第一个字符耗时**: 从发送 HTTP 请求到收到第一个字符的时间
- **预处理总耗时**: 从开始到 API 请求发送前的所有处理时间

### 2. 详细异步函数耗时

#### handleTaskLoop 阶段
- `initTrace`: 初始化追踪
- `initCheckpointTracker`: 初始化检查点追踪器
- `checkUserModifiedFiles`: 分析用户手动修改的文件
- `checkpointService.getStatus`: 获取检查点状态
- `saveCheckpoint`: 保存检查点
- `getWikiList`: 获取项目 Wiki 列表

#### recursivelyMakeLLMRequests 阶段
- `handleApiRequestLimits`: 处理 API 请求限制
- `messageService.say`: 发送消息
- `getEnvironmentDetails`: 获取环境详情
- `getUserActionMessage`: 获取用户操作消息
- `addToApiConversationHistory`: 添加到对话历史
- `prepareRequestParams`: 准备请求参数
- `optimizeMessagesContext`: 优化消息上下文
- `formatMessages`: 格式化 LLM 消息
- `getRulesListForAgent`: 获取用户和项目规则
- `getCommonToolsFunctionSchemas`: 获取工具函数模式
- `getEnvironmentAndFiles`: 获取环境详情V2和文件列表
- `buildRequestParams`: 构建完整的请求参数

## 性能日志格式示例

### 基本函数耗时记录
```
[2024-01-15T10:30:15.123Z] [START] initTrace
[2024-01-15T10:30:15.128Z] [END] initTrace - Duration: 5ms
[2024-01-15T10:30:15.130Z] [START] initCheckpointTracker
[2024-01-15T10:30:15.142Z] [END] initCheckpointTracker - Duration: 12ms
```

### 关键性能里程碑
```
[2024-01-15T10:30:16.250Z] [MILESTONE] 🎯 first-token-received - Total: 1127ms - Details: {"sessionId":"abc123","chatId":"def456","model":"Kwaipilot Pro","apiDuration":856,"preApiDuration":271}
[2024-01-15T10:30:18.500Z] [MILESTONE] handleTaskLoop-complete - Total: 3377ms - Details: {"sessionId":"abc123","chatId":"def456"}
```

### 阶段性能汇总
```
[2024-01-15T10:30:16.100Z] [PHASE_START] 📊 === Pre-API Processing Phase Summary ===
[2024-01-15T10:30:16.101Z] [PHASE_DETAIL]   initTrace: 5ms (1.8%)
[2024-01-15T10:30:16.102Z] [PHASE_DETAIL]   initCheckpointTracker: 12ms (4.4%)
[2024-01-15T10:30:16.103Z] [PHASE_DETAIL]   checkUserModifiedFiles: 25ms (9.2%)
[2024-01-15T10:30:16.104Z] [PHASE_DETAIL]   optimizeMessagesContext: 180ms (66.4%)
[2024-01-15T10:30:16.105Z] [PHASE_END] 📊 Total Pre-API Processing Duration: 271ms
```

## 使用方法

### 1. 运行性能测试

```bash
# 启动性能测试脚本
node test/performance-test.mjs

# 或者直接启动开发服务器并观察日志
npm run dev
```

### 2. 查看性能日志

```bash
# 实时查看性能日志
tail -f logs/perf-{sessionId}.log

# 查看关键性能里程碑
grep "MILESTONE" logs/perf-{sessionId}.log

# 查看所有函数耗时
grep "Duration:" logs/perf-{sessionId}.log

# 查看阶段性能汇总
grep "PHASE" logs/perf-{sessionId}.log
```

### 3. 性能分析

通过性能日志可以分析：
- 哪个异步函数耗时最长
- 预处理阶段 vs API 请求阶段的耗时比例
- 优化潜力最大的环节
- 不同会话之间的性能差异

## 性能优化建议

基于性能统计数据，可以考虑以下优化方向：

1. **并行化处理**: 将可以并行执行的异步函数进行并行处理
2. **缓存优化**: 对重复获取的环境信息、文件列表等进行缓存
3. **上下文优化**: 优化消息上下文处理算法
4. **请求合并**: 合并多个小的 API 请求
5. **预加载**: 提前加载常用的配置和环境信息

## API 接口

### LoggerManager 新增方法

```typescript
// 开始记录函数执行时间
perfStart(functionName: string, context?: string): void

// 结束记录函数执行时间并返回耗时
perfEnd(functionName: string, context?: string): number

// 记录单次性能数据
perfRecord(functionName: string, duration: number, context?: string): void

// 记录关键性能里程碑
perfMilestone(milestone: string, totalDuration: number, details?: Record<string, any>): void

// 记录阶段性能汇总
perfPhaseSummary(phase: string, functions: Array<{name: string, duration: number}>, totalDuration: number): void
```

## 注意事项

1. **性能开销**: 性能日志记录会增加少量运行时开销，但对整体性能影响很小
2. **文件管理**: 性能日志文件会自动轮转，避免文件过大
3. **会话隔离**: 每个会话的性能数据独立记录，便于分析
4. **时间精度**: 时间统计基于 `Date.now()`，精度为毫秒级
5. **网络影响**: 网络延迟会显著影响 API 请求到第一个字符的耗时

## 故障排除

### 常见问题

1. **找不到性能日志文件**: 确保 `logs` 目录存在且有写入权限
2. **性能数据不完整**: 检查是否有异常中断导致某些函数未正常结束计时
3. **耗时异常**: 对比多次测试结果，排除网络波动等外部因素影响
