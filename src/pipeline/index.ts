import { nanoid } from 'nanoid';
import { checkIfNeedAddTask, getAllFiles, indexFiles } from './utils';
import path from 'path';
const gitUrl = process.env.INDEX_REPO_URL;
const commitId = process.env.INDEX_COMMITID;
const branch = process.env.INDEX_BUILD_BRANCH;
console.log('gitUrl', gitUrl, 'commitId', commitId, 'branch', branch);
const dirPath = path.join(process.env.JOB_WS_ROOT || '', process.env.BUILD_DIR || '', 'repo-index-folder');
console.log('dirPath', dirPath);
async function main() {
  const start = Date.now();
  const allFiles = await getAllFiles(dirPath);
  console.log(allFiles.length);
  const totalFiles = allFiles.length;
  if (!gitUrl || !commitId) {
    throw new Error('gitUrl or commitId is not set');
  }
  const repoState = {
    commit_id: commitId,
    base_commit_id: commitId || undefined,
    created_at: Date.now(),
    updated_at: Date.now(),
    git_url: gitUrl,
    total_files: totalFiles,
    done_files: 0,
    is_building: true,
    using_remote_index: false
  };
  let doneFiles = 0;
  const BATCH_SIZE = 300; // 减小批次大小

  // Process files in batches
  for (let i = 0; i < allFiles.length; i += BATCH_SIZE) {
    console.log('i', i);
    const batchFiles = allFiles.slice(i, Math.min(i + BATCH_SIZE, allFiles.length));

    // 并行处理当前批次的文件
    const batchResults = await Promise.all(
      batchFiles.map(async (filePath) => {
        doneFiles++;

        // 详细检查
        if (!(await checkIfNeedAddTask(filePath, dirPath))) {
          return null;
        }
        console.log('filePath', filePath);
        return {
          task_id: nanoid(),
          filepath: filePath,
          dir_path: dirPath,
          git_url: gitUrl,
          created_at: Date.now(),
          updated_at: Date.now(),
          status: 'pending' as const,
          file_action: 'create' as const,
          retry_count: 0
        };
      })
    );
    const INDEXING_BATCH_SIZE = 20;
    const MAX_GROUPS = 10;

    // 过滤出有效的任务
    const validTasks = batchResults.filter((task): task is NonNullable<typeof task> => task !== null);

    // 将 validTasks 分成多个小组，最多分成 10 个小组，每组 20 个文件，使用 Promise.all 并行处理
    const totalGroups = Math.min(Math.ceil(validTasks.length / INDEXING_BATCH_SIZE), MAX_GROUPS);
    const actualBatchSize = Math.ceil(validTasks.length / totalGroups);

    const indexingPromises = [];
    for (let j = 0; j < validTasks.length; j += actualBatchSize) {
      const batch = validTasks.slice(j, j + actualBatchSize);
      const groupIndex = Math.floor(j / actualBatchSize) + 1;

      // 准备批量索引的文件数据
      const filesToIndex = batch.map((task) => ({
        filepath: task.filepath,
        action: 'create'
      }));

      console.debug(`Preparing group ${groupIndex}/${totalGroups} with ${batch.length} files`);

      // 创建 Promise 但不立即等待
      const indexingPromise = indexFiles(filesToIndex as any, gitUrl, commitId, dirPath)
        .then(() => {
          console.debug(`Completed group ${groupIndex}/${totalGroups}`);
        })
        .catch((e) => {
          console.error(`Error in group ${groupIndex}/${totalGroups}:`, e);
          throw e;
        });

      indexingPromises.push(indexingPromise);
    }

    // 使用 Promise.all 同时请求所有组
    try {
      const res = await Promise.all(indexingPromises);
      console.log('res', JSON.stringify(res));
      console.debug(`All ${totalGroups} groups completed successfully`);
    } catch (e) {
      console.error('Some groups failed:', e);
    }
    console.log(`Batch ${i / BATCH_SIZE + 1} processed. Done files: ${doneFiles}`);
  }
  console.log(`Total files: ${totalFiles}, Done files: ${doneFiles}, cost: ${(Date.now() - start) / 1000}s}`);
}

main().then(() => {
  console.log('finished pipeline indexing');
});
