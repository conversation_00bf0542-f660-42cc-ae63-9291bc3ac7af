import { IG_PATERNS, isTextFile } from '@/index-manager/utils';
import ignore from 'ignore';
import { promisify } from 'util';
import { exec } from 'child_process';
import * as fs from 'fs';
import path from 'path';
import { filename_to_lang } from '@/code/chunk/code';
import { PROGRAM_FILES } from '@/code/analyze/code_utils';

const execAsync = promisify(exec);
const MAX_BUFFER = 1024 * 1024 * 20; // 20MB
const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1M

export async function executeGitCommand(dirPath: string, command: string): Promise<string> {
  try {
    const { stdout } = await execAsync(`git -C ${dirPath} ${command}`, {
      maxBuffer: MAX_BUFFER
    });
    return stdout.trim();
  } catch (error) {
    console.debug(`Git command failed: git -C ${dirPath} ${command}, error: ${error}`);
    throw error;
  }
}

/**
 * 获取仓库所有文件列表（包括变更文件）
 */
export async function getAllFiles(dirPath: string): Promise<string[]> {
  try {
    const ignoreFilter = ignore().add(IG_PATERNS);

    // 并行获取未追踪和已追踪的文件，以及用户变更文件
    const [untrackedFiles, trackedFiles] = await Promise.all([
      executeGitCommand(dirPath, 'ls-files --others --exclude-standard').catch(() => ''),
      executeGitCommand(dirPath, 'ls-tree -r --name-only HEAD').catch(() => '')
    ]);

    const allFiles = [
      ...(untrackedFiles ? untrackedFiles.split('\n') : []),
      ...(trackedFiles ? trackedFiles.split('\n') : [])
      // 添加用户变更文件路径（排除已删除的文件）
    ];

    // 去重并过滤空值
    const uniqueFiles = [...new Set(allFiles.filter(Boolean))];

    // 并行检查文件存在性
    const existingFiles = await Promise.all(
      uniqueFiles.map(async (file) => {
        if (ignoreFilter.ignores(file)) return null;

        try {
          await fs.promises.access(path.join(dirPath, file));
          return file;
        } catch {
          return null;
        }
      })
    );

    const result = existingFiles.filter((file): file is string => file !== null);
    console.log(`Found ${result.length} total files in ${dirPath} (including  changed files)`);

    return result;
  } catch (error) {
    console.error(`Error getting all files from git: ${error}`);
    return [];
  }
}

// 获取文件大小
const getFileSize = async (filePath: string) => {
  try {
    const stats = await fs.promises.stat(filePath);
    return stats.size;
  } catch (error) {
    console.error('Error getting file size:', filePath, error);
    return null;
  }
};
export async function checkIfNeedAddTask(filePath: string, dirPath: string) {
  try {
    if (filePath.toLowerCase().indexOf('readme') > -1) {
      return true;
    }

    const lang = filename_to_lang(filePath);
    if (!lang || !PROGRAM_FILES.includes(lang)) {
      return false;
    }

    // 检查是否为文本文件
    if (!(await isTextFile(filePath, dirPath))) {
      return false;
    }
    // 判断文件大小，是否超过 1M
    const fullPath = path.join(dirPath, filePath);
    const fileSize = await getFileSize(fullPath);
    if (fileSize && fileSize > MAX_FILE_SIZE) {
      return false;
    }

    return true;
  } catch (e) {
    console.log(e);
  }
}

export async function indexFiles(
  files: { filepath: string; action: 'modify' | 'delete' | 'create' }[],
  gitUrl: string,
  commitId: string,
  dirPath: string
) {
  const startTime = Date.now();
  // 构造 JSON 请求体
  const requestBody = {
    repoUrl: gitUrl,
    commitId,
    files: files
      .map((file) => {
        const fileData: any = {
          filepath: file.filepath,
          action: file.action
        };

        const realPath = path.join(dirPath, file.filepath);
        if (fs.existsSync(realPath)) {
          const content = fs.readFileSync(realPath, 'utf-8');
          fileData.content = content;
        } else {
          return null;
        }

        return fileData;
      })
      .filter((fileData) => fileData !== null)
  };

  // 使用 post 方法发送 JSON 数据
  const { data } = await post<{ success: boolean }>(
    '/nodeapi/indexing/file-index/base',
    JSON.stringify(requestBody)
  ).catch((e: any) => {
    console.error('Index files failed:', e);
    return { data: {} };
  });

  return data;
}
const HOST_NAME = 'http://kwaipilot-server.internal';
/**
 * 使用 fetch 发送 POST 请求
 * @param url 请求地址
 * @param body 请求体
 * @param headers 自定义请求头
 * @returns Promise with response data
 */
async function post<T>(url: string, body: string, headers: Record<string, string> = {}): Promise<{ data: T }> {
  try {
    const response = await fetch(HOST_NAME + url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    return { data };
  } catch (error) {
    console.error('POST request failed:', error);
    throw error;
  }
}
