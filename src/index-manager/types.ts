export type IndexTaskStatus = 'pending' | 'indexing';
export type IndexTaskAction = 'modify' | 'delete' | 'create';

export interface IndexTask {
  id: string;
  filepath: string;
  repoDir: string;
  status: IndexTaskStatus;
  fileAction: IndexTaskAction;
  createdAt: number;
  updatedAt: number;
}

export interface IndexOptions {
  maxFileSize: number;
  batchSize: number;
  delayPerFile: number;
  delayPerBatch: number;
  maxCacheSize: number;
  cacheTTL: number;
}

export interface RepoIndexOptions {
  dirPath: string;
  commitId: string;
  gitUrl: string;
  ideVersion: string;
  pluginVersion: string;
  platform: string;
  manual: boolean;
}
