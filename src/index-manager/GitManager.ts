import { exec } from 'child_process';
import { promisify } from 'util';
import { Logger } from '@/util/log';
import { IndexTaskAction } from '@/index-manager/types';
import * as fs from 'fs';
import * as path from 'path';
import ignore, { Ignore } from 'ignore';
import { getKwaipilotIgnoreFiles } from '@/util/file';
import { IG_PATERNS } from './utils';

const execAsync = promisify(exec);

// 常量定义
const MAX_BUFFER = 1024 * 1024 * 20; // 20MB
const STATUS_PRIORITY_MAP: Record<IndexTaskAction, number> = {
  delete: 0,
  create: 1,
  modify: 2
};

// 类型定义
interface FileChange {
  path: string;
  status: IndexTaskAction;
}

export class GitManager {
  private readonly logger: Logger = new Logger('GitManager');

  /**
   * 执行 Git 命令的通用方法
   */
  private async executeGitCommand(dirPath: string, command: string): Promise<string> {
    try {
      const { stdout } = await execAsync(`git -C ${dirPath} ${command}`, {
        maxBuffer: MAX_BUFFER
      });
      return stdout.trim();
    } catch (error) {
      this.logger.debug(`Git command failed: git -C ${dirPath} ${command}, error: ${error}`);
      throw error;
    }
  }

  /**
   * 规范化 Git 文件路径，去除双引号和处理转义字符
   */
  private normalizeGitFilePath(filePath: string): string {
    if (!filePath) return filePath;

    // 如果文件路径被双引号包围（通常是包含中文或特殊字符的情况）
    if (filePath.startsWith('"') && filePath.endsWith('"')) {
      // 去掉首尾的双引号
      filePath = filePath.slice(1, -1);

      // 处理 UTF-8 字节序列的八进制转义，比如 \\344\\270\\255\\346\\226\\207
      try {
        // 找到所有的八进制转义序列
        const bytes: number[] = [];
        let i = 0;
        while (i < filePath.length) {
          if (filePath[i] === '\\' && i + 3 < filePath.length && /\d{3}/.test(filePath.slice(i + 1, i + 4))) {
            // 转换八进制为字节值
            const octal = filePath.slice(i + 1, i + 4);
            bytes.push(parseInt(octal, 8));
            i += 4;
          } else {
            // 普通字符直接添加其字节值
            bytes.push(filePath.charCodeAt(i));
            i++;
          }
        }

        // 将字节数组转换为 UTF-8 字符串
        const uint8Array = new Uint8Array(bytes);
        filePath = new TextDecoder('utf-8').decode(uint8Array);
      } catch (error) {
        // 如果解码失败，返回原始路径（去掉双引号）
        this.logger.warn(`Failed to decode file path: ${filePath}, error: ${error}`);
      }
    }

    return filePath;
  }

  /**
   * 解析 Git 状态输出为文件变更对象
   */
  private parseGitStatusOutput(output: string, ignoreFilter: Ignore): FileChange[] {
    if (!output) return [];

    return output
      .split('\n')
      .filter((line) => {
        if (!line) return false;
        const filePath = this.normalizeGitFilePath(line.split('\t')[1]);
        return filePath && !ignoreFilter.ignores(filePath);
      })
      .map((line) => {
        const [status, rawFilePath] = line.split('\t');
        const filePath = this.normalizeGitFilePath(rawFilePath);
        return {
          path: filePath,
          status: this.mapGitStatusToAction(status.charAt(0))
        };
      });
  }

  /**
   * 将 Git 状态字符映射为 IndexTaskAction
   */
  private mapGitStatusToAction(gitStatus: string): IndexTaskAction {
    switch (gitStatus) {
      case 'A':
        return 'create';
      case 'M':
        return 'modify';
      case 'D':
        return 'delete';
      default:
        return 'modify';
    }
  }

  /**
   * 合并并去重文件变更，保留优先级最高的状态
   */
  private deduplicateChanges(changes: FileChange[]): FileChange[] {
    const fileMap = new Map<string, FileChange>();

    changes.forEach((change) => {
      const existing = fileMap.get(change.path);
      if (!existing || STATUS_PRIORITY_MAP[change.status] < STATUS_PRIORITY_MAP[existing.status]) {
        fileMap.set(change.path, change);
      }
    });

    return Array.from(fileMap.values());
  }

  /**
   * 获取指定类型的文件变更
   */
  private async getFileChangesByType(
    dirPath: string,
    command: string,
    ignoreFilter: Ignore,
    fallbackStatus?: IndexTaskAction
  ): Promise<FileChange[]> {
    try {
      const output = await this.executeGitCommand(dirPath, command);
      if (fallbackStatus) {
        // 对于未追踪文件，直接按行分割并设置状态
        return output
          ? output
              .split('\n')
              .filter((filePath) => filePath && !ignoreFilter.ignores(this.normalizeGitFilePath(filePath)))
              .map((filePath) => ({ path: this.normalizeGitFilePath(filePath), status: fallbackStatus }))
          : [];
      }

      return this.parseGitStatusOutput(output, ignoreFilter);
    } catch (error) {
      this.logger.warn(`Failed to get file changes with command "${command}": ${error}`);
      return [];
    }
  }

  /**
   * 获取仓库所有文件列表（包括变更文件）
   */
  public async getAllFiles(dirPath: string): Promise<string[]> {
    try {
      const ignoreFilter = ignore().add(getKwaipilotIgnoreFiles(dirPath)).add(IG_PATERNS);

      // 并行获取未追踪和已追踪的文件，以及用户变更文件
      const [untrackedFiles, trackedFiles, userChangedFiles] = await Promise.all([
        this.executeGitCommand(dirPath, 'ls-files --others --exclude-standard').catch(() => ''),
        this.executeGitCommand(dirPath, 'ls-tree -r --name-only HEAD').catch(() => ''),
        this.getUserChangedFiles(dirPath).catch(() => [])
      ]);

      const allFiles = [
        ...(untrackedFiles ? untrackedFiles.split('\n') : []),
        ...(trackedFiles ? trackedFiles.split('\n') : []),
        // 添加用户变更文件路径（排除已删除的文件）
        ...userChangedFiles.filter((change) => change.status !== 'delete').map((change) => change.path)
      ];

      // 去重并过滤空值
      const uniqueFiles = [...new Set(allFiles.filter(Boolean))];

      // 并行检查文件存在性
      const existingFiles = await Promise.all(
        uniqueFiles.map(async (file) => {
          if (ignoreFilter.ignores(file)) return null;

          try {
            await fs.promises.access(path.join(dirPath, file));
            return file;
          } catch {
            return null;
          }
        })
      );

      const result = existingFiles.filter((file): file is string => file !== null);
      this.logger.info(
        `Found ${result.length} total files in ${dirPath} (including ${userChangedFiles.length} changed files)`
      );

      return result;
    } catch (error) {
      this.logger.error(`Error getting all files from git: ${error}`);
      return [];
    }
  }

  /**
   * 获取用户所有本地文件变更
   */
  public async getUserChangedFiles(dirPath: string): Promise<FileChange[]> {
    try {
      if (!(await this.isGitRepo(dirPath))) {
        this.logger.warn(`Directory ${dirPath} is not a git repository`);
        return [];
      }

      const ignoreFilter = ignore().add(getKwaipilotIgnoreFiles(dirPath)).add(IG_PATERNS);

      // 并行获取所有类型的文件变更
      const [stagedChanges, unstagedChanges, untrackedFiles] = await Promise.all([
        this.getFileChangesByType(dirPath, 'diff --cached --name-status', ignoreFilter),
        this.getFileChangesByType(dirPath, 'diff --name-status', ignoreFilter),
        this.getFileChangesByType(dirPath, 'ls-files --others --exclude-standard', ignoreFilter, 'create')
      ]);

      // 合并所有变更并去重
      const allChanges = [...stagedChanges, ...unstagedChanges, ...untrackedFiles];
      const result = this.deduplicateChanges(allChanges);

      this.logger.info(`Found ${result.length} changed files in ${dirPath}`);
      return result;
    } catch (error) {
      this.logger.error(`Error getting user changed files: ${error}`);
      throw error;
    }
  }

  /**
   * 获取两个提交之间的变更文件
   */
  public async getChangedFilesBetweenCommits(
    dirPath: string,
    newCommitId: string,
    oldCommitId: string
  ): Promise<FileChange[]> {
    try {
      const command = `diff --name-status ${oldCommitId} ${newCommitId}`;
      const output = await this.executeGitCommand(dirPath, command);
      const ignoreFilter = ignore().add(getKwaipilotIgnoreFiles(dirPath)).add(IG_PATERNS);

      return this.parseGitStatusOutput(output, ignoreFilter);
    } catch (error) {
      this.logger.error(`Error getting changed files between commits: ${error}`);
      throw error;
    }
  }

  /**
   * 判断目录是否为 git 仓库
   */
  public async isGitRepo(dirPath: string): Promise<boolean> {
    try {
      // 优先检查 .git 目录（更快）
      const gitDir = path.join(dirPath, '.git');
      try {
        const stat = await fs.promises.stat(gitDir);
        if (stat.isDirectory() || stat.isFile()) {
          // .git 可能是文件（worktree 情况）
          return true;
        }
      } catch {
        // 如果 .git 不存在，继续用 git 命令检查
      }

      // 使用 git 命令进行最终确认
      const output = await this.executeGitCommand(dirPath, 'rev-parse --is-inside-work-tree');
      return output === 'true';
    } catch {
      return false;
    }
  }

  /**
   * 获取当前分支名
   */
  public async getCurrentBranch(dirPath: string): Promise<string | null> {
    try {
      const output = await this.executeGitCommand(dirPath, 'branch --show-current');
      return output || null;
    } catch (error) {
      this.logger.warn(`Error getting current branch: ${error}`);
      return null;
    }
  }

  /**
   * 获取最新提交的 hash
   */
  public async getLatestCommitHash(dirPath: string): Promise<string | null> {
    try {
      const output = await this.executeGitCommand(dirPath, 'rev-parse HEAD');
      return output || null;
    } catch (error) {
      this.logger.warn(`Error getting latest commit hash: ${error}`);
      return null;
    }
  }

  /**
   * 获取 git 仓库根目录
   */
  public async getGitRootPath(dirPath: string): Promise<string | null> {
    try {
      const output = await this.executeGitCommand(dirPath, 'rev-parse --show-toplevel');
      return output || null;
    } catch (error) {
      this.logger.warn(`Error getting git root path: ${error}`);
      return null;
    }
  }

  /**
   * 获取文件相对于 git 仓库根目录的路径
   */
  public async getFileGitRelativePath(cwd: string, filePath: string): Promise<string | null> {
    try {
      const gitRoot = await this.getGitRootPath(cwd);
      if (!gitRoot) {
        return null;
      }

      // '/Users/<USER>/learn/duet-test/src/test' 'src/test/README.md'

      // 获取文件的绝对路径
      const absoluteFilePath = path.resolve(cwd, filePath);
      const absoluteGitRoot = path.resolve(gitRoot);

      // 计算文件相对于 git 根目录的路径
      const relativePath = path.relative(absoluteGitRoot, absoluteFilePath);

      return relativePath;
    } catch (error) {
      this.logger.warn(`Error getting file git relative path: ${error}`);
      return null;
    }
  }
}