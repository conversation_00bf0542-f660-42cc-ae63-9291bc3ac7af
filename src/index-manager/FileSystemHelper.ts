import fs from 'fs/promises';
import fsSync from 'fs';
import crypto from 'crypto';
import path from 'path';
import mime from 'mime-types';
import ignore from 'ignore';
import { Logger } from '@/util/log';
import { getKwaipilotIgnoreFiles } from '@/util/file';
import { IG_PATERNS } from './utils';

export class FileSystemHelper {
  private readonly logger: Logger = new Logger('FileSystemHelper');
  private readonly textExtensions = new Set([
    // 编程语言
    '.mts',
    '.ts',
    '.js',
    '.mjs',
    '.jsx',
    '.tsx',
    '.py',
    '.rb',
    '.php',
    '.java',
    '.c',
    '.cpp',
    '.h',
    '.hpp',
    '.cs',
    '.go',
    '.rs',
    '.swift',
    '.kt',
    '.scala',
    '.r',
    '.m',
    '.lua',
    '.pl',
    '.sh',
    '.bash',
    '.ps1',
    '.psm1',

    // Web 相关
    '.html',
    '.htm',
    '.css',
    '.scss',
    '.sass',
    '.less',
    '.vue',
    '.svelte',
    '.wxml',
    '.wxss',

    // 配置文件
    '.json',
    '.xml',
    '.yaml',
    '.yml',
    '.toml',
    '.ini',
    '.conf',
    '.config',
    '.properties',
    '.prop',
    '.env',
    '.gradle',
    '.pom',
    '.maven',
    '.dockerfile',
    '.dockerignore',
    '.gitignore',
    '.npmrc',
    '.nvmrc',

    // 标记语言
    '.md',
    '.markdown',
    '.rst',
    '.tex',
    '.latex',
    '.wiki',

    // 数据文件
    '.sql',
    '.graphql',
    '.gql',

    // 其他文本文件
    '.txt',
    '.log',
    '.csv'
  ]);

  private readonly textMimeTypes = [
    'application/json',
    'application/javascript',
    'application/typescript',
    'application/xml',
    'application/x-yaml',
    'application/x-sh',
    'application/x-httpd-php',
    'application/x-python',
    'application/x-ruby',
    'application/x-perl'
  ];

  /**
   * 计算文件的 MD5 哈希值
   */
  public async getFileHash(fullPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!fsSync.existsSync(fullPath)) {
        resolve('');
        return;
      }
      const hash = crypto.createHash('md5');
      const stream = fsSync.createReadStream(fullPath);

      stream.on('data', (data: Buffer) => hash.update(data));
      stream.on('end', () => resolve(hash.digest('hex')));
      stream.on('error', (error: Error) => reject(error));
    });
  }

  /**
   * 检查文件是否为文本文件
   */
  public async isTextFile(filePath: string): Promise<boolean> {
    try {
      const ext = path.extname(filePath).toLowerCase();

      if (this.textExtensions.has(ext)) {
        return true;
      }

      const mimeType = mime.lookup(filePath);
      if (!mimeType) return false;

      if (mimeType.startsWith('text/')) return true;

      return this.textMimeTypes.includes(mimeType);
    } catch (error) {
      this.logger.error(`Error checking file type: ${filePath}`, error);
      return true; // 发生错误时默认当做文本文件
    }
  }

  /**
   * 检查文件大小
   */
  public async getFileSize(filePath: string): Promise<number> {
    if (!fsSync.existsSync(filePath)) {
      return 0;
    }
    const stats = await fs.stat(filePath);
    return stats.size;
  }

  /**
   * 获取指定目录下的所有文件（应用 ignore 过滤）
   */
  public async getAllFiles(dirPath: string, maxFileCount: number = 1000): Promise<string[]> {
    const files: string[] = [];

    // 创建 ignore 过滤器
    const ignoreFilter = ignore().add(getKwaipilotIgnoreFiles(dirPath)).add(IG_PATERNS);

    const traverseDirectory = async (
      currentPath: string,
      maxFileCount: number,
      relativePath: string = ''
    ): Promise<void> => {
      try {
        if (files.length > maxFileCount) {
          return;
        }
        const entries = await fs.readdir(currentPath, { withFileTypes: true });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);
          const relativeFilePath = relativePath ? path.join(relativePath, entry.name) : entry.name;

          // 使用 ignore 过滤器检查是否应该忽略此文件/目录
          if (ignoreFilter.ignores(relativeFilePath)) {
            continue;
          }

          if (entry.isDirectory()) {
            // 递归遍历子目录
            await traverseDirectory(fullPath, maxFileCount, relativeFilePath);
          } else {
            // 添加文件到结果列表
            files.push(relativeFilePath);
          }
        }
      } catch (error) {
        this.logger.error(`Error reading directory ${currentPath}:`, error);
      }
    };

    await traverseDirectory(dirPath, maxFileCount);
    return files;
  }
}
