import { Api } from '@/http';
import { GlobalConfig } from '@/util/global';

export interface ModelConfig {
  type: string;
  name: string;
  desc: string;
  icon: string;
  supportImage: boolean;
  supportToolUse: boolean;
  supportThink: boolean;
  maxInputTokens: number;
}

/**
 * 从API获取模型配置
 * @returns 返回从API获取的模型配置
 */
export async function fetchModelConfig(data: {
  config?: ModelConfig;
  username: string;
  platform: string;
}): Promise<{ model: string; maxTokens: number, supportToolUse: boolean }> {
  const httpClient = new Api();
  // 调用API获取模型配置

  const { config, username, platform } = data

  if (config) {
    return { model: config.type, maxTokens: config.maxInputTokens, supportToolUse: config.supportToolUse }
  }

  const response = await httpClient.get<ModelConfig[]>(
    `/eapi/kwaipilot/plugin/agent/models`,
    {},
    {
      headers: {
        'kwaipilot-username': username,
        'kwaipilot-platform': platform,
      },
      signal: AbortSignal.timeout(3000)
    }
  );

  const modelList = response.data

  const model = modelList[0]

  return { model: model.type, maxTokens: model.maxInputTokens, supportToolUse: model.supportToolUse };
}

/**
 * 从kconf获取白名单配置
 * @returns 返回从API获取的模型配置
 */
export async function fetchGrayConfig(data: { grayKey: string; username: string }): Promise<boolean> {
  const { grayKey, username } = data;
  const httpClient = new Api();
  // 调用API获取模型配置
  const response = await httpClient.get<
    { full: boolean; name: string; whitelist: string[]; supportPlatform: string[]; fullPlatform: string[] }[]
  >(`eapi/kwaipilot/plugin/v2/config?key=kwaipilot.platform.user_gray_config`);
  // 是否包含当前用户
  const targetConfig = response.data.find((item) => item.name === grayKey);
  if (!targetConfig) {
    return false;
  }
  const platform = GlobalConfig.getConfig().getPlatform();
  if (targetConfig.fullPlatform?.includes(platform)) {
    return true;
  }

  if (targetConfig?.whitelist.includes(username) && targetConfig?.supportPlatform.includes(platform)) {
    return true;
  }
  return false;
}

export async function checkEnableRepoIndex(): Promise<boolean> {
  try {
    const httpClient = new Api();
    let repoUrl = GlobalConfig.getConfig().getRepoInfo()?.git_url;
    if (!repoUrl) {
      repoUrl = GlobalConfig.getConfig().getRepoPath();
    }
    const data = await httpClient.getRepoIndexFileCount(repoUrl);
    return data?.count > 0;
  } catch (e) {
    return true;
  }
}