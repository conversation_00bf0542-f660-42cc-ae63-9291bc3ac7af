/**
 * 工具开关配置类型定义
 */

import { ToolName } from '../services/toolHandlers';

/**
 * 工具开关配置
 */
export interface ToolSwitchConfig {
  /** 工具名称 */
  name: ToolName;
  /** 是否启用 */
  enabled: boolean;
  /** 工具显示名称 */
  displayName: string;
  /** 工具描述 */
  description: string;
  /** 工具分类 */
  category: ToolCategory;
  /** 是否为核心工具（核心工具不可禁用） */
  isCore?: boolean;
}

/**
 * 工具分类
 */
export enum ToolCategory {
  /** 文件操作 */
  FILE_OPERATIONS = 'file_operations',
  /** 搜索 */
  SEARCH = 'search',
  /** 命令执行 */
  COMMAND = 'command',
  /** 交互 */
  INTERACTION = 'interaction',
  /** 外部工具 */
  EXTERNAL = 'external',
  /** 外部工具 */
  MEMORY = 'memory'
}

/**
 * 工具开关状态
 */
export interface ToolSwitchState {
  /** 工具开关配置映射（未配置的工具默认为true） */
  switches: Partial<Record<ToolName, boolean>>;
  /** 最后更新时间 */
  lastUpdated?: number;
}

/**
 * 默认工具开关配置
 */
export const DEFAULT_TOOL_SWITCHES: ToolSwitchConfig[] = [
  {
    name: 'read_file',
    enabled: true,
    displayName: '读取文件',
    description: '读取文件内容',
    category: ToolCategory.FILE_OPERATIONS,
    isCore: true
  },
  {
    name: 'edit_file',
    enabled: true,
    displayName: '编辑文件',
    description: '编辑文件内容',
    category: ToolCategory.FILE_OPERATIONS
  },
  {
    name: 'write_to_file',
    enabled: true,
    displayName: '写入文件',
    description: '写入新文件',
    category: ToolCategory.FILE_OPERATIONS
  },
  {
    name: 'replace_in_file',
    enabled: true,
    displayName: '搜索替换',
    description: '在文件中搜索并替换文本',
    category: ToolCategory.FILE_OPERATIONS
  },
  {
    name: 'list_files',
    enabled: true,
    displayName: '列出文件',
    description: '列出目录中的文件',
    category: ToolCategory.FILE_OPERATIONS
  },
  {
    name: 'codebase_search',
    enabled: true,
    displayName: '代码库搜索',
    description: '语义搜索代码库',
    category: ToolCategory.SEARCH
  },
  {
    name: 'grep_search',
    enabled: true,
    displayName: '正则搜索',
    description: '使用正则表达式搜索文件',
    category: ToolCategory.SEARCH
  },
  {
    name: 'search_web',
    enabled: true,
    displayName: 'google搜索',
    description: '使用google搜索引擎进行内容',
    category: ToolCategory.SEARCH
  },
  {
    name: 'fetch_web',
    enabled: true,
    displayName: '网页抓取',
    description: '获取网页详细内容',
    category: ToolCategory.SEARCH
  },
  // {
  //   name: 'search_memory',
  //   enabled: true,
  //   displayName: '记忆搜索',
  //   description: '搜索存储的记忆信息',
  //   category: ToolCategory.SEARCH
  // },
  {
    name: 'get_memory',
    enabled: true,
    displayName: '获取记忆详情',
    description: '根据ID获取记忆的详细信息',
    category: ToolCategory.SEARCH
  },
  {
    name: 'search_spec',
    enabled: true,
    displayName: '搜索规格文档',
    description: '搜索AI生成代码的详细规划和规划依据',
    category: ToolCategory.SEARCH
  },
  // {
  //   name: 'save_memory',
  //   enabled: true,
  //   displayName: '保存记忆',
  //   description: '保存重要信息、用户偏好和行为模式到记忆系统',
  //   category: ToolCategory.MEMORY
  // },

  {
    name: 'execute_command',
    enabled: true,
    displayName: '执行命令',
    description: '执行系统命令',
    category: ToolCategory.COMMAND
  },
  {
    name: 'use_mcp_tool',
    enabled: true,
    displayName: 'MCP工具',
    description: '使用MCP服务器提供的工具',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'ask_followup_question',
    enabled: true,
    displayName: '询问问题',
    description: '向用户询问跟进问题',
    category: ToolCategory.INTERACTION
  },
  {
    name: 'command_status_check',
    enabled: true,
    displayName: '命令状态检查',
    description: '检查命令行工具的执行状态',
    category: ToolCategory.COMMAND
  },
  {
    name: 'render_research_plan',
    enabled: true,
    displayName: '渲染研究计划',
    description: '渲染研究计划',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'update_phase',
    enabled: true,
    displayName: '更新阶段',
    description: '更新阶段',
    category: ToolCategory.INTERACTION
  },
  {
    name: 'research',
    enabled: true,
    displayName: '研究工具',
    description: '研究工具',
    category: ToolCategory.INTERACTION
  },
  {
    name: 'write_todo',
    enabled: true,
    displayName: 'TODO 管理',
    description: '管理 TODO 列表',
    category: ToolCategory.INTERACTION
  },
  {
    name: 'read_todo',
    enabled: true,
    displayName: '读取 TODO 列表',
    description: '读取 TODO 列表',
    category: ToolCategory.INTERACTION
  },
  {
    name: 'project_preview',
    enabled: true,
    displayName: '项目预览',
    description: '预览项目',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'parse_figma',
    enabled: true,
    displayName: '解析 Figma',
    description: '解析 Figma 设计文件，获取节点数据和图片信息',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'get_figma_preview_image',
    enabled: true,
    displayName: '获取 Figma 预览图',
    description: '获取 Figma 设计文件的预览图',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'browser_action',
    enabled: true,
    displayName: '浏览器操作',
    description: '浏览器操作',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'test_case',
    enabled: true,
    displayName: '测试用例',
    description: '测试用例',
    category: ToolCategory.EXTERNAL
  },
  {
    name: 'code_formatter',
    enabled: true,
    displayName: '代码格式化',
    description: '格式化代码文件',
    category: ToolCategory.FILE_OPERATIONS
  }
];

/**
 * 检查工具是否启用
 * @param toolName 工具名称
 * @param enabledTools 启用的工具列表，如果为空或未定义则所有工具都启用
 * @returns 工具是否启用
 */
export function isToolEnabled(toolName: string, enabledTools?: string[]): boolean {
  if (!enabledTools || enabledTools.length === 0) {
    return true; // 如果没有限制，所有工具都启用
  }
  return enabledTools.includes(toolName);
}

/**
 * 根据工具开关状态获取启用的工具名称列表
 * @param toolSwitchState 工具开关状态
 * @returns 启用的工具名称数组
 */
export function getEnabledToolNames(toolSwitchState?: ToolSwitchState): string[] {
  if (!toolSwitchState || !toolSwitchState.switches) {
    // 如果没有工具开关状态，返回所有工具的名称
    return DEFAULT_TOOL_SWITCHES.map((config) => config.name);
  }

  // 过滤出启用的工具
  return DEFAULT_TOOL_SWITCHES.filter((config) => {
    const isEnabled = toolSwitchState.switches[config.name];
    // 如果未定义，默认为启用
    return isEnabled !== false;
  }).map((config) => config.name);
}
