import { MessageParam, MessageParamVersion1 } from '../types/type';

export function isAllMessagesVersion1(messages: MessageParam[]): messages is MessageParamVersion1[] {
  const messagesVersion1: MessageParamVersion1[] = [];
  messages.forEach((m) => {
    if (m.version === 1) {
      messagesVersion1.push(m);
    }
  });

  return messagesVersion1.length === messages.length;
}

export function removeThinkingText(content: string): string {
  return content.replace(/<thinking>[\s\S]*?<\/thinking>\s*/g, '');
}


// 检查 历史消息中是否有调用 writeTodo工具
export const hasTodoWriteMessage = (messages: MessageParam[]): boolean => {

  return !!messages.filter(
    (mes) =>
      Array.isArray(mes.content) &&
      !!mes.content.filter((e) => (e as unknown as { toolName: string }).toolName === 'write_todo').length
  ).length;
}

// export const pickPhaseMessage = (messages: MessageParam[]): MessageParam[] => {
//   return messages.filter(
//     (mes) =>
//       Array.isArray(mes.content) &&
//       !!mes.content.filter((e) => (e as unknown as { toolName: string }).toolName === 'update_phase').length
//   );
// };