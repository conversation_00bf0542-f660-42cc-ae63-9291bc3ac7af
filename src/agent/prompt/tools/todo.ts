import { Chat } from '@/agent/types/type';
import todoWriteParams from './todoWriteParameters.json';

/**
 * edit_file 工具的 function schema
 * @param cwd 当前工作目录
 * @returns edit_file 工具的 function schema
 */
export const getTodoWriteFunctionSchema = (): Chat.Function => ({
  name: 'write_todo',
  description: `Use this tool to create and manage a structured task list for your current coding session. This helps you track progress, organize complex tasks, and demonstrate thoroughness to the user.
It also helps the user understand the progress of the task and overall progress of their requests.

## How to use this tool

This tool provides 2 optional parameters, which you MUST choose one and ONLY one of them.

- todos: The todo list to write. use this to create TODO list.
- completedTodoId: The id of the current completed TODO item. use this to track the progress of the task.

### 1. Create Task List

After the user approves the Design, create an actionable implementation plan with a checklist of coding tasks based on the requirements and design. The tasks document should be based on the design document, so ensure it exists first.

For creating task list, you can use the parameter \`todos\` to achieve this.

**Constraints:**

- The model MUST use the following specific instructions when creating the implementation plan:
\`\`\`
Convert the feature design into a series of prompts for a code-generation LLM that will implement each step in a test-driven manner. Prioritize best practices, incremental progress, and early testing, ensuring no big jumps in complexity at any stage. Make sure that each prompt builds on the previous prompts, and ends with wiring things together. There should be no hanging or orphaned code that isn't integrated into a previous step. Focus ONLY on tasks that involve writing, modifying, or testing code.
\`\`\`
- Top-level items (like epics) should be used only when needed
- Simple structure is preferred
- The model MUST ensure each task item includes:
  - A clear objective as the task description that involves writing, modifying, or testing code
  - Additional information as \`description\` under the task
  - Specific references to requirements from the requirements document (referencing granular sub-requirements, not just user stories)
  - A unique id for each task
- The model MUST ensure that the implementation plan is a series of discrete, manageable coding steps
- The model MUST ensure each task references specific requirements from the requirement document
- The model MUST NOT include excessive implementation details that are already covered in the design document
- The model MUST assume that all context documents (feature requirements, design) will be available during implementation
- The model MUST ensure each step builds incrementally on previous steps
- The model SHOULD prioritize test-driven development where appropriate
- The model MUST ensure the plan covers all aspects of the design that can be implemented through code
- The model SHOULD sequence steps to validate core functionality early through code
- The model MUST ensure that all requirements are covered by the implementation tasks
- The model MUST offer to return to previous steps (requirements or design) if gaps are identified during implementation planning
- The model MUST ONLY include tasks that can be performed by a coding agent (writing code, creating tests, etc.)
- The model MUST NOT include tasks related to user testing, deployment, performance metrics gathering, or other non-coding activities
- The model MUST focus on code implementation tasks that can be executed within the development environment
- The model MUST ensure each task is actionable by a coding agent by following these guidelines:
  - Tasks should involve writing, modifying, or testing specific code components
  - Tasks should specify what files or components need to be created or modified
  - Tasks should be concrete enough that a coding agent can execute them without additional clarification
  - Tasks should focus on implementation details rather than high-level concepts
  - Tasks should be scoped to specific coding activities (e.g., "Implement X function" rather than "Support X feature")
- The model MUST explicitly avoid including the following types of non-coding tasks in the implementation plan:
  - User acceptance testing or user feedback gathering
  - Deployment to production or staging environments
  - Performance metrics gathering or analysis
  - Running the application to test end to end flows. We can however write automated tests to test the end to end from a user perspective.
  - User training or documentation creation
  - Business process changes or organizational changes
  - Marketing or communication activities
  - Any task that cannot be completed through writing, modifying, or testing code
- After updating the tasks document, the model MUST ask the user "Do the tasks look good?" using the 'userInput' tool.
- The model MUST make modifications to the tasks document if the user requests changes or does not explicitly approve.
- The model MUST write the entire tasks, since each too call will override the previous tasks document.
- The model MUST ask for explicit approval after every iteration of edits to the tasks document.
- The model MUST NOT consider the workflow complete until receiving clear approval (such as "yes", "approved", "looks good", etc.).
- The model MUST continue the feedback-revision cycle until explicit approval is received.
- The model MUST stop once the task document has been approved.
- The model MUST NOT rewrite the tasks once the ACTION phase starts(only status updates are accepted).


### 2. Track Task Progress


Use the \`completedTodoId\` parameter to update task progress status. You should call this tool immediately whenever a task is completed to update its status.

**Constraints:**

- The model SHOULD ONLY update task status in ACTION phase.
- The model MUST mark the task as "completed" IMMEDIATELY AFTER completing the task. DO NOT batch tasks.
- The model MUST make sure the current task is completed before marking it as completed.
- The model MUST start handling next task IMMEDIATELY after completing the current task.
- The model MUST doing tasks in the order listed. skipping tasks will cause an error.


**Status Transitions**
- Change task status from "in_progress" to "completed"
- After marking a task as "completed", the next task will be marked as "in_progress" automatically.

**Progress Reporting**
- Briefly describe what was accomplished when updating task status
- Highlight any issues or special circumstances encountered during task completion

## Task States and Management

1. **Task States**: Use these states to track progress:
   - pending: Task not yet started
   - in_progress: Currently working on (limit to ONE task at a time)
   - completed: Task finished successfully

2. **Task Breakdown**:
   - Create specific, actionable items
   - Break complex tasks into smaller, manageable steps
   - Use clear, descriptive task names

`,
  strict: true,
  parameters: todoWriteParams as any
});

/**
 * readTodo
 * @param cwd 当前工作目录
 * @returns edit_file 工具的 function schema
 */
export const getTodoReadFunctionSchema = (): Chat.Function => ({
  name: 'read_todo',
  description: `Use this tool to retrieve the current todo list and its status.

**Constraints:**
- The model MUST use this tool before using \`write_todo\` to update todo status.

**Example Result**

\`\`\`markdown
- completed(id:setup-enhanced-logger-core)创建增强日志器核心模块
- in_progress(id:implement-context-collector)实现上下文收集器
- pending(id:implement-context-collector)完成文件文档分析
\`\`\`

`,
  strict: true,
  parameters: {
    type: 'object',
    properties: {}
  }
});
