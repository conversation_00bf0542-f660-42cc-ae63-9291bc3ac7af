import { ToolArgs } from "./types"

export function getWriteToFileDescription(args: ToolArgs): string {
	return `## write_to_file
Description: Save a new file. Use this tool to write new files with the attached content. Generate \`instructions_reminder\` first to remind yourself to limit the file content to at most 300 lines. It CANNOT modify existing files. Do NOT use this tool to edit an existing file by overwriting it entirely. Use the replace_in_file tool to edit existing files instead.
Parameters:
- path: (required) The path of the file to write to (relative to the current workspace directory ${args.cwd})
- content: (required) The content to write to the file.
- instructions_reminder: (required) Should be exactly this string: 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE replace_in_file TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.
Usage:
<write_to_file>
<path>File path here</path>
<content>
Your file content here
</content>
<instructions_reminder>
LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE replace_in_file TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.
</instructions_reminder>
</write_to_file>
`;
}

export function getWriteToFileExample(): string {
  return `
Example: Requesting to write to frontend-config.json
<write_to_file>
<path>frontend-config.json</path>
<content>
{
  "apiEndpoint": "https://api.example.com",
  "theme": {
    "primaryColor": "#007bff",
    "secondaryColor": "#6c757d",
    "fontFamily": "Arial, sans-serif"
  },
  "features": {
    "darkMode": true,
    "notifications": true,
    "analytics": false
  },
  "version": "1.0.0"
}
</content>
<instructions_reminder>
LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE replace_in_file TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.
</instructions_reminder>
</write_to_file>`;
}
