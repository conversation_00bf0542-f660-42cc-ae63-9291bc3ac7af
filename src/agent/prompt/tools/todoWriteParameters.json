{"type": "object", "$schema": "http://json-schema.org/draft-07/schema#", "properties": {"todos": {"description": "The todo list to write", "items": {"additionalProperties": false, "properties": {"content": {"minLength": 1, "type": "string"}, "description": {"type": "string", "description": "detailed information"}, "id": {"type": "string", "description": "Unique identifier for the TODO item"}}, "required": ["content", "id"], "type": "object"}}, "completedTodoId": {"type": "string", "description": "The id of the current completed TODO item"}}, "required": []}