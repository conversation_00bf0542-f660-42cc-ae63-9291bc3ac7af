import { Chat } from '@/agent/types/type.d';
import { ToolArgs } from './types';

/**
 * 将工具的 function schema 转换为 prompt 描述格式
 * @param functionSchema 工具的 function schema
 * @param args 工具参数（如 cwd 等）
 * @returns 格式化的工具描述字符串
 */
export function convertSchemaToPromptDescription(functionSchema: Chat.Function, args?: ToolArgs): string {
  const { name, description, parameters } = functionSchema;
  
  // 生成参数描述
  const parametersList = generateParametersDescription(parameters);
  
  // 生成使用示例
  const usageExample = generateUsageExample(name, parameters);
  
  return `## ${name}
Description: ${description}
Parameters:
${parametersList}
Usage:
${usageExample}`;
}

/**
 * 生成参数描述列表
 * @param parameters function schema 中的 parameters 对象
 * @returns 格式化的参数描述字符串
 */
function generateParametersDescription(parameters: any): string {
  if (!parameters || !parameters.properties) {
    return '(No parameters)';
  }

  const { properties, required = [] } = parameters;
  const parameterLines: string[] = [];

  Object.entries(properties).forEach(([paramName, paramSchema]: [string, any]) => {
    const isRequired = required.includes(paramName);
    const requiredText = isRequired ? '(required)' : '(optional)';
    const description = paramSchema.description || 'No description provided';
    
    parameterLines.push(`- ${paramName}: ${requiredText} ${description}`);
  });

  return parameterLines.join('\n');
}

/**
 * 生成使用示例
 * @param toolName 工具名称
 * @param parameters function schema 中的 parameters 对象
 * @returns 格式化的使用示例字符串
 */
function generateUsageExample(toolName: string, parameters: any): string {
  if (!parameters || !parameters.properties) {
    return `<${toolName}>
</${toolName}>`;
  }

  const { properties, required = [] } = parameters;
  const exampleParams: string[] = [];

  Object.entries(properties).forEach(([paramName, paramSchema]: [string, any]) => {
    const isRequired = required.includes(paramName);
    if (isRequired) {
      const placeholder = generatePlaceholder(paramName, paramSchema);
      exampleParams.push(`<${paramName}>${placeholder}</${paramName}>`);
    }
  });

  // 添加可选参数的注释
  const optionalParams = Object.keys(properties).filter(name => !required.includes(name));
  if (optionalParams.length > 0) {
    exampleParams.push(`<!-- Optional parameters: ${optionalParams.join(', ')} -->`);
  }

  return `<${toolName}>
${exampleParams.join('\n')}
</${toolName}>`;
}

/**
 * 根据参数类型生成占位符
 * @param paramName 参数名称
 * @param paramSchema 参数 schema
 * @returns 占位符字符串
 */
function generatePlaceholder(paramName: string, paramSchema: any): string {
  const { type, description } = paramSchema;
  
  switch (type) {
    case 'string':
      return getStringPlaceholder(paramName, description);
    case 'number':
      return getNumberPlaceholder(paramName);
    case 'boolean':
      return 'true or false';
    case 'array':
      return getArrayPlaceholder(paramName, paramSchema);
    case 'object':
      return getObjectPlaceholder(paramName, paramSchema);
    default:
      return `${paramName} value here`;
  }
}

/**
 * 生成字符串类型的占位符
 */
function getStringPlaceholder(paramName: string, description?: string): string {
  // 根据参数名称和描述推断合适的占位符
  if (paramName.includes('path') || paramName.includes('file')) {
    return 'File path here';
  }
  if (paramName.includes('command')) {
    return 'Your command here';
  }
  if (paramName.includes('query') || paramName.includes('search')) {
    return 'Your search query here';
  }
  if (paramName.includes('url')) {
    return 'URL here';
  }
  if (paramName.includes('content') || paramName.includes('text')) {
    return '\nYour content here\n';
  }
  if (paramName.includes('pattern')) {
    return 'pattern here';
  }
  
  return `${paramName} here`;
}

/**
 * 生成数字类型的占位符
 */
function getNumberPlaceholder(paramName: string): string {
  if (paramName.includes('line')) {
    return '10';
  }
  if (paramName.includes('duration') || paramName.includes('time')) {
    return '5';
  }
  return '0';
}

/**
 * 生成数组类型的占位符
 */
function getArrayPlaceholder(paramName: string, paramSchema: any): string {
  const itemType = paramSchema.items?.type || 'string';
  if (itemType === 'string') {
    return `${paramName} patterns here`;
  }
  return `${paramName} items here`;
}

/**
 * 生成对象类型的占位符
 */
function getObjectPlaceholder(paramName: string, paramSchema: any): string {
  if (paramSchema.properties) {
    const exampleObj: any = {};
    Object.entries(paramSchema.properties).forEach(([key, prop]: [string, any]) => {
      if (prop.type === 'string') {
        exampleObj[key] = `${key}_value`;
      } else if (prop.type === 'number') {
        exampleObj[key] = 0;
      } else if (prop.type === 'boolean') {
        exampleObj[key] = true;
      }
    });
    return `\n${JSON.stringify(exampleObj, null, 2)}\n`;
  }
  return `\n{\n  "${paramName}": "value"\n}\n`;
}

/**
 * 批量转换多个工具 schema 为 prompt 描述
 * @param functionSchemas 工具 schema 数组
 * @param args 工具参数
 * @returns 格式化的工具描述字符串
 */
export function convertMultipleSchemasToPromptDescriptions(
  functionSchemas: Chat.Function[], 
  args?: ToolArgs
): string {
  return functionSchemas
    .map(schema => convertSchemaToPromptDescription(schema, args))
    .join('\n\n');
}

/**
 * 从工具 schema 中提取工具使用示例
 * @param functionSchemas 工具 schema 数组
 * @returns 格式化的工具使用示例字符串
 */
export function generateToolUseExamples(functionSchemas: Chat.Function[]): string {
  const examples = functionSchemas.map((schema, index) => {
    const usageExample = generateUsageExample(schema.name, schema.parameters);
    return `## Example ${index + 1}: Using ${schema.name}

${usageExample}`;
  });

  return examples.join('\n\n');
}