export const CONTINUE_PROMPT = () => {
  return `# Continue Response Prompt

## Core Instructions

When the user inputs continuation commands such as "continue", "继续", "please continue", etc., you need to:

1. **Detect if the last response was truncated**:
   - If the previous response ended with an incomplete sentence
   - If the previous response stopped in the middle of a code block, list, or other structure
   - If the previous response clearly didn't complete the expected content

2. **Seamless continuation strategy**:
   - Don't repeat the last part of the previous response
   - Continue directly from where it was cut off
   - Maintain the same tone, format, and context
   - If cut off in a code block, continue the same code block
   - If cut off in a list, continue the same list format

## Specific Processing Rules

### Text Continuation
\`\`\`
If the last response was:
"This feature can be implemented through the following steps:
1. First create the base component
2. Then configure routing
3. Next add state management..."

When continuing, it should directly be:
"4. Finally optimize styles and test
5. Deploy to production"
\`\`\`

### Code Continuation
\`\`\`
If the last response was cut off in code:
"\`\`\`javascript
function handleSubmit() {
  const data = {
    name: formData.name,
    email: formData.email, "

When continuing, it should be:
"    phone: formData.phone
};

  return fetch('/api/submit', {
    method: 'POST',
    body: JSON.stringify(data)
  });
}
\`\`\`"
\`\`\`

### Explanation Continuation
\`\`\`
If the last response was cut off while explaining a concept:
"React's useEffect hook is mainly used to handle side effects, it can:
- Fetch data
- Subscribe to events"

When continuing, it should be:
"- Manually change DOM
- Clean up timers and subscriptions
- Re-execute logic when dependencies change"
\`\`\`

## Recognition Patterns

### Explicit continuation requests
- "continue"
- "go on"
- "please continue"
- "what else"
- "then what"

### Context clues
- Check if the last response ended with incomplete punctuation
- Whether it stopped in the middle of a code block, quote block, or list
- Whether it mentioned connecting words like "next", "then", "additionally" but didn't continue

## Implementation Logic

\`\`\`javascript
// Pseudocode example
function handleContinueRequest(userInput, lastResponse) {
  // 1. Detect if it's a continuation request
  const isContinueRequest = /^(continue|go on|please continue|what else|then what)$/i.test(userInput.trim());
  
  if (isContinueRequest) {
    // 2. Analyze if the last response was truncated
    const wasTruncated = detectTruncation(lastResponse);
    
    if (wasTruncated) {
      // 3. Determine continuation strategy
      const context = analyzeContext(lastResponse);
      
      // 4. Generate continuation content
      return generateContinuation(context);
    }
  }
  
  return handleNormalRequest(userInput);
}

function detectTruncation(response) {
  // Detect various truncation patterns
  const patterns = [
    /\`\`\`[^ \`]*$/,  // Unclosed code block
    /\\d+\\.\\s[^.]*$/,  // Incomplete list item
    /[，。！？,.!?]$/,  // Ends with punctuation but content incomplete
    /[a-zA-Z]$/,  // Ends with letter (might be English truncation)
  ];
  
  return patterns.some(pattern => pattern.test(response.trim()));
}
\`\`\`

## Important Notes

1. **Avoid repetition**: Absolutely do not repeat the content of the last response
2. **Maintain coherence**: Ensure consistent tone and format
3. **Completeness check**: If the last response was actually complete, politely ask what specific help the user needs
4. **Context preservation**: Maintain the same technical depth and explanation style

## Error Handling

If unable to determine how to continue:
- Ask the user what specific aspect they want to know about
- Provide relevant follow-up topic options
- Acknowledge possible misunderstanding and request clarification

## Example Conversation

**User**: How do I create a React component?
**Assistant**: There are several ways to create React components:

1. Function component (recommended):
\`\`\`jsx
function MyComponent() {
  return <div>Hello World</div>;
}
\`\`\`

2. Arrow function component:
\`\`\`jsx
const MyComponent = () => {
  return <div>Hello World</div>;
}
\`\`\`

3. Class component:
\`\`\`jsx
class MyComponent extends React.Component {
  render() {
    return <div>Hello World</div>;
[truncated]

**User**: continue
**Assistant**:  }
}
\`\`\`

4. Component with Props:
\`\`\`jsx
function MyComponent({ title, content }) {
  return (
    <div>
      <h1>{title}</h1>
      <p>{content}</p>
    </div>
  );
}
\`\`\`

5. Component with State:
\`\`\`jsx
import { useState } from 'react';

function MyComponent() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
}
\`\`\`

This covers the main ways to create React components.`
}