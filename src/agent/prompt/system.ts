import { McpServer } from '../../mcp/types';
import { CLAUDE_SYSTEM_PROMPT } from './claude';
export * from './common';

/**
 * 根据模型类型选择合适的系统提示词
 * @param model 模型名称
 * @param cwd 当前工作目录
 * @param mcpServers MCP服务器列表
 * @param enableRepoIndex 是否启用代码库索引
 * @param rules 用户规则
 * @param shell 终端类型
 * @param useNewEditTool 是否使用新的编辑工具
 * @param enabledTools 启用的工具列表
 * @returns 适用于指定模型的系统提示词
 */
export const SYSTEM_PROMPT = (
  model: string,
  cwd: string,
  mcpServers: McpServer[],
  enableRepoIndex: boolean = false,
  rules?: string[],
  shell: string = '',
  useNewEditTool: boolean = true,
  isAskMode: boolean = false,
  enabledTools?: string[],
  platform?: string,
  isSupportCodeFormatter?: boolean
) => CLAUDE_SYSTEM_PROMPT({ cwd, mcpServers, enableRepoIndex, rules, shell, useNewEditTool, enabledTools, platform, isAskMode, isSupportCodeFormatter });

export { CLAUDE_SYSTEM_PROMPT };
