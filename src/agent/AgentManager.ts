import {
  <PERSON><PERSON>,
  ImageBlockParam,
  MessageParam,
  TextBlockParamVersion1,
  ToolUse,
  WebviewMessage
} from '@/agent/types/type';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { StateManager } from './services/StateManager';
import { StreamManager } from './services/StreamManager';
import { MessageService } from './services/MessageService';
import { CheckpointService } from './services/CheckpointService';
import { ToolExecutor } from './services/ToolExecutor';
import { ToolHelpers } from './services/ToolHelpers';
import { LoggerManager } from './services/LoggerManager';
import { ToolSwitchState } from './utils/toolSwitches';
import { SYSTEM_PROMPT } from './prompt/system';
import { getAvailableMcpServers } from './tools/mcp-tool';
import { GlobalConfig } from '@/util/global';
import { TokenCalculator } from './context/TokenCalculator';
import { ContextManager } from './context/ContextManager';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { formatResponse } from './prompt/responses';
import { Api } from '@/http';
import { fetchModelConfig, fetchGrayConfig, checkEnableRepoIndex } from './utils/getConfig';
import {
  extractDeltaTextFromAgentChatMessage,
  formatAssistantMessage,
  formatLlmMessages,
  parseAssistantMessage
} from './tools/parse-assistant-message';
import { EventSourceMessage } from '@fortaine/fetch-event-source';
import { isAllMessagesVersion1 } from './utils/message';
import pWaitFor from 'p-wait-for';
import { v4 } from 'uuid';
import cloneDeep from 'clone-deep';
import { ReportGenerateCodeRequest } from './types/tool';
import { getCommonToolsFunctionSchemas } from './prompt/tools/schema';
import { getRulesListForAgent } from './rules';
import { listFiles } from './tools/list-files';
import { ErrorUtils } from '@/util/error';
import i18n from '@/i18n';
import { getWikiList } from '@/project-wiki';
import fs from 'fs/promises'
import path from 'path';
import { parse } from 'best-effort-json-parser';

type UserContent = (TextBlockParamVersion1 | ImageBlockParam)[];
type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export abstract class BaseAgentManager {
  protected stateManager: StateManager;
  protected streamManager: StreamManager;
  protected messageService: MessageService;
  protected checkpointService: CheckpointService;
  protected toolExecutor: ToolExecutor;
  // 日志管理器
  protected loggerManager: LoggerManager;
  // 中止控制器
  protected abortController?: AbortController;
  // 上下文管理器
  public contextManager?: ContextManager;
  protected httpClient = new Api();
  private filePenddingList: string[] = [];
  private modifyMessage: TextBlockParamVersion1 | undefined = undefined;
  private currentRequestParams: any = null;
  private currentRequestHeaders: any = null;

  constructor(
    protected messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    protected cwd: string,
    protected options: {
      model: string;
      maxTokens: number;
      isEnableRepoIndex: boolean;
      isUseNewTool: boolean;
      toolSwitchState?: ToolSwitchState;
      isSupportToolUse: boolean;
      isAskMode?: boolean;
      enableDeltaStreaming: boolean;
    },
    protected sessionInfo?: WebviewMessage<'newTask'>
  ) {
    const sessionId = this.sessionInfo?.reqData.sessionId || '';
    const chatId = this.sessionInfo?.reqData.chatId || '';
    // 初始化日志管理器
    this.loggerManager = new LoggerManager({
      agentMode: this.sessionInfo?.agentMode || 'jam',
      sessionId,
      chatId,
      username: this.sessionInfo?.reqData.username,
      scope: 'assistant-agent'
    });
    const messages = this.sessionInfo?.reqData.messages;
    const userMessages = messages?.filter((m) => m.role === 'user');
    const haveXMLToolUser = userMessages?.some(
      (m) =>
        Array.isArray(m.content) &&
        m.content.some(
          (c) =>
            'category' in c &&
            (c.category === 'tool-response' ||
              c.category === 'tool-title' ||
              c.category === 'tool-feedback' ||
              c.category === 'tool-exception')
        ) &&
        !m.content.some((c) => 'toolId' in c && c.toolId)
    );
    const isSupportToolUse = this.options.isSupportToolUse ? (haveXMLToolUser ? false : true) : false;

    this.loggerManager.agentInfo(`haveXMLToolUser: ${haveXMLToolUser}`);
    this.loggerManager.agentInfo(`传入的supportToolUse: ${this.options.isSupportToolUse}`);
    this.loggerManager.agentInfo(`最终计算的isSupportToolUse: ${isSupportToolUse}`);

    this.stateManager = new StateManager({
      sessionId,
      chatId,
      cwd: this.cwd,
      startTaskTime: Date.now(),
      modelConfig: {
        model: this.options.model,
        maxTokens: this.options.maxTokens,
        isSupportToolUse: isSupportToolUse
      },
      toolSwitchState: this.options.toolSwitchState,
      chatMode: this.options.isAskMode ? 'ask' : 'agent'
    });
    const servers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
    const enabledTools = this.stateManager.getEnabledTools();
    const systemPrompt = SYSTEM_PROMPT(
      this.stateManager.modelConfig.model,
      this.cwd,
      servers,
      this.options.isEnableRepoIndex,
      this.sessionInfo?.rules,
      '',
      this.options.isUseNewTool,
      this.options.isAskMode,
      enabledTools,
      this.sessionInfo?.reqData.deviceInfo?.ide,
      this.sessionInfo?.isSupportCodeFormatter
    );
    this.stateManager.updateState({
      systemPrompt
    });
    // 初始化服务
    this.streamManager = new StreamManager(this.stateManager);
    this.messageService = new MessageService(this.messenger, this.stateManager, this.loggerManager);
    this.checkpointService = new CheckpointService(this.stateManager, this.messageService, this.loggerManager);
    // 初始化工具执行器
    this.toolExecutor = new ToolExecutor(
      this.messenger,
      this.sessionInfo,
      this.cwd,
      this.stateManager,
      this.messageService,
      this.checkpointService,
      this.loggerManager,
      this // 传递 agentManager 实例
    );
    // 初始化上下文管理器
    this.initializeContextManager();
    this.loggerManager.logTaskStart({
      sessionId: this.stateManager.sessionId,
      chatId: this.stateManager.chatId,
      ts: this.stateManager.startTaskTime,
      enableRepoIndex: this.options.isEnableRepoIndex,
      task: this.sessionInfo?.task,
      mcpServers: servers
    });
    this.loggerManager.agentInfo('系统提示词:', this.stateManager.systemPrompt);
  }

  // 公共配置获取逻辑
  protected static async getCommonConfig(sessionInfo?: WebviewMessage<'newTask'>): Promise<{
    model: string;
    maxTokens: number;
    isUseNewTool: boolean;
    isEnableRepoIndex: boolean;
    isSupportToolUse: boolean;
    enableDeltaStreaming: boolean;
  }> {
    let model = 'Kwaipilot Pro';
    let maxTokens = 30000;
    let isUseNewTool = false;
    let isEnableRepoIndex = true;
    let isSupportToolUse = false;
    try {
      const username = GlobalConfig.getConfig().getUsername();
      // 并行执行三个配置请求
      const startTime = Date.now();

      // 创建临时LoggerManager用于静态方法中的日志记录
      const tempLoggerManager = new LoggerManager({
        sessionId: 'init',
        chatId: 'init',
        username,
        scope: 'assistant-agent'
      });

      tempLoggerManager.agentInfo('开始并行执行配置请求...');

      const [modelConfig, isUseNewToolValue, isEnableRepoIndexValue] = await Promise.all([
        fetchModelConfig({
          username,
          platform: sessionInfo?.reqData.deviceInfo?.platform || '',
          config: sessionInfo?.reqData.modelConfig
        }),
        fetchGrayConfig({ grayKey: 'replaceFileTool', username }),
        checkEnableRepoIndex()
      ]);

      const endTime = Date.now();
      const duration = endTime - startTime;
      tempLoggerManager.agentInfo(`并行配置请求完成，耗时: ${duration}ms`);

      // 记录配置请求性能数据
      tempLoggerManager.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-config-requests',
        millis: duration,
        extra4: 'success'
      });

      model = modelConfig.model;
      maxTokens = modelConfig.maxTokens;
      isSupportToolUse = modelConfig.supportToolUse;
      isUseNewTool = isUseNewToolValue;
      isEnableRepoIndex = isEnableRepoIndexValue;
    } catch (e) {
      // 使用默认配置
    }

    return {
      model,
      maxTokens,
      isUseNewTool,
      isEnableRepoIndex,
      isSupportToolUse,
      enableDeltaStreaming: sessionInfo?.enableDeltaStreaming || false
    };
  }

  async beforeStartTask(param: { input: string }): Promise<UserContent> {
    const { input } = param;
    return [
      {
        type: 'text',
        text: `<task>\n${input}\n</task>`,
        category: 'user-input'
      }
    ];
  }

  /**
   * 开始任务
   */
  async startTask(t: number): Promise<void> {
    // 初始化状态
    this.stateManager.updateState({ didCompleteTask: false });
    this.messageService.setLocalMessages(this.sessionInfo?.localMessages || []);
    const apiHistory: MessageParam[] = (this.sessionInfo?.reqData.messages || []).map((m) => ({
      version: 0,
      ...m
    }));
    this.messageService.setApiConversationHistory(apiHistory);
    const task = this.sessionInfo?.task;
    // 带@相关信息的task
    const taskForLlm = this.sessionInfo?.taskForLlm || task || '';
    const images: ImageBlockParam[] = (this.sessionInfo?.images || []).map((item) => ({
      type: 'image',
      source: {
        type: 'url',
        url: item
      },
      category: 'image'
    }));
    // 发送第一条任务消息
    await this.messageService.say('text', taskForLlm, undefined, 'user');
    this.loggerManager.agentDebug('e2e cost:', Date.now() - t);
    this.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-e2e-cost',
      millis: Date.now() - t
    });
    const userContent = await this.beforeStartTask({ input: taskForLlm });
    // 开始任务循环
    await this.handleTaskLoop([...userContent, ...images], true);
  }

  /**
   * 停止任务
   */
  async stop(): Promise<void> {
    this.abortController?.abort();
    const handles = this.stateManager.getState().tasksToBeHandled;
    for (const handle of handles) {
      await handle();
    }
    this.stateManager.updateState({ abort: true });
    await this.beforeCompletedTask();
    // 用户手动停止任务
    this.loggerManager.endGenerationWithMessage('用户手动停止任务');
    this.loggerManager.agentInfo('用户手动停止任务');
    this.loggerManager.reportAgentTask('agent_task_end_stop', {
      sessionId: this.stateManager.sessionId,
      chatId: this.stateManager.chatId,
      ts: Date.now(),
      duration: Date.now() - this.stateManager.getState().startTaskTime,
      modelName: this.stateManager.modelConfig.model
    });
  }

  /**
   * 更新询问响应
   */
  updateAskResponse(response: { askResponse: AskResponse; text?: string }): void {
    this.messageService.updateAskResponse(response);
  }

  /**
   * 初始化上下文管理器 - 可被子类重写
   */
  protected initializeContextManager() {
    // 使用当前模型配置初始化TokenCalculator，传递sessionId和chatId用于性能记录
    const tokenCalculator = new TokenCalculator(
      this.stateManager.modelConfig.model,
      this.stateManager.sessionId,
      this.stateManager.chatId
    );
    this.contextManager = new ContextManager(
      this.stateManager.modelConfig.maxTokens,
      this.stateManager.systemPrompt,
      tokenCalculator
    );
  }

  protected async handleTaskLoop(userContent: UserContent, isNewTask: boolean): Promise<void> {
    // === 性能统计：任务开始 ===
    this.loggerManager.perfStart('handleTaskLoop', 'main-task');

    // 同时使用简化的性能追踪器
    const simpleTracker = this.loggerManager.getPerformanceCollector();
    simpleTracker.start('handleTaskLoop', 'task', 0);

    this.loggerManager.agentInfo(`=== 任务开始 handleTaskLoop ===`);

    let nextUserContent = userContent;
    let includeFileDetails = true;

    // === 性能统计：初始化trace ===
    this.loggerManager.perfStart('initTrace');
    this.loggerManager.initTrace({
      sessionId: this.stateManager.sessionId,
      metadata: {
        ...this.sessionInfo?.reqData,
        agentMode: this.sessionInfo?.agentMode,
        systemPrompt: this.stateManager.systemPrompt
      },
      input: userContent
    });
    // 初始化langfuse
    this.checkpointService.setTrace(this.loggerManager.getTrace());
    const traceDuration = this.loggerManager.perfEnd('initTrace');
    this.loggerManager.agentInfo(`initTrace 耗时: ${traceDuration}ms`);

    // === 性能统计：初始化检查点 ===
    this.loggerManager.perfStart('initCheckpointTracker');
    await this.checkpointService.initCheckpointTracker();
    const checkpointDuration = this.loggerManager.perfEnd('initCheckpointTracker');
    this.loggerManager.agentDebug(`initCheckpointTracker; cost:${checkpointDuration}`);

    // === 性能统计：检查用户修改文件 ===
    this.loggerManager.perfStart('checkUserModifiedFiles');
    try {
      const chatModifiedFilelist =
        this.stateManager
          .getState()
          .apiConversationHistory.filter((message) => message.role === 'user')
          .flatMap((message) => {
            if (typeof message.content === 'string') return [];
            return message.content
              .filter(
                (item) =>
                  item.type === 'text' &&
                  'toolName' in item &&
                  (item.toolName === 'write_to_file' || item.toolName === 'replace_in_file') &&
                  'category' in item &&
                  item.category === 'tool-response' &&
                  'params' in item
              )
              .map((item) => {
                const params = (item as any).params;
                return params && typeof params === 'object' && typeof params.path === 'string' ? params.path : null;
              })
              .filter((path): path is string => path !== null);
          })
          .filter((path, index, array) => array.indexOf(path) === index) || []; // 去重
      this.stateManager.updateState({ chatModifiedFilelist });
      if (chatModifiedFilelist && chatModifiedFilelist.length > 0) {
        this.loggerManager.perfStart('checkpointService.getStatus');
        const uncommitted = (await this.checkpointService?.getStatus()) || [];
        this.loggerManager.perfEnd('checkpointService.getStatus');

        this.filePenddingList = chatModifiedFilelist.filter((p) => uncommitted.includes(p));
        if (this.filePenddingList.length > 0) {
          this.modifyMessage = {
            type: 'text',
            text: `User-modified Files: ${this.filePenddingList.join(', ')}\n\n**CRITICAL**: When file content differs from expectations:\n1. Check edit history: Was your previous edit successful (received "success")?\n2. If successful: Current state = user's intentional modification. Use as new baseline. DO NOT restore.\n3. If failed: Investigate and retry the edit.\n\nKey Rules:\n- Always respect user's manual changes\n- Never assume missing content = "edit failed"\n- When user says "re-read file": understand current state, don't restore content`,
            category: 'user-input'
          };
        }
      }
    } catch (_) { }
    const fileCheckDuration = this.loggerManager.perfEnd('checkUserModifiedFiles');
    this.loggerManager.agentInfo(`检查用户修改文件 耗时: ${fileCheckDuration}ms`);

    // === 性能统计：保存检查点 ===
    this.loggerManager.perfStart('saveCheckpoint');
    await this.checkpointService.saveCheckpoint();
    const saveCheckpointDuration = this.loggerManager.perfEnd('saveCheckpoint');
    this.loggerManager.agentDebug(`saveCheckpoint; cost:${saveCheckpointDuration}`);

    // === 性能统计：获取Wiki列表 ===
    this.loggerManager.perfStart('getWikiList');
    const isContainWiki = userContent.some((item) => item && 'category' in item && item.category === 'project-wiki');
    if (isContainWiki) {
      const projectWiki = await getWikiList();
      if (projectWiki) {
        this.messageService.say('wiki_result', JSON.stringify(projectWiki));
      }
    }
    const wikiDuration = this.loggerManager.perfEnd('getWikiList');
    this.loggerManager.agentDebug(`getWikiList; cost:${wikiDuration}`);

    // === 性能统计：主循环开始 ===
    this.loggerManager.agentInfo(`=== 开始主循环 ===`);

    while (!this.stateManager.getState().abort) {
      this.loggerManager.perfStart('loopIteration');
      const didEndLoop = await this.recursivelyMakeLLMRequests(nextUserContent, includeFileDetails, isNewTask);
      const iterationDuration = this.loggerManager.perfEnd('loopIteration');
      this.loggerManager.agentInfo(`循环迭代 耗时: ${iterationDuration}ms`);

      includeFileDetails = false;
      if (didEndLoop) {
        break;
      }
    }

    // === 性能统计：任务总耗时 ===
    const totalTaskDuration = this.loggerManager.perfEnd('handleTaskLoop');
    this.loggerManager.agentInfo(`=== 任务结束 handleTaskLoop，总耗时: ${totalTaskDuration}ms ===`);
    this.loggerManager.perfMilestone('handleTaskLoop-complete', totalTaskDuration, {
      sessionId: this.stateManager.sessionId,
      chatId: this.stateManager.getState().chatId
    });

    // 清理资源
    this.loggerManager.cleanup();
  }

  protected async recursivelyMakeLLMRequests(
    userContent: UserContent,
    includeFileDetails: boolean = false,
    isNewTask: boolean = false // 保留参数以保持接口兼容性
  ): Promise<boolean> {
    // === 性能统计：LLM请求开始 ===
    this.loggerManager.perfStart('recursivelyMakeLLMRequests', 'llm-request');
    this.loggerManager.agentInfo(`=== 开始 recursivelyMakeLLMRequests ===`);

    this.stateManager.updateState({ startLlmTime: Date.now() });
    if (this.stateManager.getState().abort) throw new Error('Kwaipilot instance aborted');
    // 检查任务是否已完成，如果任务已完成，直接返回true结束循环
    if (this.stateManager.getState().didCompleteTask) return true;

    // === 性能统计：处理API请求限制 ===
    this.loggerManager.perfStart('handleApiRequestLimits');
    await this.handleApiRequestLimits(userContent);
    const apiLimitsDuration = this.loggerManager.perfEnd('handleApiRequestLimits');
    this.loggerManager.agentInfo(`handleApiRequestLimits 耗时: ${apiLimitsDuration}ms`);
    // === 性能统计：发送消息 ===
    this.loggerManager.perfStart('messageService.say');
    await this.messageService.say('api_req_started', JSON.stringify(userContent));
    const sayDuration = this.loggerManager.perfEnd('messageService.say');
    this.loggerManager.agentInfo(`messageService.say 耗时: ${sayDuration}ms`);

    this.loggerManager.agentInfo(`废弃旧的abortController，创建新的abortController`);
    this.abortController?.abort();
    this.abortController = new AbortController();
    const innerAbortController = this.abortController;

    // === 性能统计：获取环境详情 ===
    this.loggerManager.perfStart('getEnvironmentDetails');
    let environmentDetails = await this.messageService.getEnvironmentDetails(includeFileDetails);
    const envDetailsDuration = this.loggerManager.perfEnd('getEnvironmentDetails');
    this.loggerManager.agentInfo(`getEnvironmentDetails 耗时: ${envDetailsDuration}ms`);

    userContent.unshift({ type: 'text', text: environmentDetails, category: 'environment-details' });
    // 如果存在修改消息，添加到最后一个消息的content中
    if (this.modifyMessage) {
      userContent.push(this.modifyMessage);
    }

    // === 性能统计：获取用户操作消息 ===
    this.loggerManager.perfStart('getUserActionMessage');
    const { userDelete, userAdd } = await this.getUserActionMessage();
    const userActionDuration = this.loggerManager.perfEnd('getUserActionMessage');
    this.loggerManager.agentInfo(`getUserActionMessage 耗时: ${userActionDuration}ms`);

    if (userDelete.length) {
      userContent.unshift({
        type: 'text',
        data: {
          action: 'user_delete_file',
          data: userDelete
        },
        category: 'user-action',
        text: JSON.stringify({
          action: 'user_delete_file',
          data: userDelete
        })
      })
    }

    if (userAdd.length) {
      userContent.unshift({
        type: 'text',
        text: JSON.stringify({
          action: 'user_add_file',
          data: userAdd
        }),
        data: {
          action: 'user_add_file',
          data: userAdd
        },
        category: 'user-action'
      })
    }

    // === 性能统计：添加到对话历史 ===
    this.loggerManager.perfStart('addToApiConversationHistory');
    await this.messageService.addToApiConversationHistory({
      role: 'user',
      content: userContent,
      chatId: this.stateManager.getState().chatId
    });
    const addHistoryDuration = this.loggerManager.perfEnd('addToApiConversationHistory');
    this.loggerManager.agentInfo(`addToApiConversationHistory 耗时: ${addHistoryDuration}ms`);

    this.stateManager.resetStreamingState();
    this.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-pre-llm',
      millis: Date.now() - this.stateManager.getState().startLlmTime
    });
    try {
      // === 性能统计：准备请求参数 ===
      this.loggerManager.perfStart('prepareRequestParams');
      const state = this.stateManager.getState();
      const isSupportToolUse = state.modelConfig.isSupportToolUse;
      const servers = getAvailableMcpServers(this.loggerManager.getAgentLogger());
      const enabledTools = this.stateManager.getEnabledTools();
      const prepareParamsDuration = this.loggerManager.perfEnd('prepareRequestParams');
      this.loggerManager.agentInfo(`准备请求参数 耗时: ${prepareParamsDuration}ms`);

      // === 性能统计：上下文优化 ===
      this.loggerManager.perfStart('optimizeMessagesContext');
      const messages =
        isAllMessagesVersion1(state.apiConversationHistory) && this.contextManager
          ? (
            await this.contextManager.optimizeMessagesContext(
              state.apiConversationHistory,
              this.loggerManager.getTrace()
            )
          ).messages
          : state.apiConversationHistory;
      const contextOptimizeDuration = this.loggerManager.perfEnd('optimizeMessagesContext');
      this.loggerManager.agentInfo(`上下文优化 耗时: ${contextOptimizeDuration}ms`);

      // === 性能统计：格式化消息 ===
      this.loggerManager.perfStart('formatMessages');
      const formattedMessages = isSupportToolUse
        ? formatLlmMessages(messages)
        : messages.map((m) => ({
          content: m.content,
          role: m.role,
          chatId: m.chatId
        }));
      const formatMessagesDuration = this.loggerManager.perfEnd('formatMessages');
      this.loggerManager.agentInfo(`格式化消息 耗时: ${formatMessagesDuration}ms`);


      /**FIXME: 临时模型的中英文混合问题处理 */
      if (this.stateManager.modelConfig.model === 'kwaipilot_40b_agent') {
        const lastMessage = formattedMessages[formattedMessages.length - 1];


        if (typeof lastMessage.content === 'string') {
          lastMessage.content += '\n重要：必须用中文回答 必须使用中文回答 必须使用中文回答'
        } else {
          lastMessage.content.push({
            type: 'text',
            text: '重要：必须用中文回答 必须使用中文回答 必须使用中文回答'
          })
        }

      }

      // === 性能统计：获取规则 ===
      this.loggerManager.perfStart('getRulesListForAgent');
      const { userRules, projectRules } = getRulesListForAgent(this.sessionInfo?.rules || []);
      const getRulesDuration = this.loggerManager.perfEnd('getRulesListForAgent');
      this.loggerManager.agentInfo(`获取规则 耗时: ${getRulesDuration}ms`);

      // === 性能统计：构建请求参数 ===
      this.loggerManager.perfStart('buildRequestParams');

      // === 性能统计：获取工具Schema ===
      this.loggerManager.perfStart('getCommonToolsFunctionSchemas');
      const toolsSchema = isSupportToolUse
        ? getCommonToolsFunctionSchemas({
          cwd: this.cwd,
          mcpServers: servers,
          enableRepoIndex: this.options.isEnableRepoIndex,
          useNewEditTool: this.options.isUseNewTool,
          enabledTools: enabledTools,
          isSupportCodeFormatter: this.sessionInfo?.isSupportCodeFormatter
        })
        : undefined;
      const getToolsDuration = this.loggerManager.perfEnd('getCommonToolsFunctionSchemas');
      this.loggerManager.agentInfo(`获取工具Schema 耗时: ${getToolsDuration}ms`);

      // === 性能统计：获取环境详情V2和文件列表 ===
      this.loggerManager.perfStart('getEnvironmentAndFiles');
      const environmentV2Promise = isSupportToolUse ? this.messageService.getEnvironmentDetailsV2() : Promise.resolve({ visibleFiles: [], openTabs: [] });
      const listFilesPromise = isSupportToolUse ? listFiles(this.cwd, false, 100) : Promise.resolve([[], false] as [string[], boolean]);

      const [environmentDetailsV2, listFilesResult] = await Promise.all([environmentV2Promise, listFilesPromise]);
      const workingDirectoryFiles: string[] = listFilesResult[0] || [];
      const envAndFilesDuration = this.loggerManager.perfEnd('getEnvironmentAndFiles');
      this.loggerManager.agentInfo(`getEnvironmentDetailsV2 + listFiles 耗时: ${envAndFilesDuration}ms`);

      /** 这里要在用户消息上添加一条消息：查询所有出现过的文件的存在状态 */
      const reqParams: Chat.AgentChatRequest = {
        ...(this.sessionInfo?.reqData || {}),
        sessionId: state.sessionId,
        chatId: state.chatId,
        model: state.modelConfig.model,
        tools: toolsSchema,
        messages: formattedMessages,
        mode: isSupportToolUse ? (this.options.isAskMode ? 'ask' : 'agent') : undefined,
        rules: isSupportToolUse
          ? [
            {
              type: Chat.RuleType.USER_RULES,
              content: userRules.filter((rule) => rule).join('\n')
            },
            {
              type: Chat.RuleType.PROJECT_RULES,
              content: projectRules.filter((rule) => rule).join('\n')
            }
          ]
          : undefined,
        systemPrompt: isSupportToolUse ? undefined : state.systemPrompt,
        round: 0,
        environment: isSupportToolUse
          ? {
            ...environmentDetailsV2,
            workingDirectory: this.cwd,
            workingDirectoryFiles: workingDirectoryFiles
          }
          : undefined
      };
      const buildReqParamsDuration = this.loggerManager.perfEnd('buildRequestParams');
      this.loggerManager.agentInfo(`构建请求参数 耗时: ${buildReqParamsDuration}ms`);
      if (this.modifyMessage) {
        this.modifyMessage = undefined;
      }
      let assistantMessage = '';
      this.stateManager.updateState({ isStreaming: true, stopReason: '' });
      this.loggerManager.agentInfo(`历史对话记录messages: ${JSON.stringify(messages)}`);
      const reqUUID = v4();

      // === 性能统计：记录从LLM请求开始到API请求发送前的总耗时 ===
      const totalPreApiDuration = this.loggerManager.perfEnd('recursivelyMakeLLMRequests');
      this.loggerManager.perfStart('recursivelyMakeLLMRequests'); // 重新开始计时，用于总耗时
      this.loggerManager.agentInfo(`=== 从 recursivelyMakeLLMRequests 开始到 API 请求发送前 总耗时: ${totalPreApiDuration}ms ===`);
      this.loggerManager.perfMilestone('pre-api-processing', totalPreApiDuration, {
        sessionId: state.sessionId,
        chatId: state.chatId,
        model: state.modelConfig.model
      });

      // 创建一个 Promise 来追踪所有消息处理
      const messageProcessingPromise = new Promise<void>((resolve, reject) => {
        let isComplete = false;
        let hasError = false;
        let output = '';
        let returnFirstToken = false;

        const startApiTime = Date.now();

        const baseHeaders = {
          'Content-Type': 'application/json;charset=UTF-8',
          'kwaipilot-username': this.sessionInfo?.reqData.username || '',
          'kwaipilot-platform': this.sessionInfo?.reqData.deviceInfo?.platform || ''
        };
        const headers: Record<string, string> = this.sessionInfo?.reqData.jwtToken
          ? {
            Authorization: `Bearer ${this.sessionInfo?.reqData.jwtToken}`,
            ...baseHeaders
          }
          : {
            ...baseHeaders
          };

        const chatUrl = isSupportToolUse
          ? '/eapi/kwaipilot/plugin/composer/v2/chat/completions'
          : '/eapi/kwaipilot/plugin/composer/chat/completions';

        this.loggerManager.logApiStart(reqUUID, state.modelConfig.model, chatUrl, reqParams, headers);
        // 保存当前请求参数和headers用于日志记录
        this.currentRequestParams = reqParams;
        this.currentRequestHeaders = headers;

        this.httpClient
          .fetchEventSource(chatUrl, {
            method: 'POST',
            body: JSON.stringify(reqParams),
            headers,
            signal: innerAbortController?.signal,
            onclose: () => {
              this.loggerManager.logApiEnd(reqUUID, state.modelConfig.model, Date.now() - startApiTime);
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, '模型请求接口关闭，fetchEventSource aborted');
                this.loggerManager.endGenerationWithMessage('模型请求接口关闭，fetchEventSource aborted');
                return;
              }
              if (!isComplete) {
                resolve();
              }
            },
            onmessage: async (event: EventSourceMessage) => {
              if (!returnFirstToken) {
                const firstTokenTime = Date.now();
                const apiFirstTokenDuration = firstTokenTime - startApiTime;
                const totalFirstTokenDuration = this.loggerManager.perfEnd('recursivelyMakeLLMRequests');

                // === 🎯 关键性能统计：第一个字符返回！ ===
                this.loggerManager.agentInfo(`=== 🎯 第一个字符返回！从 recursivelyMakeLLMRequests 开始总耗时: ${totalFirstTokenDuration}ms ===`);
                this.loggerManager.agentInfo(`=== 🎯 其中 API 请求到第一个字符耗时: ${apiFirstTokenDuration}ms ===`);

                // 记录到专用性能日志
                this.loggerManager.perfMilestone('first-token-received', totalFirstTokenDuration, {
                  sessionId: state.sessionId,
                  chatId: state.chatId,
                  model: state.modelConfig.model,
                  apiDuration: apiFirstTokenDuration,
                  preApiDuration: totalPreApiDuration
                });

                // 记录到性能收集器
                const collector = this.loggerManager.getPerformanceCollector();
                collector.milestone('first-token-received', {
                  sessionId: state.sessionId,
                  chatId: state.chatId,
                  model: state.modelConfig.model,
                  apiDuration: apiFirstTokenDuration,
                  totalDuration: totalFirstTokenDuration
                });

                // 记录原有的首个token性能
                this.loggerManager.logFirstToken(reqUUID, state.modelConfig.model, apiFirstTokenDuration);

                returnFirstToken = true;
              }
              if (innerAbortController?.signal.aborted) {
                this.loggerManager.agentInfoWithRequestId(reqUUID, 'fetchEventSource aborted');
                return;
              }
              const data = JSON.parse(event.data);
              const traceId = data.traceId;
              if (data.code === 413) {
                this.stateManager.updateState({
                  isTooLongForApiReq: true,
                  tooLongTip: data.tip
                });
                this.loggerManager.logApiTooLongError(
                  reqUUID,
                  state.modelConfig.model,
                  Date.now() - state.startLlmTime
                );
                return;
              }
              if (data.code === 4131) {
                this.stateManager.updateState({ stopReason: 'result_token_isTooLong' });
                this.loggerManager.agentInfo('回复token超长：', { code: data.code });
                return;
              }
              const isSupportToolUse = this.stateManager.getState().modelConfig.isSupportToolUse;

              const usage = isSupportToolUse ? data?.data?.usage : data?.usage;

              if (usage) {
                this.loggerManager.reportAgentTask('agent_task_token', {
                  requestId: reqUUID,
                  sessionId: this.stateManager.getState().sessionId,
                  chatId: this.stateManager.getState().chatId,
                  ts: Date.now(),
                  duration: Date.now() - this.stateManager.getState().startLlmTime,
                  modelName: this.stateManager.modelConfig.model,
                  usage: usage
                });
                
                // 发送token使用量信息给客户端
                await this.sendTokenUsageToClient({
                  usage: usage,
                  sessionId: this.stateManager.getState().sessionId,
                  chatId: this.stateManager.getState().chatId,
                  modelName: this.stateManager.modelConfig.model,
                  requestId: reqUUID,
                  timestamp: Date.now()
                });
              }

              try {
                let delta = '';

                /** xml格式 工具调用 */
                if (!isSupportToolUse) {
                  delta = data?.message?.content;
                } else {
                  /** json schema格式 工具调用 */
                  const content = data?.data?.choices?.[0]?.message?.content;
                  if (content && Array.isArray(content)) {
                    for (let item of content) {
                      if (item.type === 'text') {
                        delta += item.text;
                      } else if (item.type === 'image') {
                        delta += item?.source?.url;
                      }
                    }
                  } else if (typeof content === 'string') {
                    delta += content;
                  }
                }

                assistantMessage += delta || '';
                output = assistantMessage;
                this.loggerManager.agentDebug(`流式返回信息 chunk: ${JSON.stringify(data)}`);
                this.loggerManager.agentInfoWithTrace(reqUUID, traceId, `流式返回信息元: ${delta}`);
                this.loggerManager.agentDebug(`流式返回信息（增量）: ${assistantMessage}`);
                const currentState = this.stateManager.getState();
                const prevLength = currentState.assistantMessageContent.length;

                const newAssistantMessageContent = isSupportToolUse
                  ? formatAssistantMessage(
                    data?.data?.choices?.[0]?.message,
                    this.stateManager.getState().assistantMessageContent
                  )
                  : parseAssistantMessage(assistantMessage);
                this.stateManager.updateState({ assistantMessageContent: newAssistantMessageContent });

                if (isSupportToolUse && this.options.enableDeltaStreaming) {
                  const delta = data?.data?.choices?.[0]?.message;
                  this.stateManager.updateState({
                    assistantMessageDeltaTextBuffer:
                      this.stateManager.getState().assistantMessageDeltaTextBuffer +
                      extractDeltaTextFromAgentChatMessage(delta)
                  });
                }

                if (newAssistantMessageContent.length > prevLength) {
                  this.stateManager.updateState({ userMessageContentReady: false });
                }

                this.presentAssistantMessage();
                if (currentState.didRejectTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didRejectTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  assistantMessage += '\n\n[Response interrupted by user feedback]';
                  resolve();
                }
                if (currentState.didAlreadyUseTool) {
                  this.loggerManager.agentInfoWithRequestId(reqUUID, 'didAlreadyUseTool:innerAbortController?.abort()');
                  innerAbortController?.abort();
                  isComplete = true;
                  if (!isSupportToolUse) {
                    assistantMessage +=
                      '\n\n[Response interrupted by a tool use result. Only one tool may be used at a time and should be placed at the end of the message.]';
                  }
                  resolve();
                }
                return;
              } catch (error: any) {
                this.loggerManager.logMessageParseError(reqUUID, error);
                this.messageService.say('error', `消息解析出错: ${error}`);
                reject(error);
              }
            },
            onerror: (e: any) => {
              this.loggerManager.logApiError(
                reqUUID,
                state.modelConfig.model,
                Date.now() - startApiTime,
                ErrorUtils.errorToJson(e)
              );
              if (!hasError && !isComplete) {
                // 处理错误...
                hasError = true;
                isComplete = true;
                reject(e);
              }
            },
            onFetchLog: (params) => {
              this.loggerManager.logApiStart(reqUUID, state.modelConfig.model, params.url, reqParams, params.headers);
            }
          })
          .then(() => {
            this.loggerManager.updateTrace(output);
            this.loggerManager.endGeneration(output);
          });
      });
      try {
        // 使用Promise包装事件流处理
        await messageProcessingPromise;
        this.loggerManager.agentInfoWithRequestId(reqUUID, '消息处理已完成:' + assistantMessage);
      } catch (error) {
        // 捕获并记录错误，但仍然继续执行
        this.loggerManager.logMessageProcessError(reqUUID, error);
      } finally {
        // 后续处理
        this.stateManager.updateState({
          isStreaming: false,
          didCompleteReadingStream: true
        });

        const currentState = this.stateManager.getState();
        const partialBlocks = currentState.assistantMessageContent.filter((block) => block.partial);
        partialBlocks.forEach((block) => {
          block.partial = false;
          if (block.type === 'tool_use') {
            const toolUse = block as ToolUse;
            if (toolUse.paramsString) {
              try {
                toolUse.params = parse(toolUse.paramsString);
                toolUse.paramsString = '';
              } catch (error) {
                this.loggerManager.agentInfo(`Failed to parse tool use params: ${toolUse.paramsString}`);
                toolUse.params = {};
              }
            }
          }
        });
        if (partialBlocks.length > 0) {
          // 强制更新：通过重新设置相同的引用，确保状态管理器知道内容已经改变
          this.stateManager.updateState({ assistantMessageContent: currentState.assistantMessageContent });

          this.presentAssistantMessage();
        }
        let didEndLoop = false;
        if (currentState.assistantMessageContent.length > 0) {
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: assistantMessage,
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          await pWaitFor(() => this.stateManager.getState().userMessageContentReady);
          const didToolUse = currentState.assistantMessageContent.some((block) => block.type === 'tool_use');

          if (!didToolUse) {
            await this.handleCompletedTask();
            return true;
          }
          this.loggerManager.logLLMSuccess(
            reqUUID,
            currentState.modelConfig.model,
            Date.now() - currentState.startLlmTime
          );

          const recDidEndLoop = await this.recursivelyMakeLLMRequests(currentState.userMessageContent);
          didEndLoop = recDidEndLoop || currentState.didCompleteTask;
        } else {
          if (!currentState.isTooLongForApiReq) {
            // await this.say('api_req_failed', 'API请求出错');
            this.loggerManager.logLLMError(
              reqUUID,
              currentState.modelConfig.model,
              Date.now() - currentState.startLlmTime,
              'api_req_failed'
            );
          }
          await this.messageService.addToApiConversationHistory({
            role: 'assistant',
            content: [
              {
                type: 'text',
                text: 'Failure: I did not provide a response.',
                category: 'assistant'
              }
            ],
            chatId: currentState.chatId
          });
          this.stateManager.updateState({
            consecutiveMistakeCount: currentState.consecutiveMistakeCount + 1
          });
        }

        return didEndLoop;
      }
    } catch (error: any) {
      const currentState = this.stateManager.getState();
      this.loggerManager.logLLMProcessError(
        currentState.modelConfig.model,
        Date.now() - currentState.startLlmTime,
        error
      );
      return true;
    }
  }

  protected async handleApiRequestLimits(userContent: UserContent) {
    if (this.stateManager.getState().isTooLongForApiReq) {
      const { askResponse, text } = await this.messageService.ask(
        'mistake_limit_reached',
        this.stateManager.getState().tooLongTip
      );
      if (askResponse === 'messageResponse' && text) {
        userContent.push({ type: 'text', text, category: 'user-input' });
      }
      this.stateManager.updateState({ isTooLongForApiReq: false, tooLongTip: '' });
    }
    if (this.stateManager.getState().consecutiveMistakeCount >= 3) {
      const state = this.stateManager.getState();
      this.loggerManager.logMistakeLimitReached(state.modelConfig.model, Date.now() - state.startTaskTime);
      const { askResponse, text } = await this.messageService.ask('mistake_limit_reached', i18n.__('agent.error'));
      if (askResponse === 'messageResponse') {
        userContent.push({ type: 'text', text: formatResponse.tooManyMistakes(text), category: 'format-response' });
      }
      this.stateManager.updateState({ consecutiveMistakeCount: 0 });
    }
  }

  protected async presentAssistantMessage() {
    if (this.stateManager.getState().abort) {
      throw new Error('Kwaipilot instance aborted');
    }

    const state = this.stateManager.getState();

    // 如果函数被锁定，标记有待处理的更新，并返回
    if (state.presentAssistantMessageLocked) {
      this.stateManager.updateState({ presentAssistantMessageHasPendingUpdates: true });
      return;
    }

    // 执行中，锁定该函数，待处理为false
    this.stateManager.updateState({
      presentAssistantMessageLocked: true,
      presentAssistantMessageHasPendingUpdates: false
    });

    if (state.currentStreamingContentIndex >= state.assistantMessageContent.length) {
      if (state.didCompleteReadingStream) {
        this.stateManager.updateState({ userMessageContentReady: true });
      }
      this.stateManager.updateState({ presentAssistantMessageLocked: false });
      return;
    }

    const block = cloneDeep(state.assistantMessageContent[state.currentStreamingContentIndex]);
    switch (block.type) {
      case 'text': {
        if (state.didRejectTool || state.didAlreadyUseTool) {
          break;
        }
        let content = block.content;
        if (content) {
          content = content.replace(/<thinking>\s?/g, '');
          content = content.replace(/\s?<\/thinking>/g, '');
          const lastOpenBracketIndex = content.lastIndexOf('<');
          if (lastOpenBracketIndex !== -1) {
            const possibleTag = content.slice(lastOpenBracketIndex);
            const hasCloseBracket = possibleTag.includes('>');
            if (!hasCloseBracket) {
              // Extract the potential tag name
              let tagContent: string;
              if (possibleTag.startsWith('</')) {
                tagContent = possibleTag.slice(2).trim();
              } else {
                tagContent = possibleTag.slice(1).trim();
              }
              // Check if tagContent is likely an incomplete tag name (letters and underscores only)
              const isLikelyTagName = /^[a-zA-Z_]+$/.test(tagContent);
              // Preemptively remove < or </ to keep from these artifacts showing up in chat (also handles closing thinking tags)
              const isOpeningOrClosing = possibleTag === '<' || possibleTag === '</';
              // If the tag is incomplete and at the end, remove it from the content
              if (isOpeningOrClosing || isLikelyTagName) {
                content = content.slice(0, lastOpenBracketIndex).trim();
              }
            }
          }
        }
        if (!block.partial) {
          // Some models add code block artifacts (around the tool calls) which show up at the end of text content
          // matches ``` with at least one char after the last backtick, at the end of the string
          const match = content?.trimEnd().match(/```[a-zA-Z0-9_-]+$/);
          if (match) {
            const matchLength = match[0].length;
            content = content.trimEnd().slice(0, -matchLength);
          }
        }
        if (this.options.enableDeltaStreaming) {
          this.stateManager.updateState({ assistantMessageDeltaTextBuffer: '' });
          this.loggerManager.agentDebug(`[AgentManager] sayDeltaText ${state.assistantMessageDeltaTextBuffer}`);
          await this.messageService.sayDeltaText(
            'text',
            block.content,
            state.assistantMessageDeltaTextBuffer,
            undefined,
            !block.partial
          );
          if (!block.partial) {
            // 触发 messageList
            await this.messageService.say('text', block.content, block.partial);
          }
        } else {
          await this.messageService.say('text', block.content, block.partial);
        }
        break;
      }
      case 'tool_use': {
        const currentState = this.stateManager.getState();
        const toolDescription = ToolHelpers.getToolDescription(block);
        if (currentState.didRejectTool) {
          if (!block.partial) {
            currentState.userMessageContent.push({
              type: 'text',
              text: `Skipping tool ${toolDescription} due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params: block.params,
              toolId: block.id
            });
          } else {
            // partial tool after user rejected a previous tool
            currentState.userMessageContent.push({
              type: 'text',
              text: `Tool ${toolDescription} was interrupted and not executed due to user rejecting a previous tool.`,
              category: 'tool-exception',
              toolName: block.name,
              params: block.params,
              toolId: block.id
            });
          }
          this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
          break;
        }
        if (currentState.didAlreadyUseTool) {
          // ignore any content after a tool has already been used
          currentState.userMessageContent.push({
            type: 'text',
            text: `Tool [${block.name}] was not executed because a tool has already been used in this message. Only one tool may be used per message. You must assess the first tool's result before proceeding to use the next tool.`,
            category: 'tool-exception',
            toolName: block.name,
            params: block.params,
            toolId: block.id
          });
          this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
          break;
        }
        await this.toolExecutor.executeToolUse(block, currentState.userMessageContent);
        // 更新 state 中的 userMessageContent，因为工具处理器可能修改了它
        this.stateManager.updateState({ userMessageContent: currentState.userMessageContent });
        break;
      }
    }

    // 解锁消息处理
    this.stateManager.updateState({ presentAssistantMessageLocked: false });
    // 处理完成块或拒绝/已使用工具的情况
    const finalState = this.stateManager.getState();
    if (!block.partial || finalState.didRejectTool || finalState.didAlreadyUseTool) {
      // 检查是否是最后一个块
      if (finalState.currentStreamingContentIndex === finalState.assistantMessageContent.length - 1) {
        // its okay that we increment if !didCompleteReadingStream, it'll just return bc out of bounds and as streaming continues it will call presentAssistantMessage if a new block is ready. if streaming is finished then we set userMessageContentReady to true when out of bounds. This gracefully allows the stream to continue on and all potential content blocks be presented.
        // last block is complete and it is finished executing
        this.stateManager.updateState({ userMessageContentReady: true });
      }
      // 移动到下一个块
      // need to increment regardless, so when read stream calls this function again it will be streaming the next block
      this.stateManager.updateState({
        currentStreamingContentIndex: finalState.currentStreamingContentIndex + 1
      });
      // 如果还有更多块，递归处理
      if (finalState.currentStreamingContentIndex + 1 < finalState.assistantMessageContent.length) {
        // there are already more content blocks to stream, so we'll call this function ourselves
        this.presentAssistantMessage();
        return;
      }
    }
    // block is partial, but the read stream may have finished
    if (finalState.presentAssistantMessageHasPendingUpdates) {
      this.presentAssistantMessage();
    }
  }

  protected async beforeCompletedTask() {
    // 子类可以重写此方法以在任务完成之前执行其他操作
  }

  async handleCompletedTask() {
    await this.beforeCompletedTask();
    const stopReason = this.stateManager.getState().stopReason;
    await this.messageService.say('completion_result', '', false, undefined, stopReason);
    this.stateManager.updateState({ didCompleteTask: true });
    const state = this.stateManager.getState();
    this.loggerManager.logTaskCompleted(state.modelConfig.model, Date.now() - state.startTaskTime);
  }


  private async getUserActionMessage() {
    const messages = this.stateManager.getState().apiConversationHistory;

    const fileStatusMap = new Map<string, 'delete' | 'add'>()

    messages.forEach(m => {
      if (m.role === 'user' && m.version === 1) {
        if (Array.isArray(m.content)) {
          m.content.forEach(c => {
            if (c.type === 'text' && 'category' in c && c.category === 'user-action' && 'data' in c) {
              try {
                const actions = c.data;
                const { action, data } = actions
                if (action === 'user_delete_file') {
                  for (const item of data) {
                    fileStatusMap.set(item, 'delete');
                  }
                } else if (action === 'user_add_file') {
                  for (const item of data) {
                    fileStatusMap.set(item, 'add');
                  }
                }
              } catch (error) {
                this.loggerManager.agentDebug('getUserActionMessage error', error)
              }
            }
          })
        }
      }
    })
    /** 找到被删除的文件 */
    const { deletedFiles, existFiles } = await this.handelGetMessagesFileStatus();

    const userDelete: string[] = [];
    const userAdd: string[] = []

    deletedFiles.forEach(i => {
      const mapHasFile = fileStatusMap.has(i.filePath);
      if (mapHasFile) {
        const status = fileStatusMap.get(i.filePath);
        if (status === 'add') {
          userDelete.push(i.filePath)
        }
      } else {
        userDelete.push(i.filePath)
      }
    })

    existFiles.forEach(i => {
      const mapHasFile = fileStatusMap.has(i.filePath);
      if (mapHasFile) {
        const status = fileStatusMap.get(i.filePath);
        if (status === 'delete') {
          userAdd.push(i.filePath)
        }
      }
    })

    return { userDelete, userAdd }
  }

  private async handelGetMessagesFileStatus() {
    try {
      // 收集历史记录中所有被编辑或查看过的文件路径
      const modifiedFileList = this.stateManager.getState().apiConversationHistory.filter(message => message.role === 'user')
        .flatMap(message => {
          if (typeof message.content === 'string') return [];
          return message.content
            .filter(item =>
              item.type === 'text' &&
              'toolName' in item &&
              (item.toolName === 'write_to_file' || item.toolName === 'replace_in_file' || item.toolName === 'read_file') &&
              'category' in item &&
              item.category === 'tool-response' &&
              'params' in item
            )
            .map(item => {
              const params = (item as any).params;
              return params && typeof params === 'object' && typeof params.path === 'string' ? params.path : null;
            })
            .filter((path): path is string => path !== null);
        }).filter((path, index, array) => array.indexOf(path) === index) || [];

      // 检查文件是否被删除（并行执行）
      const checkDeletedFiles = async (fileList: string[]) => {
        const checkPromises = fileList.map(async (filePath) => {
          try {
            await fs.access(path.resolve(this.cwd, filePath))
            return { status: 'exist', filePath }; // 文件存在，返回null
          } catch (error) {
            return { status: 'delete', filePath };// 文件不存在，返回文件路径
          }
        });

        const results = await Promise.all(checkPromises);
        return results
      }

      const statusFiles = await checkDeletedFiles(modifiedFileList)
      const existFiles = statusFiles.filter(i => i.status === 'exist')
      const deletedFiles = statusFiles.filter(i => i.status === 'delete')
      return { deletedFiles, existFiles }
    } catch (error) {
      this.loggerManager.agentDebug('error handling add delete file to message:', error)
      return { deletedFiles: [], existFiles: [] };
    }
  }

  /** 用于上报 AI 代码生成，统计代码生成率 */
  async reportGenerateCode(request: ReportGenerateCodeRequest['editDataList']): Promise<void> {
    await this.httpClient.post(
      '/eapi/kwaipilot/log/edit-file',
      JSON.stringify({
        chatId: this.stateManager.getState().chatId,
        modelType: this.stateManager.getState().modelConfig.model,
        platform: this.sessionInfo?.reqData.deviceInfo?.platform,
        sessionId: this.stateManager.getState().sessionId,
        username: this.sessionInfo?.reqData.username || GlobalConfig.getConfig().getUsername(),
        editDataList: request
      })
    );
  }

  /**
   * 获取当前请求参数
   */
  getCurrentRequestParams(): any {
    return { reqParams: this.currentRequestParams, headers: this.currentRequestHeaders };
  }
  /** 发送token使用量信息给客户端 */
  async sendTokenUsageToClient(tokenUsageData: {
    usage: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
      cached_creation_input_tokens?: number;
      cached_read_input_tokens?: number;
    };
    sessionId: string;
    chatId: string;
    modelName: string;
    requestId: string;
    timestamp: number;
  }): Promise<void> {
    try {
      await this.messenger.request('assistant/agent/tokenUsage', tokenUsageData);
      this.loggerManager.agentInfo('Token使用量已发送给客户端:', tokenUsageData);
    } catch (error) {
      this.loggerManager.agentInfo('发送Token使用量失败:', error);
    }
  }
}
