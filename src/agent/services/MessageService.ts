import {
  Say,
  Ask,
  LocalMessage,
  MessageParam,
  MessageParamVersion0,
  MessageParamVersion1,
  TextBlockParamVersion1,
  ImageBlockParam
} from '../types/type.d';
import { StateManager } from './StateManager';
import { I<PERSON>essenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { LoggerManager } from './LoggerManager';
import delay from 'delay';
import pWaitFor from 'p-wait-for';
import { throttle } from 'lodash-es';

type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';

export class MessageService {
  constructor(
    private messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    private stateManager: StateManager,
    private loggerManager: LoggerManager
  ) {}

  async say(type: Say, text?: string, partial?: boolean, role?: 'user', stopReason?: string): Promise<void> {
    const state = this.stateManager.getState();

    if (state.abort) {
      throw new Error('Kwaipilot instance aborted');
    }

    if (partial !== undefined) {
      await this.handlePartialMessage(type, text, partial, role, stopReason);
    } else {
      await this.handleNormalMessage(type, text, role);
    }
  }

  async sayDeltaText(
    type: Extract<Say, 'text'>,
    text: string,
    deltaText: string,
    role: 'user' | undefined,
    done: boolean
  ): Promise<void> {
    const state = this.stateManager.getState();

    if (state.abort) {
      throw new Error('Kwaipilot instance aborted');
    }

    const lastMessage = state.localMessages.at(-1);
    const isUpdatingPreviousPartial =
      lastMessage && lastMessage.partial && lastMessage.type === 'say' && lastMessage.say === type;

    if (isUpdatingPreviousPartial) {
      // 更新现有的部分消息
      lastMessage.text = text;
      await this.messenger.send('assistant/agent/messageDelta', {
        ts: lastMessage.ts,
        type: 'textDelta',
        text: deltaText,
        done
      });
    } else {
      // 创建新的部分消息
      const newSayTs = await this.generateSayTimestamp();
      await this.addToLocalMessages({
        ts: newSayTs,
        type: 'say',
        say: type,
        text,
        partial: true,
        role,
        sessionId: state.sessionId,
        lastCheckpointHash: state.lastCheckpointHash,
        chatMode: state.chatMode
      });
    }
  }

  async ask(
    type: Ask,
    text?: string,
    partial?: boolean,
    role?: 'user'
  ): Promise<{
    type: string;
    askResponse: AskResponse;
    text?: string;
  }> {
    const state = this.stateManager.getState();

    if (state.abort) {
      throw new Error('Kwaipilot instance aborted');
    }

    if (partial !== undefined) {
      await this.handlePartialAsk(type, text, partial);
    } else {
      await this.handleNormalAsk(type, text);
    }

    // 等待用户响应
    await pWaitFor(
      () => {
        const currentState = this.stateManager.getState();
        return currentState.askResponse !== undefined;
      },
      { interval: 100 }
    );

    const currentState = this.stateManager.getState();
    const result = {
      type,
      askResponse: currentState.askResponse!,
      text: currentState.askResponseText
    };

    // 清理响应状态
    this.stateManager.updateState({
      askResponse: undefined,
      askResponseText: undefined
    });

    return result;
  }

  updateAskResponse(response: { askResponse: AskResponse; text?: string }): void {
    this.stateManager.updateState({
      askResponse: response.askResponse,
      askResponseText: response.text
    });
  }

  // ============ 对话管理功能 ============

  async addToApiConversationHistory(message: Omit<MessageParamVersion1, 'version'>): Promise<void> {
    const state = this.stateManager.getState();
    message.chatId = state.chatId;
    const updatedHistory: MessageParam[] = [
      ...state.apiConversationHistory,
      {
        ...message,
        version: 1
      }
    ];

    this.stateManager.updateState({ apiConversationHistory: updatedHistory });
    await this.messenger.send('assistant/agent/apiConversationList', updatedHistory);
  }

  async getEnvironmentDetails(includeFileDetails: boolean = false): Promise<string> {
    const res = await this.messenger.request('assistant/agent/environment', { includeFileDetails });
    this.loggerManager.agentInfo(`获取环境信息成功`);
    return res.data || '';
  }

  async getEnvironmentDetailsV2(): Promise<{ visibleFiles: string[]; openTabs: string[] }> {
    const res = await this.messenger.request('assistant/agent/environmentV2', undefined);
    this.loggerManager.agentInfo(`获取环境信息V2成功`);
    return res.data || { visibleFiles: [], openTabs: [] };
  }

  getApiConversationHistory(): (MessageParam | MessageParamVersion0)[] {
    return this.stateManager.getState().apiConversationHistory;
  }

  getLocalMessages(): LocalMessage[] {
    return this.stateManager.getState().localMessages;
  }

  setLocalMessages(messages: LocalMessage[]): void {
    this.stateManager.updateState({ localMessages: messages });
  }

  setApiConversationHistory(history: MessageParam[]): void {
    this.stateManager.updateState({ apiConversationHistory: history });
  }

  async addToWebviewMessages(message: LocalMessage) {
    const state = this.stateManager.getState();
    message.chatId = state.chatId;
    message.sessionId = state.sessionId;
    this.loggerManager.agentDebug(`发送给ide的消息：${JSON.stringify(message)}`);
    this.throttledSendSingleMessage(message);
  }

  async addToLocalMessages(message: LocalMessage): Promise<void> {
    const state = this.stateManager.getState();
    message.chatId = state.chatId;
    const updatedMessages = [...state.localMessages, message];
    this.stateManager.updateState({ localMessages: updatedMessages });
    await this.saveToLocalMessages();
    this.loggerManager.agentDebug(`发送给ide的消息：${JSON.stringify(message)}`);
    this.throttledSendSingleMessage(message);
  }

  private async saveToLocalMessages(): Promise<void> {
    const state = this.stateManager.getState();
    this.loggerManager.agentDebug(`发送给ide的messageList消息：${JSON.stringify(state.localMessages)}`);
    await this.messenger.send('assistant/agent/messageList', state.localMessages);
  }

  private async generateSayTimestamp(): Promise<number> {
    const state = this.stateManager.getState();
    let sayTs = Date.now();
    if (state.lastMessageTs === sayTs) {
      await delay(1);
      sayTs = Date.now();
    }
    this.stateManager.updateState({ lastMessageTs: sayTs });
    return sayTs;
  }

  private async handlePartialMessage(
    type: Say,
    text?: string,
    partial?: boolean,
    role?: 'user',
    stopReason?: string
  ): Promise<void> {
    const state = this.stateManager.getState();
    const lastMessage = state.localMessages.at(-1);
    const isUpdatingPreviousPartial =
      lastMessage && lastMessage.partial && lastMessage.type === 'say' && lastMessage.say === type;

    if (partial) {
      if (isUpdatingPreviousPartial) {
        // 更新现有的部分消息
        lastMessage.text = text;
        lastMessage.partial = partial;
        lastMessage.role = role;
        await this.addToWebviewMessages(lastMessage);
      } else {
        // 创建新的部分消息
        const newSayTs = await this.generateSayTimestamp();
        await this.addToLocalMessages({
          ts: newSayTs,
          type: 'say',
          say: type,
          text,
          partial,
          role,
          sessionId: state.sessionId,
          lastCheckpointHash: state.lastCheckpointHash,
          chatMode: state.chatMode
        });
      }
    } else {
      // partial=false means its a complete version of a previously partial message
      if (isUpdatingPreviousPartial) {
        this.stateManager.updateState({ lastMessageTs: lastMessage.ts });
        lastMessage.text = text;
        lastMessage.partial = false;
        lastMessage.role = role;
        lastMessage.lastCheckpointHash = state.lastCheckpointHash;
        lastMessage.stopReason = stopReason;
        lastMessage.chatMode = state.chatMode;

        // 更新本地消息列表
        const updatedMessages = [...state.localMessages];
        updatedMessages.pop();
        this.stateManager.updateState({ localMessages: updatedMessages });
        await this.addToLocalMessages(lastMessage);
      } else {
        const state = this.stateManager.getState();
        const newSayTs = await this.generateSayTimestamp();
        await this.addToLocalMessages({
          ts: newSayTs,
          type: 'say',
          say: type,
          text,
          role,
          sessionId: state.sessionId,
          lastCheckpointHash: state.lastCheckpointHash,
          stopReason,
          chatMode: state.chatMode
        });
      }
    }
  }

  private async handleNormalMessage(type: Say, text?: string, role?: 'user'): Promise<void> {
    const state = this.stateManager.getState();
    const newSayTs = await this.generateSayTimestamp();

    await this.addToLocalMessages({
      ts: newSayTs,
      type: 'say',
      say: type,
      text,
      role,
      sessionId: state.sessionId,
      lastCheckpointHash: state.lastCheckpointHash,
      chatMode: state.chatMode
    });
  }

  private async handlePartialAsk(type: Ask, text?: string, partial?: boolean): Promise<void> {
    const state = this.stateManager.getState();
    const lastMessage = state.localMessages.at(-1);
    const isUpdatingPreviousPartial =
      lastMessage && lastMessage.partial && lastMessage.type === 'ask' && lastMessage.ask === type;

    if (partial) {
      if (isUpdatingPreviousPartial) {
        lastMessage.text = text;
        lastMessage.partial = partial;
        lastMessage.lastCheckpointHash = state.lastCheckpointHash;
        await this.addToWebviewMessages(lastMessage);
        throw new Error('Current ask promise was ignored 1');
      } else {
        const askTs = Date.now();
        this.stateManager.updateState({ lastMessageTs: askTs });
        await this.addToLocalMessages({
          ts: askTs,
          type: 'ask',
          ask: type,
          text,
          partial,
          sessionId: state.sessionId,
          lastCheckpointHash: state.lastCheckpointHash,
          chatMode: state.chatMode
        });
        throw new Error('Current ask promise was ignored 2');
      }
    } else {
      if (isUpdatingPreviousPartial) {
        this.stateManager.updateState({
          askResponse: undefined,
          askResponseText: undefined
        });
        const askTs = lastMessage.ts;
        this.stateManager.updateState({ lastMessageTs: askTs });
        lastMessage.text = text;
        lastMessage.partial = false;
        lastMessage.lastCheckpointHash = state.lastCheckpointHash;
        lastMessage.chatMode = state.chatMode;

        // 更新本地消息列表
        const updatedMessages = [...state.localMessages];
        updatedMessages.pop();
        this.stateManager.updateState({ localMessages: updatedMessages });

        await this.addToLocalMessages(lastMessage);
      } else {
        await this.handleNormalAsk(type, text);
      }
    }
  }

  private async handleNormalAsk(type: Ask, text?: string): Promise<void> {
    const state = this.stateManager.getState();
    this.stateManager.updateState({
      askResponse: undefined,
      askResponseText: undefined
    });
    const askTs = Date.now();
    this.stateManager.updateState({ lastMessageTs: askTs });
    await this.addToLocalMessages({
      ts: askTs,
      type: 'ask',
      ask: type,
      text,
      sessionId: state.sessionId,
      lastCheckpointHash: state.lastCheckpointHash,
      chatMode: state.chatMode
    });
  }

  private async sendSingleMessage(message: LocalMessage) {
    await this.messenger.send('assistant/agent/message', message);
  }

  private throttledSendSingleMessage = (() => {
    let currentSendingMessageTs: number | undefined;
    const throttled = throttle(this.sendSingleMessage.bind(this), 200, {
      leading: true,
      trailing: true
    });
    return (message: LocalMessage) => {
      if (currentSendingMessageTs && message.ts !== currentSendingMessageTs) {
        throttled.flush();
      }
      currentSendingMessageTs = message.ts;
      return throttled(message);
    };
  })();
}
