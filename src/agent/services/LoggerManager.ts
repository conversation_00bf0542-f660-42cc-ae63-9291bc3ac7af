import { AgentDataSet<PERSON>og<PERSON>, AgentLogger, Logger } from '@/util/log';
import { LangfuseTraceClient, LangfuseGenerationClient } from 'langfuse';
import { getTrace } from '@/util/langfuse';
import { PerfInfo } from '@/util/log.d';
import { CUSTOM_USER_ACTION } from '@/util/weblogger';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { ErrorUtils } from '@/util/error';
import { PerfLogger, PerfLoggerManager } from '@/util/perf-logger';
import { PerformanceCollector, PerformanceCollectorManager, PerformanceReport } from '@/util/performance-collector';
import { JsonPerformanceLogger, JsonPerformanceLoggerManager } from '@/util/json-performance-logger';

export interface LoggerConfig {
  sessionId: string;
  chatId: string;
  username?: string;
  scope?: string;
  agentMode?: 'duet' | 'jam';
}

export interface TraceMetadata {
  sessionId: string;
  metadata?: Record<string, any>;
  input?: any;
}

/**
 * 统一的日志管理器
 * 负责管理所有类型的logger，提供统一的接口
 */
export class LoggerManager {
  private agentLogger: InstanceType<typeof AgentLogger>;
  private agentDataSetLogger: InstanceType<typeof AgentDataSetLogger>;
  private performanceLogger: Logger;
  private perfLogger: PerfLogger;
  private performanceCollector: PerformanceCollector;
  private jsonPerfLogger: JsonPerformanceLogger;
  private trace: LangfuseTraceClient | null = null;
  private traceGeneration: LangfuseGenerationClient | null = null;
  private readonly config: LoggerConfig;

  constructor(config: LoggerConfig) {
    this.config = config;
    this.agentLogger = new AgentLogger(`s-${config.sessionId}`);
    this.agentDataSetLogger = new AgentDataSetLogger(`s-${config.sessionId}`);
    this.performanceLogger = new Logger(config.scope || 'assistant-agent');
    this.perfLogger = PerfLoggerManager.getInstance().getLogger(config.sessionId, config.chatId);

    // 初始化性能收集器，设置上报回调
    this.performanceCollector = PerformanceCollectorManager.getCollector(
      config.sessionId,
      (report: PerformanceReport) => {
        this.agentInfo(`📊 性能报告生成: 总耗时 ${report.totalDuration}ms, 第一个字符 ${report.keyMetrics.firstTokenTime}ms`);
        // 这里可以添加上报逻辑
        this.uploadPerformanceData(report);
      }
    );

    // 初始化JSON性能日志记录器
    this.jsonPerfLogger = JsonPerformanceLoggerManager.getLogger(config.sessionId, config.chatId);
  }

  // ============ Agent日志方法 ============

  /**
   * 记录agent信息日志
   */
  agentInfo(message: string, ...params: any[]): void {
    this.agentLogger.info(message, ...params);
  }

  /**
   * 记录agent错误日志
   */
  agentError(message: string, ...params: any[]): void {
    this.agentLogger.error(message, ...params);
  }

  /**
   * 记录agent调试日志
   */
  agentDebug(message: string, ...params: any[]): void {
    this.agentLogger.debug(message, ...params);
  }

  /**
   * 记录带请求ID的agent日志
   */
  agentInfoWithRequestId(requestId: string, message: string, ...params: any[]): void {
    this.agentLogger.info(`[${requestId}] ${message}`, ...params);
  }

  /**
   * 记录带请求ID和traceId的agent日志
   */
  agentInfoWithTrace(requestId: string, traceId: string, message: string, ...params: any[]): void {
    this.agentLogger.info(`[${requestId}] [${traceId}] ${message}`, ...params);
  }

  // ============ 性能日志方法 ============

  /**
   * 记录性能日志
   */
  perf(log: PerfInfo): void {
    let subtag = log.subtag;
    if (this.config.agentMode === 'duet') {
      // 增加对 duet 适配上报
      subtag = subtag + '-duet';
    }
    this.performanceLogger.perf({ ...log, subtag });
  }

  /**
   * 记录用户行为
   */
  reportUserAction(action: CUSTOM_USER_ACTION): void {
    this.performanceLogger.reportUserAction({ ...action, agentMode: this.config.agentMode || 'jam' });
  }

  /**
   * 记录页面访问
   */
  collectPV(page: string, params?: any): void {
    this.performanceLogger.collectPV(page, params);
  }

  /**
   * 记录点击事件
   */
  collectClick(action: string, params?: any): void {
    this.performanceLogger.collectClick(action, params);
  }

  // ============ 便捷性能日志方法 ============

  /**
   * 记录LLM API相关的性能日志
   */
  perfLLMApi(subtag: string, duration: number, status: 'success' | 'error', modelName: string, extra?: string): void {
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: `kwaipilot-ide-agent-${subtag}`,
      millis: duration,
      extra4: status,
      extra6: modelName,
      extra3: extra
    });
  }

  /**
   * 记录agent任务相关的用户行为
   */
  reportAgentTask(
    type: string,
    data: {
      sessionId: string;
      chatId: string;
      requestId?: string;
      ts: number;
      duration?: number;
      error?: string;
      modelName?: string;
      [key: string]: any;
    }
  ): void {
    this.reportUserAction({
      key: 'agent_task',
      type,
      content: JSON.stringify(data)
    });
  }

  // ============ Trace管理方法 ============

  /**
   * 初始化trace
   */
  initTrace(metadata: TraceMetadata): void {
    this.trace = getTrace(metadata.sessionId, this.config.username, {
      sessionId: metadata.sessionId,
      metadata: metadata.metadata,
      input: metadata.input
    });
  }

  /**
   * 获取当前trace
   */
  getTrace(): LangfuseTraceClient | null {
    return this.trace;
  }

  /**
   * 更新trace
   */
  updateTrace(data: any): void {
    this.trace?.update({
      output: data
    });
  }

  /**
   * 开始generation
   */
  startGeneration(name: string, input: any): void {
    this.traceGeneration =
      this.trace?.generation({
        name,
        input
      }) || null;
  }

  /**
   * 结束generation
   */
  endGeneration(output: any): void {
    this.traceGeneration?.end({
      output
    });
  }

  /**
   * 获取当前generation
   */
  getGeneration(): LangfuseGenerationClient | null {
    return this.traceGeneration;
  }

  /**
   * 手动结束generation（用于错误或中断场景）
   */
  endGenerationWithMessage(message: string): void {
    this.traceGeneration?.end({
      output: {
        msg: message
      }
    });
  }

  // ============ 获取内部logger实例方法 ============

  /**
   * 获取AgentLogger实例（用于向下传递给其他组件）
   */
  getAgentLogger(): InstanceType<typeof AgentLogger> {
    return this.agentLogger;
  }

  /**
   * 获取AgentDataSetLogger实例（用于向下传递给其他组件）
   */
  getAgentDataSetLogger(): InstanceType<typeof AgentDataSetLogger> {
    return this.agentDataSetLogger;
  }

  /**
   * 获取性能Logger实例（用于向下传递给其他组件）
   */
  getPerformanceLogger(): Logger {
    return this.performanceLogger;
  }

  /**
   * 获取专用性能日志记录器
   */
  getPerfLogger(): PerfLogger {
    return this.perfLogger;
  }

  // ============ 专用性能日志方法 ============

  /**
   * 开始记录函数执行时间
   */
  perfStart(functionName: string, context?: string): void {
    this.perfLogger.start(functionName, context);
    // 同时记录到性能收集器
    this.performanceCollector.start(functionName, this.getCategoryFromContext(context), this.getLevelFromContext(context));
    // 记录到JSON性能日志
    this.jsonPerfLogger.startStage(functionName);
  }

  /**
   * 结束记录函数执行时间并返回耗时
   */
  perfEnd(functionName: string, context?: string): number {
    const duration = this.perfLogger.end(functionName, context);
    // 同时记录到性能收集器
    this.performanceCollector.end(functionName);
    // 记录到JSON性能日志
    this.jsonPerfLogger.endStage(functionName);
    return duration;
  }

  /**
   * 记录单次性能数据
   */
  perfRecord(functionName: string, duration: number, context?: string): void {
    this.perfLogger.record(functionName, duration, context);
  }

  /**
   * 记录关键性能里程碑
   */
  perfMilestone(milestone: string, totalDuration: number, details?: Record<string, any>): void {
    this.perfLogger.milestone(milestone, totalDuration, details);
    // 同时记录到性能收集器
    this.performanceCollector.milestone(milestone, details);
  }

  /**
   * 记录阶段性能汇总
   */
  perfPhaseSummary(phase: string, functions: Array<{name: string, duration: number}>, totalDuration: number): void {
    this.perfLogger.phaseSummary(phase, functions, totalDuration);
  }

  /**
   * 记录性能相关错误
   */
  perfError(functionName: string, error: any): void {
    this.perfLogger.error(functionName, error);
  }

  /**
   * 记录性能相关警告
   */
  perfWarn(functionName: string, warning: string): void {
    this.perfLogger.warn(functionName, warning);
  }

  /**
   * 记录性能相关调试信息
   */
  perfDebug(functionName: string, debug: string): void {
    this.perfLogger.debug(functionName, debug);
  }

  // ============ 性能收集器专用方法 ============

  /**
   * 获取性能收集器
   */
  getPerformanceCollector(): PerformanceCollector {
    return this.performanceCollector;
  }

  /**
   * 获取JSON性能日志记录器
   */
  getJsonPerformanceLogger(): JsonPerformanceLogger {
    return this.jsonPerfLogger;
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport(taskId?: string): PerformanceReport {
    return this.performanceCollector.generateReport(taskId || this.config.chatId);
  }

  /**
   * 获取图表数据
   */
  getPerformanceChartData() {
    return this.performanceCollector.getChartData();
  }

  /**
   * 获取上报数据
   */
  getPerformanceUploadData() {
    return this.performanceCollector.getUploadData();
  }

  /**
   * 上报性能数据
   */
  private uploadPerformanceData(report: PerformanceReport): void {
    try {
      // 这里可以添加实际的上报逻辑
      // 例如发送到监控系统、数据库等
      this.agentDebug(`性能数据上报: ${JSON.stringify({
        sessionId: report.sessionId,
        taskId: report.taskId,
        totalDuration: report.totalDuration,
        firstTokenTime: report.keyMetrics.firstTokenTime,
        apiRequestTime: report.keyMetrics.apiRequestTime,
        preProcessingTime: report.keyMetrics.preProcessingTime,
        phasesCount: report.phases.length
      })}`);

      // 记录到性能日志
      this.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'performance-report-generated',
        millis: report.totalDuration,
        extra4: `first-token:${report.keyMetrics.firstTokenTime}ms`,
        extra6: `api:${report.keyMetrics.apiRequestTime}ms,pre:${report.keyMetrics.preProcessingTime}ms`
      });
    } catch (error) {
      this.agentError(`性能数据上报失败: ${error}`);
    }
  }

  /**
   * 从上下文获取分类
   */
  private getCategoryFromContext(context?: string): string {
    if (!context) return 'default';

    const categoryMap: Record<string, string> = {
      'main-task': 'task',
      'llm-request': 'api',
      'preprocessing': 'preprocessing',
      'postprocessing': 'postprocessing',
      'init': 'initialization',
      'cleanup': 'cleanup'
    };

    return categoryMap[context] || context;
  }

  /**
   * 从上下文获取层级
   */
  private getLevelFromContext(context?: string): number {
    if (!context) return 0;

    const levelMap: Record<string, number> = {
      'main-task': 0,
      'llm-request': 1,
      'preprocessing': 2,
      'postprocessing': 2,
      'init': 2,
      'cleanup': 2
    };

    return levelMap[context] || 1;
  }

  // ============ 清理方法 ============

  /**
   * 清理资源
   */
  cleanup(): void {
    // 生成最终性能报告
    try {
      const finalReport = this.generatePerformanceReport();
      this.agentInfo(`📊 最终性能报告: 总耗时 ${finalReport.totalDuration}ms`);
    } catch (error) {
      this.agentError(`生成最终性能报告失败: ${error}`);
    }

    // 清理 Langfuse 资源
    this.trace = null;
    this.traceGeneration = null;

    // 关闭性能日志记录器
    PerfLoggerManager.getInstance().closeLogger(this.config.sessionId, this.config.chatId);

    // 清理性能收集器
    PerformanceCollectorManager.removeCollector(this.config.sessionId);

    // 写入JSON性能日志结束标识并清理
    JsonPerformanceLoggerManager.writeEndFlag(this.config.sessionId);
    JsonPerformanceLoggerManager.removeLogger(this.config.sessionId);
  }

  // ============ 组合方法 ============

  /**
   * 记录API请求开始
   */
  logApiStart(requestId: string, modelName: string, url: string, reqParams: any, headers: any): void {
    const startTime = Date.now();
    this.agentInfoWithRequestId(requestId, '开始请求');
    this.agentInfoWithRequestId(requestId, `fetchEventSource 请求参数: URL=${url}, 请求体=${JSON.stringify(reqParams)}, 请求头=${JSON.stringify(headers)}`);
    this.reportAgentTask('agent_llm_api_start', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: startTime,
      modelName
    });
    this.startGeneration('chat-completion', reqParams.messages || []);
  }

  /**
   * 记录API请求结束
   */
  logApiEnd(requestId: string, modelName: string, duration: number, status: 'success' | 'error' = 'success'): void {
    this.agentInfoWithRequestId(requestId, '模型请求接口关闭');
    this.perfLLMApi('chat-api', duration, status, modelName, status === 'success' ? 'close' : 'error');
    this.reportAgentTask('agent_llm_api_close', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      modelName
    });
  }

  /**
   * 记录首个token返回
   */
  logFirstToken(requestId: string, modelName: string, duration: number): void {
    this.perfLLMApi('chat-first-token', duration, 'success', modelName);

    // 记录到JSON性能日志
    this.jsonPerfLogger.logFirstToken(duration);

    this.reportAgentTask('agent_llm_api_frist_token', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      modelName
    });
  }

  /**
   * 记录API错误
   */
  logApiError(requestId: string, modelName: string, duration: number, error: string): void {
    this.perfLLMApi('chat-api', duration, 'error', modelName);
    this.reportAgentTask('agent_llm_api_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      error,
      modelName
    });
    // 记录带请求ID的错误信息
    this.agentInfoWithRequestId(requestId, `模型请求接口出错: ${error}`);

    // 结束generation并记录错误信息
    this.endGenerationWithMessage(`模型请求接口出错：${ErrorUtils.errorToJson(error)}`);
  }

  /**
   * 记录LLM成功结束（包含性能日志和任务报告）
   */
  logLLMSuccess(requestId: string, modelName: string, duration: number): void {
    // 记录聊天LLM成功性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-llm',
      millis: duration,
      extra4: `success`
    });

    // 记录LLM总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-llm-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务结束
    this.reportAgentTask('agent_llm_end', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      modelName
    });
  }

  /**
   * 记录LLM错误结束（包含性能日志和任务报告）
   */
  logLLMError(requestId: string, modelName: string, duration: number, error: string): void {
    // 记录聊天LLM错误性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-llm',
      millis: duration,
      extra4: `error`,
      extra6: error
    });

    // 记录LLM总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-llm-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务错误
    this.reportAgentTask('agent_llm_end_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      error,
      modelName
    });
  }

  /**
   * 记录LLM处理异常（包含错误日志、性能日志和任务报告）
   */
  logLLMProcessError(modelName: string, duration: number, error: any): void {
    // 记录错误日志
    this.agentError(`模型请求出错: ${error}`);

    const serializedError = ErrorUtils.errorToJson(error);

    // 记录聊天LLM错误性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-llm',
      millis: 1,
      extra4: `error`,
      extra6: `${serializedError}`
    });

    // 记录LLM总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-llm-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务处理错误
    this.reportAgentTask('agent_llm_process_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      ts: Date.now(),
      duration,
      error: 'api_req_failed',
      modelName
    });
  }

  /**
   * 记录错误限制达到（包含性能日志和任务报告）
   */
  logMistakeLimitReached(modelName: string, duration: number): void {
    // 记录聊天全部错误性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-all',
      millis: duration,
      extra4: `error`,
      extra6: `mistake_limit_reached`
    });

    // 记录agent总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-agent-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务错误结束
    this.reportAgentTask('agent_task_end_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      ts: Date.now(),
      duration,
      error: 'mistake_limit_reached',
      modelName
    });
  }

  /**
   * 记录任务成功完成（包含性能日志和任务报告）
   */
  logTaskCompleted(modelName: string, duration: number): void {
    // 记录聊天全部成功性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-all',
      millis: duration,
      extra4: `success`
    });

    // 记录agent总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-agent-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务成功结束
    this.reportAgentTask('agent_task_end', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      ts: Date.now(),
      duration,
      modelName
    });
  }

  /**
   * 记录消息解析错误（包含错误日志和性能日志）
   */
  logMessageParseError(requestId: string, error: any): void {
    // 记录带请求ID的错误信息
    this.agentInfoWithRequestId(requestId, '消息解析出错');

    // 记录详细错误信息
    this.agentError(`消息解析出错: ${error}`);

    const serializedError = ErrorUtils.errorToJson(error);

    // 记录性能日志
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'parse-assistant-message-error',
      millis: 1,
      extra4: `消息解析出错：${serializedError}`,
      extra6: new Error().stack || ''
    });
  }

  /**
   * 记录消息处理错误（包含错误日志和性能日志）
   */
  logMessageProcessError(requestId: string, error: any): void {
    // 记录带请求ID的错误信息
    this.agentInfoWithRequestId(requestId, `消息处理过程中出错: ${error}`);

    // 优化错误序列化，使用 serializeError 来获取完整的错误信息
    const serializedError = ErrorUtils.errorToJson(error);

    // 记录性能日志
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'message-processing-error',
      millis: 1,
      extra4: serializedError,
      extra6: new Error().stack || ''
    });
  }

  /**
   * 记录任务开始
   */
  logTaskStart(taskData: any): void {
    this.agentInfo('用户开始任务');
    this.reportUserAction({
      key: 'agent_task',
      type: 'agent_start_task',
      content: JSON.stringify(taskData)
    });
  }

  /**
   * 记录任务结束
   */
  logTaskEnd(modelName: string, duration: number, status: 'success' | 'error' = 'success'): void {
    this.perfLLMApi('agent-total', duration, status, modelName);
    this.reportAgentTask(status === 'success' ? 'agent_task_end' : 'agent_task_end_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      ts: Date.now(),
      duration,
      modelName
    });
  }

  /**
   * 记录API请求过长错误
   */
  logApiTooLongError(requestId: string, modelName: string, duration: number): void {
    // 记录聊天LLM错误性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-llm',
      millis: duration,
      extra4: `error`,
      extra6: `api_req_isTooLong`
    });

    // 记录LLM总时间性能
    this.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-llm-total',
      millis: duration,
      extra4: modelName
    });

    // 报告agent任务错误
    this.reportAgentTask('agent_llm_api_error', {
      sessionId: this.config.sessionId,
      chatId: this.config.chatId,
      requestId,
      ts: Date.now(),
      duration,
      error: 'api_req_isTooLong',
      modelName
    });
  }
}
