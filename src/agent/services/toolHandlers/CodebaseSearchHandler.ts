import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { SearchManager } from '@/indexing/SearchManager';
import { GlobalConfig } from '@/util/global';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 代码库搜索工具处理器
 */
export class CodebaseSearchHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const query: string | undefined = block.params?.query;
    const target_directories: string | undefined = block.params?.target_directories;
    const sharedMessageProps: SayTool = {
      tool: 'codebaseSearch',
      query: query,
      target_directories: typeof target_directories === 'string' ? ToolHelpers.removeClosingTag('target_directories', target_directories, block.partial) : target_directories
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!query) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('codebase_search', 'query', this.context),
            this.context.stateManager
          );
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'codebase_search'
        });
        const searchManager = new SearchManager();
        const toolLog = ToolHelpers.generateToolLog('codebase_search', this.context.loggerManager);
        toolLog.start(`${query} ${target_directories}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            query,
            target_directories
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();

        const rerankResult = await searchManager.search({
          query,
          topK: 20, // limit to 20 results
          targetDirectory: target_directories ? [target_directories] : [],
          chatHistory: this.context.messageService.getApiConversationHistory(),
          enable_rewrite: false,
          gitRepo: this.context.sessionInfo?.reqData?.projectInfo?.gitUrl || this.context.cwd,
          commit: '',
          username: this.context.sessionInfo?.reqData?.username || GlobalConfig.getConfig().getUsername(),
          enableCloudSearch: true,
          dirPath: GlobalConfig.getConfig().getRepoPath()
        });

        toolLog.end(JSON.stringify(rerankResult));
        generationCall?.end({
          output: { rerankResult }
        });
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: JSON.stringify(rerankResult)
        } satisfies SayTool);
        await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, JSON.stringify(rerankResult), this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          query,
          target_directories
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'codebase searching files',
        error,
        'codebase_search'
      );
    }
  }
}
