import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool, EditFileRequest, EditFileResponse } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import fs from 'fs/promises';
import path from 'path';
import delay from 'delay';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 编辑文件工具处理器
 */
export class EditFileHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  /**
   * 执行文件编辑操作
   */
  private async onEditFile(request: EditFileRequest): Promise<EditFileResponse> {
    if (!this.context.messenger) {
      throw new Error('Messenger not available for onEditFile');
    }
    const res = await this.context.messenger.request('assistant/agent/editFile', request);
    return res.data || { type: 'failed', content: '未返回文件修改结果' };
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const target_file: string | undefined = block.params.target_file;
    let code_edit: string | undefined = block.params.code_edit;
    let instructions: string | undefined = block.params.instructions;
    let language: string | undefined = block.params.language;


    try {
      const sharedMessageProps: SayTool = {
        tool: 'editFile',
        content: ToolHelpers.removeClosingTag('code_edit', code_edit, block.partial),
        instructions: ToolHelpers.removeClosingTag('instructions', instructions, block.partial),
        language: ToolHelpers.removeClosingTag('language', language, block.partial),
        path: target_file
      };

      if (block.partial) {
        const partialMessage = JSON.stringify(sharedMessageProps);
        // if (!block.params.requires_approval) {
        // this.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
        await this.context.messageService.say('tool', partialMessage, block.partial, undefined);
        // } else {
        //   this.removeLastPartialMessageIfExistsWithType('say', 'tool');
        //   await this.ask('tool', partialMessage, block.partial, undefined).catch(() => {});
        // }
        return;
      } else {
        if (!target_file) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError(block.name, 'target_file', this.context), this.context.stateManager);

          return;
        }
        if (!code_edit) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError(block.name, 'code_edit', this.context), this.context.stateManager);
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'edit_file'
        });
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: code_edit
        } satisfies SayTool);

        // if (!block.params.requires_approval) {
        // this.removeLastPartialMessageIfExistsWithType('ask', 'tool'); // in case the user changes auto-approval settings mid stream
        await this.context.messageService.say('tool', completeMessage, block.partial, undefined);
        await delay(3_500);
        // } else {
        //   this.removeLastPartialMessageIfExistsWithType('say', 'tool');
        //   await this.ask('tool', completeMessage, block.partial, undefined);
        //   let didApprove = true;
        //   const { askResponse, text } = await this.ask('tool', completeMessage, false);
        //   if (askResponse !== 'yesButtonClicked') {
        //     // User either sent a message or pressed reject button
        //     pushToolResult(`The user denied this operation.`);
        //     if (text) {
        //       pushAdditionalToolFeedback(text);
        //       await this.say('user_feedback', text);
        //     }
        //     this.didRejectTool = true;
        //     didApprove = false;
        //     break;
        //   } else {
        //     // User hit the approve button, and may have provided feedback
        //     if (text) {
        //       pushAdditionalToolFeedback(text);
        //       await this.say('user_feedback', text);
        //     }
        //   }
        // }

        let fileContent = '';
        try {
          const absolutePath = path.resolve(this.context.cwd, target_file);
          fileContent = await fs.readFile(absolutePath, 'utf-8');
        } catch (error) {
          // 数据只用于日志上报，不处理异常
        }
        const toolLog = ToolHelpers.generateToolLog('edit_file', this.context.loggerManager);
        toolLog.start(target_file);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            content: ToolHelpers.removeClosingTag('code_edit', code_edit, block.partial),
            instructions: ToolHelpers.removeClosingTag('instructions', instructions, block.partial),
            language: ToolHelpers.removeClosingTag('language', language, block.partial),
            path: target_file
          },
          metadata: {
            name: block.name
          }
        });
        // 调用edit_file工具
        const startToolTime = Date.now();
        const { type, content, newFile } = await this.onEditFile({
          content: ToolHelpers.removeClosingTag('code_edit', code_edit, block.partial),
          instructions: ToolHelpers.removeClosingTag('instructions', instructions, block.partial),
          language: ToolHelpers.removeClosingTag('language', language, block.partial) as any,
          path: target_file,
          lastCheckpointHash: this.context.stateManager.getState().lastCheckpointHash,
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId
        });
        toolLog.end(`${type} ${content}`);
        generationCall?.end({
          output: { type, content, newFile }
        });
        let result = '';
        if (typeof newFile === 'boolean') {
          result = JSON.stringify({ type, content, newFile });
        } else {
          result = JSON.stringify({ type, content });
        }
        ToolHelpers.pushToolResult(block, userMessageContent, content, this.context.stateManager);
        await this.context.messageService.say('edit_file_result', result);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: type === 'success' ? 'success' : 'error',
          extra6: block.name
        });
        const fileLines = fileContent.split('\n').length;
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-editFile',
          millis: Date.now() - startToolTime,
          extra4: type === 'success' ? 'success' : 'error',
          extra6: fileLines > 1000 ? 'large' : fileLines > 500 ? 'medium' : 'small'
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          resType: type,
          lines: fileContent.split('\n').length,
          newFile
        });
        await this.context.checkpointService?.saveCheckpoint();
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'editing file', error, 'edit_file');
      return;
    }
  }
} 