import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, <PERSON><PERSON>Hand<PERSON> } from '../ToolHelpers';
import { TextBlockParamVersion1, ExecuteCommandResponse } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { generate } from 'fast-glob/out/managers/tasks';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 执行命令工具处理器
 */
export class ExecuteCommandHandler implements ToolHandler {
  private maxTerminalOutputLength = 5000;
  private prefixLength = this.maxTerminalOutputLength * 0.2;
  private suffixLength = this.maxTerminalOutputLength * 0.8;
  constructor(private context: ToolHandlerContext) {
  }

  /**
   * 执行命令工具
   */
  private async executeCommandTool(
    command: string,
    is_background?: boolean,
    ignore_output?: boolean,
  ): Promise<{ userSkip: boolean; result: string; usefulResult: string }> {
    if (!this.context.messenger) {
      throw new Error('Messenger not available for executeCommandTool');
    }
    const commandRes = await this.context.messenger.request('assistant/agent/executeCommand', { command, is_background, ignore_output });
    const { userSkip, result, completed = true } = commandRes.data as ExecuteCommandResponse;
    const skipPrompt = "user skip command execution result, continue to execute the following command.\nCurrent output: ";
    const normalPrompt = "command output: ";

    if (result.length < this.maxTerminalOutputLength) {
      return {
        userSkip,
        result,
        usefulResult: `${userSkip ? skipPrompt : normalPrompt}${result}`,
      };
    } else {
      return {
        userSkip,
        result,
        usefulResult: `${userSkip ? skipPrompt : normalPrompt}${result.slice(0, this.prefixLength)}...${result.slice(-this.suffixLength)}`,
      }
    }
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const command: string | undefined = block.params.command;
    const is_background: string | undefined = block.params.is_background;
    const is_background_boolean = typeof is_background === 'boolean'
      ? is_background
      : is_background?.toLowerCase() === 'true';
    const ignore_output: string | undefined = block.params.ignore_output;
    const ignore_output_boolean = typeof ignore_output === 'boolean'
      ? ignore_output
      : ignore_output?.toLowerCase() === 'true';
    const requires_approval: string | undefined = block.params.requires_approval;
    const requires_approval_boolean = typeof requires_approval === 'boolean'
      ? requires_approval
      : requires_approval?.toLowerCase() === 'true';
    const message = JSON.stringify({
      tool: 'executeCommand',
      command: ToolHelpers.removeClosingTag('command', command, block.partial),
      is_background: is_background_boolean,
      requires_approval: requires_approval_boolean
    });
    try {
      if (block.partial) {
        await this.context.messageService.ask('command', message, block.partial).catch(() => { });
        return;
      } else {
        if (!command) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('execute_command', 'command', this.context), this.context.stateManager);
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'execute_command'
        });
        const startAskTime = Date.now();
        const { didApprove, text } = await ToolHelpers.askCommandApproval(
          block,
          userMessageContent,
          this.context.messageService,
          this.context.stateManager,
          'command',
          message
        );

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request_ope',
          type: 'execute_command',
          subType: didApprove ? 'tool_run' : 'tool_cancel'
        });
        this.context.loggerManager.reportUserAction({
          key: 'agent_task',
          type: 'tool_use_ask',
          content: JSON.stringify({
            toolName: block.name,
            sessionId: this.context.stateManager.getState().sessionId,
            chatId: this.context.stateManager.getState().chatId,
            ts: Date.now(),
            duration: Date.now() - startAskTime,
            didApprove,
            requiresApproval: requires_approval
          })
        });
        if (!didApprove) {
          return;
        }
        await this.context.messageService.say('command_output', '', true);
        const toolLog = ToolHelpers.generateToolLog('execute_command', this.context.loggerManager);
        toolLog.start(`${command} ${is_background_boolean} ${ignore_output_boolean}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            command,
            editCommand: text,
            is_background,
            requires_approval
          },
          metadata: {
            name: block.name
          }
        });
        // 执行命令
        const startToolTime = Date.now();
        const resCommand = text || command;
        const { userSkip, result, usefulResult } = await this.executeCommandTool(
          resCommand,
          is_background_boolean,
          ignore_output_boolean
        );

        toolLog.end(result);
        generationCall?.end({
          output: { result }
        });
        // VS Code 的文件系统监听器有限制：
        // - 只能监听到用户手动的文件操作
        // - 无法自动检测程序创建/删除的文件
        // - 需要手动触发更新来保持同步
        ToolHelpers.pushToolResult(block, userMessageContent, usefulResult, this.context.stateManager);
        await this.context.messageService.say('command_output', result, false);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          command,
          editCommand: text,
          is_background,
          requires_approval
        });
        await this.context.checkpointService?.saveCheckpoint();
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'executing command', error, 'execute_command');
    }
  }
} 