import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';

/**
 * 保存记忆请求接口
 */
interface SaveMemoryRequest {
  username: string;
  type: string;
  content: string;
  summary?: string;
  tags?: string[];
  project?: string;
  chatid?: string;
  metadata?: {
    source?: string;
    date_observed?: string;
    frequency?: string;
    [key: string]: any;
  };
}

/**
 * 保存记忆 API 响应接口
 */
interface SaveMemoryResponse {
  id: number;
  username: string;
  type: string;
  content: string;
  summary?: string;
  tags?: string[];
  metadata?: {
    [key: string]: any;
  };
  project?: string;
  chatid?: string;
  status: number;
  created_at: string;
  updated_at: string;
  created: boolean;
}

/**
 * 记忆保存工具处理器
 */
export class SaveMemoryHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;
    const type: string | undefined = block.params?.type;
    const content: string | undefined = block.params?.content;
    const summary: string | undefined = block.params?.summary;
    const tags: string[] | undefined = block.params?.tags
      ? Array.isArray(block.params?.tags)
        ? block.params?.tags
        : block.params?.tags.split(' ')
      : [];
    const metadata: any | undefined = block.params?.metadata;

    const sharedMessageProps: SayTool = {
      tool: 'saveMemory',
      content: content || ''
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        // 参数验证
        if (!content) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('save_memory', 'content', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'save_memory'
        });

        const toolLog = ToolHelpers.generateToolLog('save_memory', this.context.loggerManager);
        toolLog.start(`保存记忆: ${content.substring(0, 100)}...`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            type,
            content,
            summary,
            tags,
            metadata
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();

        try {
          // 从 GlobalConfig 获取 username 和 project
          const username = GlobalConfig.getConfig().getUsername() || '';
          const project = GlobalConfig.getConfig().getRepoPath() || '';

          // 构建请求体
          const requestBody: SaveMemoryRequest = {
            username,
            type: type || 'event',
            content,
            project
          };

          // 添加可选字段
          if (summary) requestBody.summary = summary;
          if (tags && Array.isArray(tags)) requestBody.tags = tags;
          requestBody.chatid = this.context.stateManager.chatId; //获取会话 id
          if (metadata) requestBody.metadata = metadata;

          // 使用 Api 类发送 HTTP 请求
          const api = new Api();
          const { data: result } = await api.post<SaveMemoryResponse>(
            '/nodeapi/indexing/memory',
            JSON.stringify(requestBody)
          );
          const resultString = JSON.stringify(result, null, 2);

          toolLog.end(resultString);
          generationCall?.end({
            output: { result }
          });

          const completeMessage = JSON.stringify({
            ...sharedMessageProps,
            content: resultString
          } satisfies SayTool);

          await this.context.messageService.say('tool', completeMessage, false);

          // 格式化保存结果以便更好地显示
          let formattedResult = '';
          // 判断保存是否成功：检查 created 字段或 status 为 0，或者兼容旧版本的 success 字段
          const isSuccess = result.created || result.status === 0;

          if (isSuccess) {
            formattedResult = `✅ 记忆保存成功!\n\n`;
            if (result.id) {
              formattedResult += `记忆ID: ${result.id}\n`;
            }
            if (result.username) {
              formattedResult += `用户: ${result.username}\n`;
            }
            formattedResult += `类型: ${result.type || type || 'event'}\n`;
            formattedResult += `内容: ${result.content || content}\n`;
            if (result.summary || summary) {
              formattedResult += `摘要: ${result.summary || summary}\n`;
            }
            if (result.tags && result.tags.length > 0) {
              formattedResult += `标签: ${result.tags.join(', ')}\n`;
            } else if (tags && tags.length > 0) {
              formattedResult += `标签: ${tags.join(', ')}\n`;
            }
            if (result.project || project) {
              formattedResult += `项目: ${result.project || project}\n`;
            }
            if (result.chatid) {
              formattedResult += `会话ID: ${result.chatid}\n`;
            }
            if (result.created_at) {
              formattedResult += `创建时间: ${result.created_at}\n`;
            }
            if (result.metadata) {
              formattedResult += `元数据: ${JSON.stringify(result.metadata, null, 2)}\n`;
            }
          } else {
            formattedResult = `❌ 记忆保存失败\n\n`;
            if (result.status !== undefined) {
              formattedResult += `状态码: ${result.status}\n`;
            }
            formattedResult += `\n原始结果:\n${resultString}`;
          }

          ToolHelpers.pushToolResult(block, userMessageContent, formattedResult, this.context.stateManager);
        } catch (fetchError: any) {
          const errorMessage = `保存记忆时发生错误: ${fetchError.message}`;
          toolLog.end(errorMessage);
          generationCall?.end({
            output: { error: errorMessage }
          });

          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        }

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          username: GlobalConfig.getConfig().getUsername() || '',
          type,
          content: content?.substring(0, 100),
          summary: summary?.substring(0, 50),
          tags: tags?.join(','),
          project: GlobalConfig.getConfig().getRepoPath() || ''
        });

        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'saving memory',
        error,
        'save_memory'
      );
    }
  }
}
