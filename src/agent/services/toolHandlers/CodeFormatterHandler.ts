import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, <PERSON>lHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 代码格式化工具处理器
 */
export class CodeFormatterHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  /**
   * 执行代码格式化工具
   */
  private async executeCodeFormatTool(
    paths: string[]
  ): Promise<{ type: 'success' | 'failed', result: { successFileList?: string[], failedFileList?: string[] } }> {
    if (!this.context.messenger) {
      throw new Error('Messenger not available for codeFormatTool');
    }

    const formatRes = await this.context.messenger.request('assistant/agent/codeFormat', { format_file_paths: paths });
    const { type, result } = formatRes.data as { type: 'success' | 'failed', result: { successFileList?: string[], failedFileList?: string[] } };

    return {
      type,
      result,
    };
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const pathsString: string | object | undefined = block.params.format_file_paths;
    const sharedMessageProps = {
      tool: 'code_formatter',
      format_file_paths: pathsString,
    }

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify(sharedMessageProps);
        await this.context.messageService.ask('code_formatter', partialMessage, block.partial).catch(() => { });
        return;
      } else {
        let paths: string[] = [];
        if (Array.isArray(pathsString)) {
          paths = pathsString;
        } else if (typeof pathsString === 'string') {
          try {
            const parsed = JSON.parse(pathsString);
            paths = Array.isArray(parsed) ? parsed : [];
          } catch {
            paths = [];
          }
        }
        const completeMessage = JSON.stringify({ ...sharedMessageProps, format_file_paths: paths });
        if (!paths || !Array.isArray(paths) || paths.length === 0) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('code_formatter', 'format_file_paths', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'code_formatter'
        });

        const startAskTime = Date.now();
        const didApprove = await ToolHelpers.askApproval(
          block,
          userMessageContent,
          this.context.messageService,
          this.context.stateManager,
          'code_formatter',
          completeMessage
        );

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request_ope',
          type: 'code_formatter',
          subType: didApprove ? 'tool_run' : 'tool_cancel'
        });

        this.context.loggerManager.reportUserAction({
          key: 'agent_task',
          type: 'tool_use_ask',
          content: JSON.stringify({
            toolName: block.name,
            sessionId: this.context.stateManager.getState().sessionId,
            chatId: this.context.stateManager.getState().chatId,
            ts: Date.now(),
            duration: Date.now() - startAskTime,
            didApprove,
            requiresApproval: true
          })
        });

        if (!didApprove) {
          return;
        }

        await this.context.messageService.say('code_formatter_result', '', true);
        const toolLog = ToolHelpers.generateToolLog('code_formatter', this.context.loggerManager);
        toolLog.start(`格式化文件: ${paths.join(', ')}`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            format_file_paths: paths,
          },
          metadata: {
            name: block.name
          }
        });

        // 执行代码格式化
        const startToolTime = Date.now();
        const { type, result } = await this.executeCodeFormatTool(paths);

        // 生成结果描述
        const successCount = result.successFileList?.length || 0;
        const failedCount = result.failedFileList?.length || 0;

        let resultMessage = '';
        if (type === 'success') {
          if (failedCount === 0) {
            resultMessage = `Successfully formatted ${successCount} file(s): ${result.successFileList?.join(', ') || ''}`;
          } else {
            resultMessage = `Formatting completed: ${successCount} successful, ${failedCount} failed.\nSuccessful: ${result.successFileList?.join(', ') || ''}\nFailed: ${result.failedFileList?.join(', ') || ''}`;
          }
        } else {
          resultMessage = `Formatting failed: ${result.failedFileList?.join(', ') || ''}`;
        }

        toolLog.end(resultMessage);
        generationCall?.end({
          output: { type, result }
        });

        ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);
        await this.context.messageService.say('code_formatter_result', resultMessage, false);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          paths: pathsString,
        });
        await this.context.checkpointService?.saveCheckpoint();
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'formatting code', error, 'code_formatter');
    }
  }
}
