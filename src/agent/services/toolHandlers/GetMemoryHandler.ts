import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';

/**
 * 记忆项元数据接口
 */
interface MemoryMetadata {
  source?: string;
  date_observed?: string;
  frequency?: string;
  [key: string]: any;
}

/**
 * 记忆项详情接口
 */
interface MemoryDetail {
  memory_id: number;
  username: string;
  type: string;
  content: string;
  original_content: string;
  summary?: string;
  tags?: string[];
  metadata?: MemoryMetadata;
  project?: string;
  chunk_id: string;
  chunk_index: number;
  is_chunked: boolean;
  total_chunks: number;
  created_at: string;
  updated_at: string;
}

/**
 * 获取记忆详情 API 响应接口
 */
interface GetMemoryResponse {
  memory: MemoryDetail;
}

/**
 * 记忆详情获取工具处理器
 */
export class GetMemoryHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const memory_id: string | undefined = block.params?.memory_id;

    const sharedMessageProps: SayTool = {
      tool: 'getMemory',
      memory_id: memory_id || ''
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        // 参数验证
        if (!memory_id) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('get_memory', 'memory_id', this.context),
            this.context.stateManager
          );
          return;
        }

        // 验证 memory_id 是否为有效数字
        const memoryIdNum = parseInt(memory_id, 10);
        if (isNaN(memoryIdNum) || memoryIdNum <= 0) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            '错误: memory_id 必须是一个有效的正整数',
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'get_memory'
        });

        const toolLog = ToolHelpers.generateToolLog('get_memory', this.context.loggerManager);
        toolLog.start(`获取记忆详情: ${memory_id}`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            memory_id: memoryIdNum
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();

        try {
          // 从 GlobalConfig 获取 username
          const username = GlobalConfig.getConfig().getUsername() || '';

          // 构建查询参数
          const queryParams = new URLSearchParams();
          queryParams.append('username', username);

          // 使用 Api 类发送 HTTP 请求
          const api = new Api();
          const { data: result } = await api.get<GetMemoryResponse>(
            `/nodeapi/indexing/memory/${memoryIdNum}?${queryParams.toString()}`
          );
          const resultString = JSON.stringify(result, null, 2);

          toolLog.end(resultString);
          generationCall?.end({
            output: { result }
          });

          const completeMessage = JSON.stringify({
            ...sharedMessageProps,
            content: resultString
          } satisfies SayTool);

          await this.context.messageService.say('tool', completeMessage, false);

          // 格式化记忆详情以便更好地显示
          let formattedResult = '';
          if (result.memory) {
            const memory = result.memory;
            formattedResult = `记忆详情 (ID: ${memory.memory_id}):\n\n`;
            formattedResult += `类型: ${memory.type}\n`;
            formattedResult += `内容: ${memory.content}\n`;
            if (memory.original_content && memory.original_content !== memory.content) {
              formattedResult += `原始内容: ${memory.original_content}\n`;
            }
            if (memory.summary) {
              formattedResult += `摘要: ${memory.summary}\n`;
            }
            if (memory.tags && memory.tags.length > 0) {
              formattedResult += `标签: ${memory.tags.join(', ')}\n`;
            }
            if (memory.project) {
              formattedResult += `项目: ${memory.project}\n`;
            }
            if (memory.metadata) {
              if (memory.metadata.date_observed) {
                formattedResult += `观察日期: ${memory.metadata.date_observed}\n`;
              }
              if (memory.metadata.source) {
                formattedResult += `来源: ${memory.metadata.source}\n`;
              }
              if (memory.metadata.frequency) {
                formattedResult += `频率: ${memory.metadata.frequency}\n`;
              }
            }
            formattedResult += `用户: ${memory.username}\n`;
            formattedResult += `创建时间: ${memory.created_at}\n`;
            formattedResult += `更新时间: ${memory.updated_at}\n`;
            if (memory.is_chunked) {
              formattedResult += `分块信息: ${memory.chunk_index + 1}/${memory.total_chunks}\n`;
              formattedResult += `分块ID: ${memory.chunk_id}\n`;
            }
          } else {
            formattedResult = `未找到记忆 ID ${memory_id} 的详情\n\n原始结果:\n${resultString}`;
          }

          ToolHelpers.pushToolResult(block, userMessageContent, formattedResult, this.context.stateManager);
        } catch (fetchError: any) {
          const errorMessage = `获取记忆详情时发生错误: ${fetchError.message}`;
          toolLog.end(errorMessage);
          generationCall?.end({
            output: { error: errorMessage }
          });

          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        }

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          username: GlobalConfig.getConfig().getUsername() || '',
          memory_id: memoryIdNum
        });

        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'getting memory detail',
        error,
        'get_memory'
      );
    }
  }
}
