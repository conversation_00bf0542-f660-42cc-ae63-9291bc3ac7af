import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type';
import { EditFileResponse, SayTool } from '../../types/type';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 搜索替换工具处理器
 */
export class WriteToFileHandlerV2 implements ToolHandler {

    constructor(private context: ToolHandlerContext) { }
    /**
     * 处理编辑结果的通用方法
     */
    private async handleEditResult(
        data: EditFileResponse | undefined,
        block: ToolUse,
        userMessageContent: TextBlockParamVersion1[],
        validRelPath: string
    ): Promise<void> {
        this.context.messageService.say('edit_file_result', JSON.stringify(data));

        if (data?.noModified) {
            ToolHelpers.pushToolResult(
                block,
                userMessageContent,
                `No changes needed for '${validRelPath}'`,
                this.context.stateManager
            );
            return;
        }

        if (data?.type === 'success') {
            // 将修改的文件路径添加到chatModifiedFilelist中（相对路径），并去重
            const currentModifiedFiles = this.context.stateManager.getState().chatModifiedFilelist;
            const relRecorded = validRelPath;
            if (!currentModifiedFiles.includes(relRecorded)) {
                this.context.stateManager.updateState({
                    chatModifiedFilelist: [...currentModifiedFiles, relRecorded]
                });
            }

            const resultMessage = [
                `The updated content has been successfully saved to ${validRelPath}.\n\n`,
                `Please note:\n`,
                `1. You do not need to re-write the file with these changes, as they have already been applied.\n`,
                `2. Proceed with the task using the updated file content as the new baseline.\n`,
                `3. If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.`
            ].join('');
            ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);
        } else {
            await ToolHelpers.handleError(
                block,
                userMessageContent,
                this.context,
                null,
                'replace in file',
                new Error(data?.content || ''),
                'replace_in_file'
            );
        }

        // 保存检查点
        await this.context.checkpointService?.saveCheckpoint();
    }

    async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
        let generationCall: LangfuseGenerationClient | null | undefined = null;

        const relPath: string | undefined = block.params.path;
        const content: string | undefined = block.params.content || '';
        const absolutePath = getReadablePath(this.context.cwd, relPath);

        try {
            const sharedMessageProps: SayTool = {
                tool: 'editFile',
                path: absolutePath,
                content,
                tool_version: 'v2'
            };

            // Handle partial tool use
            if (block.partial) {
                await this.context.messageService
                    .say('tool', JSON.stringify(sharedMessageProps), block.partial)
                    .catch(() => { });
                return;
            }

            // Validate required parameters
            if (!relPath) {
                this.context.stateManager.updateState({
                    consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
                });
                const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('write_to_file', 'path', this.context);
                ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
                return;
            }


            const startToolTime = Date.now();
            const fileExists = await fileExistsAtPath(absolutePath);

            // Reset consecutive mistakes since all validations passed
            this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });


            this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
            this.context.loggerManager.reportUserAction({
                key: 'agent_tools_request',
                type: 'write_to_file'
            });
            generationCall = this.context.loggerManager.getTrace()?.generation({
                name: 'tool_call',
                input: {
                    path: ToolHelpers.removeClosingTag('path', relPath),
                    newContent: content,
                },
                metadata: {
                    name: block.name
                }
            });
            await this.context.messageService.say('tool', JSON.stringify({ ...sharedMessageProps, content }), block.partial);

            if (!this.context.messenger) {
                throw new Error('Messenger not available for writeToFile');
            }

            const { data } = await this.context.messenger.request('assistant/agent/replaceInFile', {
                path: relPath || '',
                searchReplaceInfo: [{ search: '', replace: content }],
                sessionId: this.context.stateManager.getState().sessionId,
                chatId: this.context.stateManager.getState().chatId
            })


            // 使用统一的处理方法
            await this.handleEditResult(data, block, userMessageContent, relPath);

            generationCall?.end({
                output: { type: data?.type, content: data?.content }
            });

            this.context.loggerManager.perf({
                namespace: ASSISTANT_NAMESPACE,
                subtag: 'kwaipilot-ide-agent-chat-tool',
                millis: Date.now() - startToolTime,
                extra4: data?.type === 'success' ? 'success' : 'error',
                extra6: block.name
            });
        } catch (error: any) {
            await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'writing file', error, 'write_to_file');
            return;
        }
    }
}