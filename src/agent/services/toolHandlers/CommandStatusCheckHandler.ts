import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { CommandStatusCheckResponse, TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';



/**
 * 列出文件工具处理器
 */
export class CommandStatusCheckHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const check_duration: string | undefined = block.params.check_duration;
    const check_duration_number = check_duration ? parseInt(check_duration) : 5;

    try {
      if (block.partial) {
        return;
      } else {
        if (!check_duration) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('command_status_check', 'check_duration', this.context), this.context.stateManager);
          return;
        }
        const skipCheck = async () => {
          await this.context.messageService.say('command_status_check_result', JSON.stringify({ status: "skip", output: "" }), false);
        }

        this.context.stateManager.updateState({ tasksToBeHandled: [...this.context.stateManager.getState().tasksToBeHandled, skipCheck] });

        await this.context.messageService.say('command_status_check', JSON.stringify({
          tool: 'commandStatusCheck',
          check_duration: check_duration_number
        }), block.partial).catch(() => { });

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'command_status_check'
        });

        const toolLog = ToolHelpers.generateToolLog('command_status_check', this.context.loggerManager);
        toolLog.start(`check_duration: ${check_duration_number}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            check_duration: check_duration_number
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();
        const { status, output } = await this.commandStatusCheckTool(check_duration_number);

        await this.context.messageService.say('command_status_check_result', JSON.stringify({ status, output }), false);

        this.context.stateManager.updateState({ tasksToBeHandled: this.context.stateManager.getState().tasksToBeHandled.filter(f => f !== skipCheck) })
        toolLog.end(`status: ${status}, output: ${output}`);
        generationCall?.end({
          output: { status, output }
        });

        let resultMessage = '';
        switch (status) {
          case 'success':
            resultMessage = `Command status check completed. New output:\n${output}`;
            break;
          case 'skip':
            resultMessage = `Command status check skipped by user. Proceeding with current output without further tool checks.`;
            break;
          case 'error':
            resultMessage = `Command status check failed. Error: ${output}`;
        }

        ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          check_duration: check_duration_number,
          status,
          outputLength: output.length
        })
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'checking command status', error, 'command_status_check');
      return;
    }

  }
  async commandStatusCheckTool(
    check_duration: number
  ): Promise<CommandStatusCheckResponse> {
    // 创建取消监听 Promise
    const cancelPromise = new Promise<{ status: 'skip'; output: string }>((resolve) => {
      this.context.messageService.ask("command_status_check", "", false).then((response) => {
        if (response.askResponse === 'noButtonClicked') {
          resolve({
            status: 'skip',
            output: ''
          });
        }
      });
    });


    // 创建状态检查 Promise
    const statusCheckPromise = this.context.messenger.request('assistant/agent/commandStatusCheck', {
      check_duration
    }).then((commandRes) => {
      const { status, output } = commandRes.data as CommandStatusCheckResponse;
      return { status, output };
    });

    try {
      // 使用 Promise.race 竞争执行
      const result = await Promise.race([statusCheckPromise, cancelPromise]);
      return result;
    } catch (error) {
      // 确保在出错时恢复原始的 updateAskResponse 方法
      // 注意：这里可能需要更健壮的恢复机制
      throw error;
    }
  }
}