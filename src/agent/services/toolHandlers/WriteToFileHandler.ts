import { ToolUse, ToolUseName } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type.d';
import { SayTool } from '../../types/type';
import { getReadablePath } from '@/util/path';
import { fileExistsAtPath } from '@/util/fs';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import path from 'path';
import delay from 'delay';
import { LangfuseGenerationClient } from 'langfuse';
import { GitManager } from '@/index-manager/GitManager';
import { getRelativePath } from '@/util/git-path';

/**
 * 写文件工具处理器
 */
export class WriteToFileHandler implements ToolHandler {
  private gitManager: GitManager;

  constructor(private context: ToolHandlerContext) {
    this.gitManager = new GitManager();
  }

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const relPath: string | undefined = block.params.path;
    let newContent: string | undefined = block.params.content || '';

    // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
    // if (newContent.startsWith('```')) {
    //   // agent handles cases where it includes language specifiers like ```python ```js
    //   newContent = newContent.split('\n').slice(1).join('\n').trim();
    // }

    // if (newContent.endsWith('```')) {
    //   newContent = newContent.split('\n').slice(0, -1).join('\n').trim();
    // }

    const sharedMessageProps: SayTool = {
      tool: 'editFile',
      path: getReadablePath(this.context.cwd, ToolHelpers.removeClosingTag('path', relPath)),
      content: newContent,
      tool_version: 'v2'
    };

    try {
      if (block.partial) {
        // update gui message
        const partialMessage = JSON.stringify(sharedMessageProps);
        await this.context.messageService.say('tool', partialMessage, block.partial).catch(() => { });
        return;
      } else {
        if (!relPath) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('write_to_file', 'path', this.context);
          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
          return;
        }

        // if (!newContent) {
        //   this.context.stateManager.updateState({
        //     consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        //   });
        //   const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('write_to_file', 'content', this.context);
        //   ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        //   return;
        // }

        // Determine if the path is outside the workspace
        const fullPath = relPath ? path.resolve(this.context.cwd, ToolHelpers.removeClosingTag('path', relPath)) : '';
        const fileExists = await fileExistsAtPath(fullPath);
        // if (fileExists) {
        //   this.context.stateManager.updateState({
        //     consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
        //   });
        //   const formattedError = `File has existed at path: ${fullPath}\n You can't use this tool to edit a file that existed. Use the replace_in_file tool to edit existing files instead.`;
        //   ToolHelpers.pushToolResult(block, userMessageContent, formattedError, this.context.stateManager);
        //   return;
        // }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'write_to_file'
        });
        const startToolTime = Date.now();

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: ToolHelpers.removeClosingTag('path', relPath),
            newContent
          },
          metadata: {
            name: block.name
          }
        });

        await this.context.messageService.say(
          'tool',
          JSON.stringify({ ...sharedMessageProps, content: newContent }),
          block.partial
        );
        await delay(1000); // wait for diff view to update

        if (!this.context.messenger) {
          throw new Error('Messenger not available for writeToFile');
        }
        const toolLog = ToolHelpers.generateToolLog('write_to_file', this.context.loggerManager);
        toolLog.start(JSON.stringify({ ...sharedMessageProps, content: newContent }));
        const { data } = await this.context.messenger.request('assistant/agent/writeToFile', {
          path: relPath,
          newFile: !fileExists,
          content: newContent,
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId
        });
        toolLog.end(JSON.stringify(data));
        this.context.messageService.say('edit_file_result', JSON.stringify(data));

        // Report code generation
        if (this.context.agentManager) {
          const absolutePath = path.resolve(this.context.cwd, relPath);
          // 获取文件相对于 git 仓库根目录的路径
          const gitRelativePath = await getRelativePath(absolutePath, this.context.cwd);

          await this.context.agentManager.reportGenerateCode([
            {
              filePath: relPath,
              replace: newContent,
              search: '',
              gitRelativePath: gitRelativePath || undefined
            }
          ]);
        }
        if (data?.type === 'success') {
          const resultMessage = [
            `The content has been successfully saved to ${path}.\n`,
          ].join('');
          ToolHelpers.pushToolResult(block, userMessageContent, resultMessage, this.context.stateManager);
        } else {
          await ToolHelpers.handleError(
            block,
            userMessageContent,
            this.context,
            null,
            'write_to_file',
            new Error(data?.content || ''),
            'write_to_file'
          );
        }


        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          contentLength: newContent.length,
          noModified: !!data?.noModified,
          lines: newContent.split('\n').length,
          type: data?.type
        });

        generationCall?.end({
          output: { type: data?.type, content: data?.content }
        });

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: data?.type === 'success' ? 'success' : 'error',
          extra6: block.name
        });

        await this.context.checkpointService?.saveCheckpoint();
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'writing file',
        error,
        'write_to_file'
      );
      return;
    }
  }
}