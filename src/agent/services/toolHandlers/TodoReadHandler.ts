import { ToolUse } from '@/agent/types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHelpers } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1, TodoItem, TodoWriteContent } from '@/agent/types/type';
import { LangfuseGenerationClient } from 'langfuse';
import delay from 'delay';
import { ASSISTANT_NAMESPACE } from '@/util/const';

export class TodoReadHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    try {
      const sharedMessageProps: SayTool = {
        tool: 'readTodo'
      };
      if (block.partial) {
        const partialMessage = JSON.stringify(sharedMessageProps);
        await this.context.messageService.say('tool', partialMessage, block.partial, undefined);
        return;
      } else {
        // 找到上一条 writeTodo 结果
        const previousTodoMessage = [...this.context.stateManager.localMessages]
          .reverse()
          .find(
            (v) => !v.partial && v.type === 'say' && v.say === 'tool' && JSON.parse(v.text || '{}').tool === 'writeTodo'
          );

        if (!previousTodoMessage) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateInvalidParamError(block.name, 'TodoList not found', this.context),
            this.context.stateManager
          );
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        const previousTodo: TodoWriteContent | undefined = previousTodoMessage
          ? JSON.parse(JSON.parse(previousTodoMessage.text || '{}').content)
          : undefined;

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'todoRead'
        });

        const completeMessage = {
          ...sharedMessageProps,
          content: JSON.stringify(previousTodo)
        } satisfies SayTool;

        await this.context.messageService.say('tool', JSON.stringify(completeMessage), block.partial, undefined);

        await delay(3_500);

        const toolLog = ToolHelpers.generateToolLog('writeTodo', this.context.loggerManager);
        toolLog.start(JSON.stringify(completeMessage));
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {},
          output: previousTodo,
          metadata: {
            name: block.name
          }
        });
        // 调用edit_file工具
        const startToolTime = Date.now();
        toolLog.end('');
        generationCall?.end();
        const result = previousTodo?.todos?.map((todo) => `- ${todo.status}(id:${todo.id}) ${todo.content}`).join('\n');
        ToolHelpers.pushToolResult(block, userMessageContent, result || '', this.context.stateManager);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          previousTodo
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'todo read',
        error,
        'read_todo'
      );
      return;
    }
  }
}
