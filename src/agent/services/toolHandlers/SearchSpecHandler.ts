import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';

/**
 * 规格项元数据接口
 */
interface SpecMetadata {
  source?: string;
  date_observed?: string;
  frequency?: string;
  [key: string]: any;
}

/**
 * 规格项接口
 */
interface SpecItem {
  memory_id: number;
  username: string;
  type: string;
  content: string;
  original_content: string;
  summary?: string;
  tags?: string[];
  metadata?: SpecMetadata;
  project?: string;
  chunk_id: string;
  chunk_index: number;
  is_chunked: boolean;
  total_chunks: number;
  score: number;
  created_at: string;
  updated_at: string;
}

/**
 * 搜索规格 API 响应接口
 */
interface SearchSpecResponse {
  items: SpecItem[];
  total: number;
  size: number;
}
const memTypes = ['spec_requirement', 'spec_design'];

/**
 * 规格检索工具处理器
 */
export class SearchSpecHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const text: string | undefined = block.params?.text;
    const sizeParam: string | undefined = block.params?.size;
    const size: number | undefined = sizeParam ? parseInt(sizeParam, 10) : undefined;

    const sharedMessageProps: SayTool = {
      tool: 'searchSpec',
      query: text || '',
      mem_type: memTypes
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        // 参数验证
        if (!text) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('search_spec', 'text', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'search_spec'
        });

        const toolLog = ToolHelpers.generateToolLog('search_spec', this.context.loggerManager);
        toolLog.start(`搜索规格: ${text}`);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            mem_type: memTypes,
            text,
            size
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();

        try {
          // 从 GlobalConfig 获取 username 和 project
          const username = GlobalConfig.getConfig().getUsername() || '';
          const project = GlobalConfig.getConfig().getRepoPath() || '';

          // 构建查询参数，固定 mem_type 为 ['spec']
          const queryParams = new URLSearchParams();
          queryParams.append('username', username);

          // 处理 mem_type 数组格式

          memTypes.forEach((type) => {
            queryParams.append('mem_type', type);
          });

          if (text) queryParams.append('text', encodeURIComponent(text));
          if (size !== undefined) queryParams.append('size', size.toString());
          if (project) queryParams.append('project', project);

          // 使用 Api 类发送 HTTP 请求
          const api = new Api();
          const { data: result } = await api.get<SearchSpecResponse>(
            `/nodeapi/indexing/memory/search-es?${queryParams.toString()}`
          );
          const resultString = JSON.stringify(result, null, 2);

          toolLog.end(resultString);
          generationCall?.end({
            output: { result }
          });

          const completeMessage = JSON.stringify({
            ...sharedMessageProps,
            content: resultString
          } satisfies SayTool);

          await this.context.messageService.say('tool', completeMessage, false);

          // 格式化搜索结果以便更好地显示
          let formattedResult = '';
          if (result.items && Array.isArray(result.items)) {
            formattedResult = `找到 ${result.total} 条规格:\n\n`;
            result.items.forEach((item: SpecItem, index: number) => {
              formattedResult += `${index + 1}. [规格] ${item.content}\n`;
              if (item.summary) {
                formattedResult += `   摘要: ${item.summary}\n`;
              }
              if (item.tags && item.tags.length > 0) {
                formattedResult += `   标签: ${item.tags.join(', ')}\n`;
              }
              if (item.project) {
                formattedResult += `   项目: ${item.project}\n`;
              }
              if (item.metadata && item.metadata.date_observed) {
                formattedResult += `   日期: ${item.metadata.date_observed}\n`;
              }
              formattedResult += `   得分: ${item.score}\n`;
              formattedResult += `   规格ID: ${item.memory_id}\n\n`;
            });
          } else {
            formattedResult = `未找到相关规格\n\n原始结果:\n${resultString}`;
          }

          ToolHelpers.pushToolResult(block, userMessageContent, formattedResult, this.context.stateManager);
        } catch (fetchError: any) {
          const errorMessage = `搜索规格时发生错误: ${fetchError.message}`;
          toolLog.end(errorMessage);
          generationCall?.end({
            output: { error: errorMessage }
          });

          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
        }

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          username: GlobalConfig.getConfig().getUsername() || '',
          mem_type: memTypes,
          text: text?.substring(0, 100),
          size,
          project: GlobalConfig.getConfig().getRepoPath() || ''
        });

        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'searching spec',
        error,
        'search_spec'
      );
    }
  }
}
