import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { regexSearchFiles } from '../../tools/ripgrep';
import { getReadablePath } from '@/util/path';
import path from 'path';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * Grep搜索工具处理器
 */
export class GrepSearchHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const relDirPath: string | undefined = block.params.path;
    const regex: string | undefined = block.params.regex;
    const filePattern: string | undefined = block.params.file_pattern;
    const sharedMessageProps: SayTool = {
      tool: 'grepSearch',
      path: getReadablePath(this.context.cwd, ToolHelpers.removeClosingTag('path', relDirPath, block.partial)),
      regex: ToolHelpers.removeClosingTag('regex', regex, block.partial),
      filePattern: ToolHelpers.removeClosingTag('file_pattern', filePattern, block.partial)
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!relDirPath) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('grep_search', 'path', this.context), this.context.stateManager);
          return;
        }
        if (!regex) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('grep_search', 'regex', this.context), this.context.stateManager);
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'grep_search'
        });
        const absolutePath = path.resolve(this.context.cwd, relDirPath);
        const toolLog = ToolHelpers.generateToolLog('grep_search', this.context.loggerManager);
        toolLog.start(`${absolutePath} ${regex} ${filePattern}`);
         generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: absolutePath,
            regex,
            filePattern
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        const results = await regexSearchFiles(this.context.cwd, absolutePath, regex, filePattern);
        toolLog.end(results);
        generationCall?.end({
          output: { results }
        });
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          content: results
        } satisfies SayTool);
        await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, results, this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          path: absolutePath,
          regex,
          filePattern
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'grep searching files', error, 'grep_search');
    }
  }
} 