import { ToolUse } from '../../types/message';
import { <PERSON>lHelpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { formatResponse } from '@/agent/prompt/responses';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 询问后续问题工具处理器
 */
export class AskFollowupQuestionHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;
    const question: string | undefined = block.params?.question;
    try {
      if (block.partial) {
        await this.context.messageService.ask('followup', ToolHelpers.removeClosingTag('question', question), block.partial).catch(() => { });
        return;
      } else {
        if (!question) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('ask_followup_question', 'question', this.context), this.context.stateManager);
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'ask_followup_question'
        });
        this.context.loggerManager.reportAgentTask('agent_task_end', {
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId,
          ts: Date.now(),
          duration: Date.now() - this.context.stateManager.getState().startTaskTime,
          modelName: this.context.stateManager.modelConfig.model
        });
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            question
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        const { text } = await this.context.messageService.ask('followup', question, false);
        await this.context.messageService.say('user_feedback', text ?? '');
        generationCall?.end({
          output: { text }
        });
        ToolHelpers.pushToolResult(block, userMessageContent, formatResponse.toolResult(`<answer>\n${text}\n</answer>`), this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        this.context.loggerManager.reportUserAction({
          key: 'agent_task',
          type: 'tool_use',
          content: JSON.stringify({
            toolName: block.name,
            sessionId: this.context.stateManager.getState().sessionId,
            chatId: this.context.stateManager.getState().chatId,
            ts: Date.now(),
            duration: Date.now() - startToolTime,
            params: {
              question
            }
          })
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'asking question', error, 'ask_followup_question');
    }
  }
} 