import { ToolUse } from '../../types/message';
import { <PERSON>l<PERSON>elpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1, ToolResponse } from '../../types/type.d';
import { FigmaClient } from '../../tools/parse-figma/figma-request';
import { parseUrl } from '@/agent/tools/parse-figma';
import axios from 'axios';
import { uploadBuffer } from '@/util/upload';
import { GlobalConfig } from '@/util/global';
import { LangfuseGenerationClient } from 'langfuse';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { FigmaTokenHelper } from './FigmaTokenHelper';


/**
 * 从 URL 下载图片并返回 buffer
 */
async function downloadImageAsBuffer(imageUrl: string): Promise<Buffer> {
    try {
      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      });
      
      return Buffer.from(response.data);
    } catch (error) {
      throw new Error(`Failed to download image from ${imageUrl}: ${error}`);
    }
  }
/**
 * Figma 预览图片获取工具处理器
 */
export class GetFigmaPreviewImageHandler implements ToolHandler {
    constructor(private context: ToolHandlerContext) { }

    async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
        let generationCall: LangfuseGenerationClient | null | undefined = null;
        
        const url: string | undefined = block.params.url;
        const sharedMessageProps: SayTool = {
            tool: 'getFigmaPreviewImage',
            url: ToolHelpers.removeClosingTag('url', url)
        };

        try {
            // 处理部分工具调用（流式传输中）
            if (block.partial) {
                const partialMessage = JSON.stringify({
                    ...sharedMessageProps,
                    content: ''
                  } satisfies SayTool);
                  await this.context.messageService.say('tool', partialMessage, block.partial);
                return;
            }
            
            // 参数验证（工具调用完成后）
            if (!url) {
                this.context.stateManager.updateState({
                    consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
                });
                const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('get_figma_preview_image', 'url', this.context);
                ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
                return;
            }
            
            this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
            
            this.context.loggerManager.reportUserAction({
                key: 'agent_tools_request',
                type: 'get_figma_preview_image'
            });
            
            const toolLog = ToolHelpers.generateToolLog('get_figma_preview_image', this.context.loggerManager);
            toolLog.start(url);
            
            generationCall = this.context.loggerManager.getTrace()?.generation({
                name: 'tool_call',
                input: { url },
                metadata: { name: block.name }
            });
            
            const startToolTime = Date.now();
            const figmaAccessToken = await FigmaTokenHelper.getEnvironmentFigmaToken(this.context.messenger);
            const figmaClient = new FigmaClient(figmaAccessToken);
            const urlInfo = parseUrl(url!);
            const rootImageRes = await figmaClient.getImages(urlInfo.fileKey, urlInfo.nodeId);
            const rootImageUrl = rootImageRes.images?.[urlInfo.nodeId] ?? rootImageRes.images?.[urlInfo.nodeId.replaceAll('-', ':')] ?? '';
            // 从 rootImageUrl 下载图片并获取 buffer
            let screenshotBuffer: Buffer | null = null;
            let uploadedFileUrl: string = '';

            if (!rootImageUrl) {
                throw new Error('无法获取Figma根节点预览图');
            }

            // 下载图片
            try {
                screenshotBuffer = await downloadImageAsBuffer(rootImageUrl);
            } catch (downloadError) {
                throw new Error(`图片下载失败: ${downloadError instanceof Error ? downloadError.message : String(downloadError)}`);
            }

            // 上传图片到服务器
            try {
                const timestamp = Date.now();
                const filename = `figma_${timestamp}.png`;
                
                const file = await uploadBuffer(GlobalConfig.getConfig().getKwaiPilotDomain(), {
                    buffer: screenshotBuffer,
                    filename: filename,
                    mimeType: 'image/png'
                });

                uploadedFileUrl = file.url || '';
                if (!uploadedFileUrl) {
                    throw new Error('图片上传成功但未返回有效的URL');
                }
            } catch (uploadError) {
                throw new Error(`图片上传失败: ${uploadError instanceof Error ? uploadError.message : String(uploadError)}`);
            }

            const successMessage = JSON.stringify({
                ...sharedMessageProps,
                content: uploadedFileUrl
              } satisfies SayTool);

            toolLog.end(uploadedFileUrl);
            generationCall?.end({
                output: { uploadedFileUrl }
            });
            
            await this.context.messageService.say('tool', successMessage, false);
            
            // 推送结果，包含图片
            ToolHelpers.pushToolResult(
                block,
                userMessageContent,
                [{
                    type: 'text',
                    text: 'Figma 预览图片获取成功'
                }, {
                    type: 'image',
                    source: {
                        type: 'url',
                        url: uploadedFileUrl
                    }
                }],
                this.context.stateManager
            );

            // 添加性能监控
            this.context.loggerManager.perf({
                namespace: ASSISTANT_NAMESPACE,
                subtag: 'kwaipilot-ide-agent-chat-tool',
                millis: Date.now() - startToolTime,
                extra4: 'success',
                extra6: block.name
            });

            // 添加工具操作报告
            ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
                url,
                uploadedFileUrl,
                fileSize: screenshotBuffer?.length || 0
            });
            
            return;
        } catch (error: any) {
            await ToolHelpers.handleError(
                block,
                userMessageContent,
                this.context,
                generationCall,
                'getting figma preview image',
                error,
                'get_figma_preview_image' as any
            );
        }
    }

}
