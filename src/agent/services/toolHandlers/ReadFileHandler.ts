import { ToolUse } from '../../types/message';
import { Tool<PERSON>elpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import path from 'path';
import { getReadablePath } from '@/util/path';
import { extractTextFromFile } from '../../tools/extract-text';
import { LangfuseGenerationClient } from 'langfuse';
import { getFileLanguage } from '@/util/fileType';
import fs from 'fs/promises';
import { listFiles } from '@/agent/tools/list-files';
import { formatResponse } from '@/agent/prompt/responses';
import { exec } from 'child_process';


interface ExecResult {
  stdout: string;
}
function execAsync(command: string): Promise<ExecResult> {
  return new Promise((resolve, reject) => {
    exec(command, { encoding: 'utf-8', timeout: 3000 }, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve({ stdout });
      }
    });
  });
}


type AmendStrategy = 'pathAdd' | 'suffixSwitch'

/**
 * 读取文件工具处理器
 */
export class ReadFileHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) { }

  /**
   * 智能路径解析方法，处理cd命令后路径不匹配的问题
   * @param relPath 相对路径
   * @returns 解析后的绝对路径和修正策略
   */
  private async normalizePath(relPath: string): Promise<{ path: string; amendStrategy?: AmendStrategy }> {
    const normalizedPath = relPath.trim().replace(/^\.?\//, '');

    // 如果已经是绝对路径，直接返回
    if (path.isAbsolute(normalizedPath)) {
      return { path: normalizedPath };
    }

    // 处理波浪线路径
    if (normalizedPath.startsWith('~/')) {
      const homedir = require('os').homedir();
      return { path: path.resolve(homedir, normalizedPath.slice(2)) };
    }

    // 首先尝试以当前工作目录为基准的路径
    const absolutePathFromCwd = path.resolve(this.context.cwd, normalizedPath);

    // 检查路径是否存在
    try {
      await fs.access(absolutePathFromCwd);
      this.context.loggerManager.agentInfo(`路径解析成功: ${absolutePathFromCwd}`);
      return { path: absolutePathFromCwd };
    } catch {
      // 路径不存在，尝试智能猜测
      this.context.loggerManager.agentInfo(`路径 ${absolutePathFromCwd} 不存在，开始智能路径解析`);
      const time = Date.now();
      const res = await this.intelligentPathResolve(normalizedPath, absolutePathFromCwd);
      const millis = Date.now() - time;
      this.context.loggerManager.reportUserAction({
        key: 'agent_task',
        type: 'read_file_intelligent_path_resolve',
        content: JSON.stringify({
          sessionId: this.context.stateManager.getState().sessionId,
          chatId: this.context.stateManager.getState().chatId,
          amendStrategy: res.amendStrategy,
          millis,
          originAbsolutePath: absolutePathFromCwd,
          absolutePath: res.path
        })
      })
      this.context.loggerManager.agentInfo(`智能路径解析耗时: ${millis}ms`);
      return res;
    }
  }

  /**
   * 智能路径解析，处理cd命令导致的路径偏移问题
   * @param normalizedPath 标准化后的相对路径
   * @param fallbackPath 回退路径
   * @returns 解析后的绝对路径和修正策略
   */
  private async intelligentPathResolve(normalizedPath: string, fallbackPath: string): Promise<{ path: string; amendStrategy?: AmendStrategy }> {
    // 核心策略: 使用fast-glob查找文件（处理cd命令后缺失目录层级的情况）

    function isLikelyFile(filePath: string) {
      // 获取路径的最后一部分（文件名或目录名）
      const basename = path.basename(filePath);

      // 检查是否包含点号，且点号不在开头位置
      const dotIndex = basename.lastIndexOf('.');
      return dotIndex > 0 && dotIndex < basename.length - 1;
    }


    if (!isLikelyFile(normalizedPath)) {
      this.context.loggerManager.agentInfo(`返回可能是个目录，目录不容错: ${fallbackPath}`);
      return { path: fallbackPath };
    }
    const pathCompletionResult = await this.tryPathCompletion(normalizedPath);
    if (pathCompletionResult) {
      return pathCompletionResult;
    }

    // 所有策略都失败，返回原始路径让后续流程处理错误
    this.context.loggerManager.agentInfo(`智能路径解析失败，使用原始路径: ${fallbackPath}`);
    return { path: fallbackPath };
  }

  /**
   * 路径补全策略：处理cd命令后AI返回的相对路径缺失目录层级的情况
   * 例如：cwd=/home/<USER>/tool.ts, 实际文件在/home/<USER>/src/mcp/tool.ts
   */
  private async tryPathCompletion(normalizedPath: string): Promise<{ path: string; amendStrategy: AmendStrategy } | null> {
    try {
      return await this.findFileWithBash(normalizedPath);
    } catch (error) {
      return null;
    }
  }

  /**
   * 使用 find 命令查找文件
   * @param normalizedPath 要查找的相对路径
   * @returns 找到的绝对路径和修正策略或null
   */
  private async findFileWithBash(normalizedPath: string): Promise<{ path: string; amendStrategy: AmendStrategy } | null> {
    try {
      const fileName = path.basename(normalizedPath);
      const relativeDirs = path.dirname(normalizedPath);

      this.context.loggerManager.agentInfo(`使用bash查找文件: ${normalizedPath}`);

      /**
      * 处理路径错误发生的情况
      * 例如：AI返回mcp/tool.ts，但实际文件在/home/<USER>/src/mcp/tool.tsx
      */
      const exactMatch = await this.findExactMatch(normalizedPath, relativeDirs);
      if (exactMatch) {
        return { path: exactMatch, amendStrategy: 'pathAdd' };
      }

      // 策略2: 扩展名自动修正查找
      const extensionCorrected = await this.findWithExtensionCorrection(normalizedPath, fileName, relativeDirs);
      if (extensionCorrected) {
        return { path: extensionCorrected, amendStrategy: 'suffixSwitch' };
      }

    } catch (error) {
      this.context.loggerManager.agentInfo(`查找过程出错: ${error}`);
    }

    return null;
  }

  /**
   * 处理路径错误发生的情况
   * 例如：AI返回mcp/tool.ts，但实际文件在/home/<USER>/src/mcp/tool.tsx
   */
  private async findExactMatch(normalizedPath: string, relativeDirs: string): Promise<string | null> {
    // 对于简单文件名
    if (relativeDirs === '.' || !normalizedPath.includes('/')) {
      return null
    }
    // 对于包含路径的文件
    else {
      const excludePaths = this.getExcludedPathsForFindCommand();
      // 修复路径匹配模式：使用 **/path 而不是 ./**/path
      const findCommand = `find "${this.context.cwd}" ${excludePaths} -path "*/${normalizedPath}" -type f -print | head -5`

      try {
        const { stdout } = await execAsync(findCommand);
        const foundPaths = stdout.trim().split('\n').filter(p => p.length > 0);

        if (foundPaths.length > 0) {
          const foundPath = foundPaths[0];
          this.context.loggerManager.agentInfo(`精确路径匹配成功: 找到文件 ${foundPath}`);
          return foundPath;
        }
      } catch (error) {
        this.context.loggerManager.agentInfo(`精确路径匹配命令执行失败: ${error}`);
      }
    }

    return null;
  }

  /**
   * 扩展名自动修正查找
   * 处理AI返回错误扩展名的情况，如返回app.ts但实际是app.tsx
   * 基于 getExtensionPriorityList 进行智能扩展名容错
   */
  private async findWithExtensionCorrection(normalizedPath: string, fileName: string, relativeDirs: string): Promise<string | null> {
    const fileNameWithoutExt = path.parse(fileName).name;
    const originalExt = path.parse(fileName).ext.slice(1); // 移除点号

    this.context.loggerManager.agentInfo(`尝试扩展名修正: 基于优先级查找 ${fileNameWithoutExt} 的其他扩展名`);

    // 获取扩展名优先级列表
    const extensionMap = this.getExtensionPriorityList();
    const candidateExtensions = extensionMap[originalExt] || [];

    // 如果没有找到相关的扩展名组，直接返回null
    if (candidateExtensions.length === 0) {
      this.context.loggerManager.agentInfo(`未找到 ${originalExt} 的扩展名组，跳过扩展名修正`);
      return null;
    }

    // 对于简单文件名，搜索当前工作目录
    if (relativeDirs === '.' || !normalizedPath.includes('/')) {
      try {
        const files = await fs.readdir(this.context.cwd, { withFileTypes: true });
        const targetFiles = files.filter(file =>
          file.isFile() &&
          file.name.startsWith(fileNameWithoutExt) &&
          candidateExtensions.some(ext => file.name === `${fileNameWithoutExt}.${ext}`)
        );

        if (targetFiles.length > 0) {
          const foundPath = path.resolve(this.context.cwd, targetFiles[0].name);
          this.context.loggerManager.agentInfo(`扩展名修正成功: ${fileName} -> ${targetFiles[0].name}, 找到文件 ${foundPath}`);
          return foundPath;
        }
      } catch (error) {
        this.context.loggerManager.agentInfo(`读取目录失败: ${error}`);
      }
    }
    // 对于包含路径的文件，只读取目标目录下的文件
    else {
      const searchDir = path.resolve(this.context.cwd, relativeDirs);

      try {
        const files = await fs.readdir(searchDir, { withFileTypes: true });
        const targetFiles = files.filter(file =>
          file.isFile() &&
          file.name.startsWith(fileNameWithoutExt) &&
          candidateExtensions.some(ext => file.name === `${fileNameWithoutExt}.${ext}`)
        );

        if (targetFiles.length > 0) {
          const foundPath = path.resolve(searchDir, targetFiles[0].name);
          this.context.loggerManager.agentInfo(`扩展名路径修正成功: ${normalizedPath} -> ${foundPath.split('/').slice(-2).join('/')}, 找到文件 ${foundPath}`);
          return foundPath;
        }
      } catch (error) {
        this.context.loggerManager.agentInfo(`读取目录 ${searchDir} 失败: ${error}`);
      }
    }

    this.context.loggerManager.agentInfo(`所有优先级扩展名都未找到，扩展名修正失败`);
    return null;
  }

  private getExcludedPathsForFindCommand(): string {
    // 太多的排除会导致find 命令执行耗时增加
    const excludeDirs = [
      'node_modules',
      'oh_modules',
      '.git',
      '.idea',
      '.vscode',
      'dist',
      'build',
      '.next',
      'coverage',
      'target',
      'bin',
      '__pycache__',
      'env',
      'venv',
      'out',
      "bundle",
      "vendor",
      "tmp",
      "temp",
      "deps",
      "pkg",
      "Pods",
      ".gradle",
      ".parcel-cache",
      ".pytest_cache",
      ".nuxt",
      ".sass-cache",
      ".vs",
      "obj"
    ];

    // 构建正确的排除条件，使用 -type d -name 格式
    const excludeConditions = excludeDirs.map(dir => `-name "${dir}"`);

    // 如果有排除规则，返回完整的排除字符串
    if (excludeConditions.length > 0) {
      return `-type d \\( ${excludeConditions.join(' -o ')} \\) -prune -o`;
    }

    return '';
  }

  /**
   * 获取扩展名映射优先级列表
   * @param targetExt 目标扩展名
   * @returns 按优先级排序的扩展名数组
   */
  private getExtensionPriorityList(): Record<string, string[]> {
    const extensions = [
      ['tsx', 'js', 'jsx', 'mjs', 'cjs', 'ts'],
      ['pyi', 'pyc', 'py'],
      ['scss', 'less', 'sass', 'styl', 'css'],
      ['jsonc', 'json5', 'js', 'json'],
      ['markdown', 'md'],
      ['class', 'kt', 'java'],
      ['rs', 'rust'],
      ['go', 'golang'],
      ['html', 'htm'],
      ['h', 'hpp', 'cc', 'c', 'cpp'],
      ['php8', 'php7', 'php5', 'php'],
    ]

    // 根据extensions数组动态生成extensionMap
    const extensionMap: { [key: string]: string[] } = this.generateExtensionMap(extensions);

    return extensionMap;
  }

  /**
   * 根据extensions数组动态生成extensionMap
   * @param extensions 扩展名分组数组
   * @returns 完整的扩展名映射对象
   */
  private generateExtensionMap(extensions: string[][]): { [key: string]: string[] } {
    const extensionMap: { [key: string]: string[] } = {};

    // 为每个扩展名分组生成映射
    for (const group of extensions) {
      for (const ext of group) {
        // 映射到组内其他扩展名（排除自身），保持原始顺序
        extensionMap[ext] = group.filter(other => other !== ext);
      }
    }

    return extensionMap;
  }



  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;
    let amendStrategy: AmendStrategy | undefined = undefined;
    const relPath: string | undefined = block.params.path;
    const sharedMessageProps: SayTool = {
      tool: 'readFile',
      path: getReadablePath(this.context.cwd, ToolHelpers.removeClosingTag('path', relPath, block.partial))
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: undefined
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!relPath) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('read_file', 'path', this.context),
            this.context.stateManager
          );
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        const normalizeResult = await this.normalizePath(relPath);
        const res = await this.normalizePath('')
        const absolutePath = normalizeResult.path;
        amendStrategy = normalizeResult.amendStrategy;
        const originAbsolutePath = path.resolve(this.context.cwd, relPath);
        let isDir = false;

        try {
          isDir = (await fs.stat(absolutePath)).isDirectory()
        } catch (error) {
        }

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'read_file',
          content: JSON.stringify({
            toolName: block.name,
            sessionId: this.context.stateManager.getState().sessionId,
            chatId: this.context.stateManager.getState().chatId,
            params: {
              path: absolutePath,
              fileType: getFileLanguage(absolutePath)
            }
          })
        });
        const startLine: number | undefined = block.params.start_line_one_indexed
          ? parseInt(block.params.start_line_one_indexed)
          : undefined;
        const endLine: number | undefined = block.params.end_line_one_indexed
          ? parseInt(block.params.end_line_one_indexed)
          : undefined;
        // 兼容tooluse格式，可能是boolean
        const shouldReadEntireFileRaw: string | boolean | undefined = block.params.should_read_entire_file;
        const shouldReadEntireFile: boolean = typeof shouldReadEntireFileRaw === 'boolean'
          ? shouldReadEntireFileRaw
          : shouldReadEntireFileRaw !== 'false';
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          startLine,
          endLine,
          shouldReadEntireFile,
          content: absolutePath
        } satisfies SayTool);

        await this.context.messageService.say('tool', completeMessage, false);
        const toolLog = ToolHelpers.generateToolLog('read_file', this.context.loggerManager);
        toolLog.start(absolutePath);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: absolutePath
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();

        let content = ''
        let totalLineNum = 0;
        let readedLineNum = 0;

        const getPathCorrectionMessage = (strategy: AmendStrategy | undefined, originalPath: string, correctedPath: string): string => {
          switch (strategy) {
            case 'pathAdd':
              // Show relative paths for better readability
              const relativeOriginal = path.relative(this.context.cwd, originalPath);
              const relativeCorrected = path.relative(this.context.cwd, correctedPath);
              return `📁 Path Auto-Completion: "${relativeOriginal}" → "${relativeCorrected}"`;
            case 'suffixSwitch':
              return `🔄 File Extension Auto-Correction: "${path.basename(originalPath)}" → "${path.basename(correctedPath)}"`;
            default:
              return '';
          }
        };

        const correctionMessage = amendStrategy
          ? getPathCorrectionMessage(amendStrategy, originAbsolutePath, absolutePath)
          : null;

        if (!isDir) {
          if (!amendStrategy || amendStrategy === 'suffixSwitch') {
            const res = await extractTextFromFile(
              absolutePath,
              shouldReadEntireFile
                ? undefined
                : {
                  startLine,
                  endLine
                }
            );
            content = res.content;
            if (correctionMessage) {
              content = `${correctionMessage}\n\n${content}`;
            }
            totalLineNum = res.totalLineNum;
            readedLineNum = res.readedLineNum;
          } else {
            content = `${correctionMessage}\n\n💡 Note: File found but requires manual confirmation to read. To view the file content, please use the read_file tool to re-read this file.`;
          }
        } else {
          if (!amendStrategy) {
            const [files, didHitLimit] = await listFiles(absolutePath, true, 200);
            const result = formatResponse.formatFilesListWithLLMMessages(absolutePath, files, didHitLimit);
            content = `📂 Directory path detected, automatically switched to directory listing mode\nPath: ${absolutePath}\n\n${result}`;
          } else {
            content = `${correctionMessage}\n\n📂 This is a directory. To view directory contents, please use the list_files tool.`;
          }
        }

        /** 上报修正策略消息 */
        this.context.loggerManager.reportUserAction({
          key: 'agent_task',
          type: 'read_file_path_amend',
          content: JSON.stringify({
            toolName: block.name,
            sessionId: this.context.stateManager.getState().sessionId,
            chatId: this.context.stateManager.getState().chatId,
            originAbsolutePath,
            amendStrategy: amendStrategy,
            absolutePath,
            isAmend: absolutePath !== originAbsolutePath,
            isDir: isDir,
            absolutePathExt: getFileLanguage(absolutePath),
            originAbsolutePathExt: getFileLanguage(originAbsolutePath),
            millis: Date.now() - startToolTime,
          })
        });

        toolLog.end(content);
        generationCall?.end({
          output: { content, totalLineNum }
        });
        ToolHelpers.pushToolResult(block, userMessageContent, content, this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          shouldReadEntireFile,
          totalLineNum,
          readedLineNum,
          path: absolutePath,
          startLine,
          endLine,
          fileType: getFileLanguage(absolutePath)
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'reading file',
        error,
        'read_file'
      );
    }
  }
}