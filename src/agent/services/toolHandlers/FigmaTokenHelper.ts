import { I<PERSON>esseng<PERSON> } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';

/**
 * Figma 令牌助手类
 * 提供获取 Figma 访问令牌的公共方法
 */
export class FigmaTokenHelper {
  /**
   * 获取环境中的 Figma 令牌
   * @param messenger 消息传递器实例
   * @returns Promise<string> Figma 访问令牌
   */
  static async getEnvironmentFigmaToken(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>): Promise<string> {
    const res = await messenger.request('assistant/agent/figmaToken', {});
    return res.data || '';
  }
}
