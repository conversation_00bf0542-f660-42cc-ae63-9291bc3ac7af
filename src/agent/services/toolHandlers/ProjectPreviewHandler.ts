import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON>el<PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';
import { UIPreviewManager } from '@/ui-preview';

/**
 * 项目预览工具处理器
 */
export class ProjectPreviewHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) { }

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    try {
      if (block.partial) {
        return;
      }
      const url = block.params.preview_url || '';
      const previewBlock: SayTool = {
        tool: 'project_preview',
        url
      };
      this.context.loggerManager.reportUserAction({
        key: 'agent_tools_request',
        type: 'project_preview'
      });

      if (url) {
        const isActive = await UIPreviewManager.getInstance().checkPortActive(url);
        if (isActive) {
          await this.context.messageService.say('project_preview', JSON.stringify(previewBlock), false);

          ToolHelpers.pushToolResult(block, userMessageContent, '<answer>success</answer>', this.context.stateManager);
          return;
        }
      }

      ToolHelpers.pushToolResult(
        block,
        userMessageContent,
        `project_preview tool error: ${url} is not active`,
        this.context.stateManager
      );

      return;
    } catch (e) {
      await this.context.messageService.say(
        'error',
        `project_preview tool error: ${e instanceof Error ? e.message : String(e)}`,
        false
      );
      ToolHelpers.pushToolResult(
        block,
        userMessageContent,
        `project_preview tool error: ${e instanceof Error ? e.message : String(e)}`,
        this.context.stateManager
      );
      return;
    }
  }
}
