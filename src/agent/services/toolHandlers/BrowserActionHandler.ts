import { ToolUse } from '../../types/message';
import { <PERSON>lHelpers, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1, ToolResponse } from '../../types/type.d';
import { BrowserPreview } from '@/ui-preview/browser';

/**
 * 浏览器获取工具处理器
 */
export class BrowserActionHandler implements ToolHandler {
    constructor(private context: ToolHandlerContext) { }

    async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
        try {
            const sharedMessageProps: SayTool = {
                tool: 'browserAction',
            };

            if (block.partial) {
                await this.context.messageService.say('tool', JSON.stringify({ params: '', ...sharedMessageProps, }), true);
                return;
            }

            this.context.loggerManager.reportUserAction({
                key: 'agent_tools_request',
                type: 'browser_action',
            });

            const { action, tabId, ...params } = (block.params || {}) as any;

            await this.context.messageService.say('tool', JSON.stringify({ action, params: JSON.stringify(params), ...sharedMessageProps, }), true);

            const res = await BrowserPreview.getBrowserTools().execute(action, block.params, tabId as any);

            const content: any = [];

            res.content.forEach(item => {
                if (!item) {
                    return;
                }

                if (item.type == 'text') {
                    content.push({
                        type: 'text' as const,
                        text: item.text,
                    })
                }

                if (item.type == 'image' && item.source?.url) {
                    content.push({
                        type: 'image' as const,
                        source: {
                            type: 'url',
                            url: item.source.url
                        }
                    })
                }
            })

            await this.context.messageService.say('tool', JSON.stringify({
                action,
                params: JSON.stringify({ ...params, tabId }),
                result: JSON.stringify(res.content),
                ...sharedMessageProps
            }), false);

            ToolHelpers.pushToolResult(
                block,
                userMessageContent,
                content,
                this.context.stateManager
            )
            return;
        } catch (e) {
            await this.context.messageService.say(
                'error',
                `browser_action tool error: ${e instanceof Error ? e.message : String(e)}`,
                false
            );
            ToolHelpers.pushToolResult(
                block,
                userMessageContent,
                `browser_action tool error: ${e instanceof Error ? e.message : String(e)}`,
                this.context.stateManager
            );
            return;
        }
    }
}