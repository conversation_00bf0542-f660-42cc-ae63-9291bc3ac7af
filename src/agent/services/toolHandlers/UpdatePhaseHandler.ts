import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type';
import { LangfuseGenerationClient } from 'langfuse';
import { PHASE_LIMIT_VALUES } from '@/agent/constants/tool';
import { hasTodoWriteMessage } from '../../utils/message';
import { ASSISTANT_NAMESPACE } from '@/util/const';

/**
 * 更新阶段工具处理器
 */
export class UpdatePhaseHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const curPhase: string | undefined = block.params.current_phase;
    const phase: string | undefined = block.params.next_phase;
    const sharedMessageProps: SayTool = {
      tool: 'updatePhase',
      content: phase,
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
        } satisfies SayTool);
        // await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!phase) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('update_phase', 'content', this.context),
            this.context.stateManager
          );
          return;
        }
        if (!PHASE_LIMIT_VALUES.includes(phase)) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            'The phase value is not valid. the phase must be one of the following values: \n' +
              PHASE_LIMIT_VALUES.join(', '),
            this.context.stateManager
          );
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'update_phase'
        });

        const toolLog = ToolHelpers.generateToolLog('update_phase', this.context.loggerManager);
        toolLog.start(`current_phase: ${curPhase}, next_phase: ${phase}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            curPhase,
            nextPhase: phase
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();

        // ---------业务逻辑开始
        if (curPhase === phase) {
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            'The phase is already ' + phase + ', no need to update.',
            this.context.stateManager
          );
          return;
        }
        if (phase === 'action') {
          // 如果是要转到 action 阶段，需要检查一下 ToDoList 是否已经生成
          if (!hasTodoWriteMessage(this.context.stateManager.apiConversationHistory)) {
            ToolHelpers.pushToolResult(
              block,
              userMessageContent,
              'The ToDoList is not generated yet, please generate it first.',
              this.context.stateManager
            );
            return;
          }
        }
        await this.context.messageService.say('tool', JSON.stringify(sharedMessageProps), false);
        ToolHelpers.pushToolResult(
          block,
          userMessageContent,
          'The phase has been updated to ' + phase + ' successfully',
          this.context.stateManager
        );

        // ---------业务逻辑结束

        toolLog.end('success');
        generationCall?.end({});
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra3: phase,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          curPhase,
          nextPhase: phase
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'update phase',
        error,
        'update_phase'
      );
    }
  }
} 