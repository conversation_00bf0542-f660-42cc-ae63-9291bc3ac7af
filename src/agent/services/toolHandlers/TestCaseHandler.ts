import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON>el<PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type.d';

export interface ISimpleTestCase {
    id: string;
    title: string;
}

const sharedMessageProps: SayTool = {
    tool: 'testCase',
};

/**
 * 轻量测试用例工具处理器
 */
export class TestCaseHandler implements ToolHandler {
    constructor(private context: ToolHandlerContext) { }

    /**
     * Convenient method to send test case messages
     */
    private async say(content: any, isStreaming: boolean = false): Promise<void> {
        await this.context.messageService.say('tool', JSON.stringify({
            ...content,
            ...sharedMessageProps,
        }), isStreaming);
    }

    async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
        const { params } = block;

        try {
            if (block.partial) {
                await this.context.messageService.say('tool', JSON.stringify({ params: '', ...sharedMessageProps, }), true);
                return;
            }

            this.context.loggerManager.reportUserAction({
                key: 'agent_tools_request',
                type: 'test_case',
            });

            const action = params.action;
            let result: any = null;

            switch (action) {
                case 'create_test_cases':
                    result = await this.handleCreateTestCases(params, false);
                    break;
                default:
                    throw new Error(`Unknown test case operation: ${action}`);
            }

            // Push tool result using ToolHelpers
            if (result) {
                this.context.loggerManager.reportUserAction({
                    key: 'agent_tools_request',
                    type: 'test_case'
                });
                ToolHelpers.pushToolResult(block, userMessageContent, result, this.context.stateManager);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';

            // Create error result
            const errorResult = {
                success: false,
                action: params?.action || 'unknown',
                error: errorMessage,
                data: {
                    message: `Test case operation failed: ${errorMessage}`
                }
            };

            // Push error result using ToolHelpers
            ToolHelpers.pushToolResult(block, userMessageContent, JSON.stringify(errorResult), this.context.stateManager);
        }
    }

    /**
     * Create test cases list
     */
    private async handleCreateTestCases(params: any, partial: boolean): Promise<string> {
        const { testCases } = params;

        await this.say({
            type: 'create_test_cases',
            testCases: testCases,
        }, partial);

        const result = {
            success: true,
            action: 'create_test_cases'
        };

        return JSON.stringify(result);
    }
}