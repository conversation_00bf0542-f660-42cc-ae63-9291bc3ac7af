import { ToolUse, ToolUseName, ToolParamName } from '../types/message';
import { StateManager } from './StateManager';
import { MessageService } from './MessageService';
import { LoggerManager } from './LoggerManager';
import { formatResponse } from '../prompt/responses';
import { Ask, ToolResponse, TextBlockParamVersion1, ImageBlockParam } from '../types/type.d';
import { serializeError } from 'serialize-error';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LangfuseGenerationClient } from 'langfuse';
import { v4 } from 'uuid';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { BaseAgentManager } from '../AgentManager';
import { CheckpointService } from './CheckpointService';

export interface ToolHandlerContext {
  stateManager: StateManager;
  messageService: MessageService;
  loggerManager: LoggerManager;
  cwd: string;
  sessionInfo: any;
  messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>;
  checkpointService: CheckpointService;
  agentManager: BaseAgentManager; // 添加 agentManager 属性
}

export interface ToolHandler {
  handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void>;
}

/**
 * 工具辅助函数类 - 包含公共的辅助方法
 */
export class ToolHelpers {
  /**
   * 生成工具描述信息
   */
  static getToolDescription(block: ToolUse, command?: string): string {
    switch (block.name) {
      case 'execute_command':
        return `[${block.name} for '${command || block.params.command}']`;
      case 'read_file':
        return `[${block.name} for '${block.params.path}' ${block.params.should_read_entire_file === 'false' && block.params.end_line_one_indexed
            ? `from line ${block.params.start_line_one_indexed} to line ${block.params.end_line_one_indexed}`
            : ''
          }]`;
      case 'edit_file':
        return `[${block.name} for '${block.params.target_file}']`;
      case 'replace_in_file':
        return `[${block.name} for '${block.params.path}']`;
      case 'write_to_file':
        return `[${block.name} for '${block.params.path}']`;
      case 'codebase_search':
        return `[${block.name} for '${block.params.query}']`;
      case 'grep_search':
        return `[${block.name} for '${block.params.regex}'${block.params.file_pattern ? ` in '${block.params.file_pattern}'` : ''
          }]`;
      case 'list_files':
        return `[${block.name} for '${block.params.path}']`;
      case 'ask_followup_question':
        return `[${block.name} for '${block.params.question}']`;
      case 'use_mcp_tool':
        return `[${block.name}] for '${block.params.server_name}' and '${block.params.tool_name}'`;
      case 'command_status_check':
        return `[${block.name} for ${block.params.check_duration || '5'} seconds]`;
      case 'browser_action':
        return `[${block.name} for '${block.params.action}']`;
      case 'test_case':
        return `[${block.name} for '${block.params.action}']`;
      case 'project_preview':
        return `[${block.name} for '${block.params.preview_url}']`;
      case 'parse_figma':
        return `[${block.name} for '${block.params.url}']`;
      case 'get_figma_preview_image':
        return `[${block.name} for '${block.params.url}']`;
      case 'render_research_plan':
        return `[${block.name} for '${block.params.content}']`;
      case 'research':
        return `[${block.name} for '${block.params.stage}']`;
      case 'update_phase':
        return `[${block.name} for 'update phase from ${block.params.current_phase} to ${block.params.next_phase}']`;
      default:
        return `[${block.name}]`;
    }
  }

  /**
   * 推送工具结果
   */
  static pushToolResult(
    block: ToolUse,
    userMessageContent: (TextBlockParamVersion1 | ImageBlockParam)[],
    content: ToolResponse,
    stateManager: StateManager,
    command?: string
  ) {
    const toolDescription = ToolHelpers.getToolDescription(block, command);
    const toolName = block.name;
    const params = block.params;

    userMessageContent.push({
      type: 'text',
      text: `${toolDescription} Result:`,
      category: 'tool-title',
      toolName,
      params,
      toolId: block.id
    });

    if (typeof content === 'string') {
      userMessageContent.push({
        type: 'text',
        text: content || '(tool did not return anything)',
        category: 'tool-response',
        toolName,
        params,
        toolId: block.id
      });
    } else {
      const formattedContentList = content?.map<TextBlockParamVersion1 | ImageBlockParam>((c) => {
        if ('text' in c) {
          return {
            ...c,
            category: 'tool-response',
            toolName,
            params,
            toolId: block.id
          } as TextBlockParamVersion1;
        } else {
          return {
            ...c,
            toolName,
            params,
            toolId: block.id
          } as ImageBlockParam;
        }
      });
      userMessageContent.push(...formattedContentList);
    }
    stateManager.updateState({ didAlreadyUseTool: true });
  }

  /**
   * 推送额外的工具反馈
   */
  static pushAdditionalToolFeedback(block: ToolUse, userMessageContent: TextBlockParamVersion1[], feedback?: string) {
    if (!feedback) {
      return;
    }
    const content = `The user provided the following feedback:\n<feedback>\n${feedback}\n</feedback>`;
    userMessageContent.push({
      type: 'text',
      text: content,
      category: 'tool-feedback',
      toolName: block.name,
      params: block.params,
      toolId: block.id
    });
  }

  /**
   * 请求用户批准
   */
  static async askApproval(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[],
    messageService: MessageService,
    stateManager: StateManager,
    type: Ask,
    partialMessage?: string
  ): Promise<boolean> {
    const { askResponse, text } = await messageService.ask(type, partialMessage, false);
    if (askResponse !== 'yesButtonClicked') {
      ToolHelpers.pushToolResult(block, userMessageContent, formatResponse.toolDenied(), stateManager);
      if (text) {
        ToolHelpers.pushAdditionalToolFeedback(block, userMessageContent, text);
        await messageService.say('user_feedback', text);
      }
      stateManager.updateState({ didRejectTool: true });
      return false;
    } else {
      if (text) {
        ToolHelpers.pushAdditionalToolFeedback(block, userMessageContent, text);
        await messageService.say('user_feedback', text);
      }
      return true;
    }
  }

  /**
   * 请求命令批准
   */
  static async askCommandApproval(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[],
    messageService: MessageService,
    stateManager: StateManager,
    type: Ask,
    partialMessage?: string
  ): Promise<{ didApprove: boolean; text?: string }> {
    const { askResponse, text } = await messageService.ask(type, partialMessage, false);
    if (askResponse !== 'yesButtonClicked') {
      ToolHelpers.pushToolResult(block, userMessageContent, formatResponse.toolDenied(), stateManager);
      stateManager.updateState({ didRejectTool: true });
      return { didApprove: false, text };
    } else {
      if (text) {
        const newText = `Note: The user changed the command before running it. The updated command was: ${text}`;
        ToolHelpers.pushAdditionalToolFeedback(block, userMessageContent, newText);
      }
      return { didApprove: true, text };
    }
  }

  /**
   * 处理错误
   */
  static async handleError(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[],
    context: ToolHandlerContext,
    generationCall: LangfuseGenerationClient | null | undefined,
    action: string,
    error: Error,
    toolName: ToolUseName
  ) {
    const errorString = `Error ${action}: ${error.message ?? JSON.stringify(serializeError(error))}`;
    await context.messageService.say(
      'tool_error',
      `Error ${action}:\n${error.message ?? JSON.stringify(serializeError(error), null, 2)}`
    );
    ToolHelpers.pushToolResult(block, userMessageContent, formatResponse.toolError(errorString), context.stateManager);
    context.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - context.stateManager.getState().startLlmTime,
      extra4: errorString,
      extra6: toolName
    });
    context.loggerManager.reportUserAction({
      key: 'agent_task',
      type: 'tool_use_error',
      content: JSON.stringify({
        toolName: block.name,
        sessionId: context.stateManager.getState().sessionId,
        chatId: context.stateManager.getState().chatId,
        ts: Date.now(),
        error: errorString
      })
    });
    generationCall?.end({
      output: {
        msg: error.message
      }
    });
  }

  /**
   * 移除闭合标签
   */
  static removeClosingTag(tag: ToolParamName, text?: string, isPartial?: boolean): string {
    if (!isPartial) {
      return text || '';
    }
    if (!text) {
      return '';
    }
    const tagRegex = new RegExp(
      `\\s?<\/?${tag
        .split('')
        .map((char) => `(?:${char})?`)
        .join('')}$`,
      'g'
    );
    return text.replace(tagRegex, '');
  }

  /**
   * 生成工具日志
   */
  static generateToolLog(toolName: string, loggerManager: LoggerManager) {
    const uuid = v4();
    const agentLogger = loggerManager.getAgentLogger();
    return {
      start: (content: string) => {
        agentLogger.info(`tool}-开始${toolName}-${uuid}`, content);
      },
      end: (details: string) => {
        agentLogger.debug(`tool}-结束${toolName}-${uuid}`, details);
        agentLogger.info(`tool}-结束${toolName}-${uuid}`);
      }
    };
  }

  /**
   * 报告工具操作
   */
  static reportToolAction(context: ToolHandlerContext, block: ToolUse, duration: number, params: Record<string, any>) {
    context.loggerManager.reportUserAction({
      key: 'agent_task',
      type: 'tool_use',
      content: JSON.stringify({
        toolName: block.name,
        sessionId: context.stateManager.getState().sessionId,
        chatId: context.stateManager.getState().chatId,
        ts: Date.now(),
        duration,
        params
      })
    });
  }

  /**
   * 创建缺失参数错误
   */
  static async sayAndCreateMissingParamError(
    toolName: ToolUseName,
    paramName: string,
    context: ToolHandlerContext,
    relPath?: string
  ) {
    await context.messageService.say(
      'tool_error',
      `尝试使用 ${toolName}， 因缺少参数'${paramName}'调用失败. 正在重试中...`
    );
    const state = context.stateManager.getState();
    context.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - state.startLlmTime,
      extra4: 'error',
      extra6: toolName
    });
    return formatResponse.toolError(formatResponse.missingToolParameterError(paramName));
  }

  static async sayAndCreateInvalidParamError(toolName: ToolUseName, reason: string, context: ToolHandlerContext) {
    await context.messageService.say('tool_error', `尝试使用 ${toolName}， 参数校验失败. 正在重试中...`);
    const state = context.stateManager.getState();
    context.loggerManager.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'kwaipilot-ide-agent-chat-tool',
      millis: Date.now() - state.startLlmTime,
      extra4: 'error',
      extra6: toolName
    });
    return formatResponse.toolError(formatResponse.invalidToolArgumentError(reason));
  }
}
