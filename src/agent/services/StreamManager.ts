import { AssistantMessageContent } from '../types/message';
import { StateManager } from './StateManager';
import { parseAssistantMessage } from '../tools/parse-assistant-message';
import { TextBlockParamVersion1 } from '../types/type';

export class StreamManager {
  constructor(private stateManager: StateManager) { }

  parseStreamContent(assistantMessage: string): AssistantMessageContent[] {
    return parseAssistantMessage(assistantMessage);
  }

  updateAssistantMessageContent(content: AssistantMessageContent[]): void {
    this.stateManager.updateState({ assistantMessageContent: content });
  }

  getCurrentStreamingContentIndex(): number {
    return this.stateManager.getState().currentStreamingContentIndex;
  }

  incrementStreamingContentIndex(): void {
    const state = this.stateManager.getState();
    this.stateManager.updateState({
      currentStreamingContentIndex: state.currentStreamingContentIndex + 1
    });
  }

  isStreamingComplete(): boolean {
    const state = this.stateManager.getState();
    return state.currentStreamingContentIndex >= state.assistantMessageContent.length;
  }

  markStreamingComplete(): void {
    this.stateManager.updateState({
      didCompleteReadingStream: true,
      userMessageContentReady: true
    });
  }

  lockPresentAssistantMessage(): void {
    this.stateManager.updateState({ presentAssistantMessageLocked: true });
  }

  unlockPresentAssistantMessage(): void {
    this.stateManager.updateState({
      presentAssistantMessageLocked: false
    });
  }

  markPendingUpdates(): void {
    this.stateManager.updateState({
      presentAssistantMessageHasPendingUpdates: true
    });
  }

  clearPendingUpdates(): void {
    this.stateManager.updateState({
      presentAssistantMessageHasPendingUpdates: false
    });
  }

  hasPendingUpdates(): boolean {
    return this.stateManager.getState().presentAssistantMessageHasPendingUpdates;
  }

  isPresentLocked(): boolean {
    return this.stateManager.getState().presentAssistantMessageLocked;
  }

  getCurrentBlock(): AssistantMessageContent | undefined {
    const state = this.stateManager.getState();
    if (state.currentStreamingContentIndex >= state.assistantMessageContent.length) {
      return undefined;
    }
    return state.assistantMessageContent[state.currentStreamingContentIndex];
  }

  setDidRejectTool(rejected: boolean): void {
    this.stateManager.updateState({ didRejectTool: rejected });
  }

  setDidAlreadyUseTool(used: boolean): void {
    this.stateManager.updateState({ didAlreadyUseTool: used });
  }

  addUserMessageContent(content: TextBlockParamVersion1): void {
    const state = this.stateManager.getState();
    const updatedContent = [...state.userMessageContent, content];
    this.stateManager.updateState({ userMessageContent: updatedContent });
  }

  setUserMessageContentReady(ready: boolean): void {
    this.stateManager.updateState({ userMessageContentReady: ready });
  }

  processPartialBlocks(): void {
    const state = this.stateManager.getState();
    const partialBlocks = state.assistantMessageContent.filter((block) => block.partial);
    partialBlocks.forEach((block) => {
      block.partial = false;
    });
    if (partialBlocks.length > 0) {
      this.updateAssistantMessageContent(state.assistantMessageContent);
    }
  }

  hasToolUse(): boolean {
    const state = this.stateManager.getState();
    return state.assistantMessageContent.some((block) => block.type === 'tool_use');
  }

  isLastBlock(): boolean {
    const state = this.stateManager.getState();
    return state.currentStreamingContentIndex === state.assistantMessageContent.length - 1;
  }
} 