import { ToolSwitchState } from '../utils/toolSwitches';
import { ToolParamName, ToolUseName } from './message';

export enum ToolUseType {
  TOOL_USE = 'tool_use',
  TOOL_RESULT = 'tool_result'
}

export interface ToolUse {
  type: ToolUseType.TOOL_USE;
  name: ToolUseName;
  id?: string;
  // params is a partial record, allowing only some or none of the possible parameters to be used
  params: Partial<Record<ToolParamName, string>>;
  paramsString?: string;
  partial: boolean;
}

export type WebviewMessage<
  T extends 'newTask' | 'askResponse' | 'restore' | 'stop' = 'newTask' | 'askResponse' | 'restore' | 'stop'
> = T extends 'newTask'
  ? {
    type: 'newTask';
    taskForLlm: string;
    task: string;
    rules?: string[];
    reqData: ChatRequest;
    localMessages: LocalMessage[];
    images?: string[];
    toolSwitchState?: ToolSwitchState;
    isAskMode?: boolean;
    agentMode?: IAgentMode;
    phase?: JAM_MODE_PHASE;
    /**
     * 开启增量流式输出. 文本内容会使用 assistant/agent/messageDelta 发送增量内容, 开启后可显著降低通信数据量. 因为 jetbrains 尚未兼容这一消息格式, 所以通过参数开启
     */
    enableDeltaStreaming?: boolean;
    isSupportCodeFormatter?: boolean;
  }
  : T extends 'askResponse'
  ? {
    type: 'askResponse';
    askResponse: 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';
    text?: string;
  }
  : T extends 'restore'
  ? {
    type: 'restore';
    params: {
      sessionId: string;
      chatId?: string;
      restoreCommitHash: string;
    };
  }
  : T extends 'stop'
  ? {
    type: 'stop';
    params: {
      sessionId: string;
      chatId?: string;
    };
  }
  : never;

export interface LocalMessageTextDelta {
  ts: number;
  type: 'textDelta';
  text: string;
  done: boolean;
}

export interface LocalMessage {
  /** 消息的时间戳(timestamp) */
  ts: number;
  role?: 'user';
  sessionId: string;
  chatId?: string;

  /** 消息类型：请求类消息(ask)或者回复类消息(say) */
  type: 'ask' | 'say';

  /** 当type为"ask"时的具体请求类型 */
  ask?: Ask;

  /** 当type为"say"时的具体响应类型 */
  say?: Say;

  /** 消息的具体文本内容 */
  text?: string;

  /** 是否为部分消息(未完成的消息) */
  partial?: boolean;

  /** 最后检查点的哈希值 */
  lastCheckpointHash?: string;

  /** 检查点是否已签出 */
  isCheckpointCheckedOut?: boolean;

  /** 对话历史索引 */
  conversationHistoryIndex?: number;

  /** 对话历史被截断的范围 */
  conversationHistoryDeletedRange?: [number, number];
  /** 模型输出内容结束原因 */
  stopReason?: string;
  /** 对话模式 */
  chatMode?: 'ask' | 'agent';
}

/**
 * 请求类型的消息种类，需要用户交互的消息
 */
export type Ask =
  /** 跟进问题 */
  | 'followup'
  /** 命令执行 */
  | 'command'
  /** mcp */
  | 'use_mcp_tool'
  /** 命令执行输出 */
  | 'command_output'
  /** 工具使用 */
  | 'tool'
  /** API请求失败 */
  | 'api_req_failed'
  /** 达到错误次数限制 */
  | 'mistake_limit_reached'
  /** 命令执行状态检查 */
  | 'command_status_check'
  /** 代码格式化 */
  | 'code_formatter';

/**
 * 响应类型的消息种类，纯展示/通知类消息，不需要用户交互
 */
export type Say =
  /** 任务 */
  | 'task'
  /** 错误 */
  | 'error'
  /** API请求开始 */
  | 'api_req_started'
  /** API请求失败 */
  | 'api_req_failed'
  /** API请求完成 */
  | 'api_req_finished'
  /** 文本 */
  | 'text'
  /** 完成结果 */
  | 'completion_result'
  /** 用户反馈 */
  | 'user_feedback'
  /** 编辑文件的结果 */
  | 'edit_file_result'
  /** API请求重试 */
  | 'api_req_retried'
  /** 工具错误 */
  | 'tool_error'
  /** 命令 */
  | 'command'
  /** 命令输出 */
  | 'command_output'
  /** mcp工具结果 */
  | 'use_mcp_tool_result'
  /** 工具 */
  | 'tool'
  /** 检查点创建 */
  | 'checkpoint_created'
  /** 浏览器工具 */
  | 'browserAction'
  | 'testCase'
  /** 解析 figma 结果 */
  | 'parse_figma_result'
  /** 项目预览块 */
  | 'project_preview'
  /** research 相关 */
  | 'render_research_plan'
  | 'update_phase'
  | 'research_start'
  | 'research_end'
  /** 检查点签出 */
  | 'command_status_check'
  | 'command_status_check_result'
  | 'wiki_result'
  /** 代码格式化输出 */
  | 'code_formatter_result';

export interface DeviceInfo {
  deviceId?: string;
  deviceModel?: string;
  deviceName?: string;
  deviceOsName?: string;
  deviceOsVersion?: string;
  ide?: string;
  ideVersion?: string;
  platform?: string;
  pluginVersion?: string;
}

export interface ProjectInfo {
  gitUrl?: string;
  openedFilePath?: string;
  projectName?: string;
}

export interface ChatRequest {
  mode?: string;
  chatId?: string;
  deviceInfo?: DeviceInfo;
  messages: MessageParamVersion1[] | Omit<MessageParamVersion0, 'version'>[];
  model?: string;
  projectInfo?: ProjectInfo;
  refFiles?: number[];
  sessionId: string;
  systemPrompt?: string;
  useSearch?: boolean;
  username?: string;
  jwtToken?: string;
  // 工具
  tools?: Chat.Tool[];
  modelConfig?: ModelConfig;
}

export type IAgentMode = 'jam' | 'duet';
export type JAM_MODE_PHASE = 'preResearch' | 'research' | 'action';

export type APIChatRequestData = Omit<ChatRequest, 'messages'> & {
  messages: Omit<MessageParamVersion0, 'version'>[];
};

export interface ChatResponse {
  message: {
    content: string;
  };
  traceId: string;
}

export type TextBlockParamToolSection = {
  category: 'tool-response' | 'tool-feedback' | 'tool-title' | 'tool-exception';
  toolName: ToolUseName;
  params: ToolUse['params'];
  toolId?: string;
};
// export interface TextBlockParam {
//   text: string;

//   type: "text";
// }


type UserActionFileData = {
  action: 'user_delete_file' | 'user_add_file',
  data: string[]
}

type TextBlockParamUserActionSection = {
  category: 'user-action';
  data: UserActionFileData
}

type TextBlockParamUserInputSection = {
  category: 'user-input';
};

type TextBlockParamFormatResponseSection = {
  category: 'format-response';
};

type TextBlockParamEnvironmentDetailsSection = {
  category: 'environment-details';
};

type TextBlockParamAssistantSection = {
  category: 'assistant';
};
type TextBlockParamProjectWikiSection = {
  category: 'project-wiki';
};
type TextBlockParamPhaseSection = {
  category: 'phase';
};

type TextBlockParamVersion0 = {
  text: string;
  type: 'text';
};

export type TextBlockParamVersion1 = TextBlockParamVersion0 &
  (
    | TextBlockParamToolSection
    | TextBlockParamUserInputSection
    | TextBlockParamFormatResponseSection
    | TextBlockParamEnvironmentDetailsSection
    | TextBlockParamAssistantSection
    | TextBlockParamProjectWikiSection
    | TextBlockParamPhaseSection
    | TextBlockParamUserActionSection
  );

export type TextBlockParam = TextBlockParamVersion0 | TextBlockParamVersion1;

export type ToolResponse = string | Array<TextBlockParamVersion0 | ImageBlockParam>;

export interface MessageParamVersion0 {
  content: string | Array<TextBlockParamVersion0>;

  role: 'user' | 'assistant';
  chatId: string;
  version: 0;
}

export interface MessageParamVersion1 {
  content: string | Array<TextBlockParamVersion1 | ImageBlockParam>;

  role: 'user' | 'assistant';
  chatId: string;
  version: 1;
}

export type MessageParam = MessageParamVersion0 | MessageParamVersion1;

export type ImageBlockParam = {
  source: {
    type: 'url';
    url: string;
  };
  type: 'image';
  category: 'image';
};

// export interface MessageParam {
//   content: string | Array<TextBlockParam>;

//   role: 'user' | 'assistant';
//   chatId?: string;
// }

/*
editFile 参数：
path: 文件路径
content: 文件内容
instructions: 编辑指令
language: 文件语言
*/
/*
readFile 参数：
path: 文件路径
*/
/*
listFilesTopLevel 参数：
path: 文件路径
*/
/*
listFilesRecursive 参数：
path: 文件路径
*/
/*
grepSearch 参数：
path: 文件路径
regex: 正则表达式
file_pattern: 文件模式
*/
/*
codebaseSearch 参数：
query: 查询语句
target_directories: 目标目录
content: 内容
*/
/*
executeCommand 参数：
command: 命令
is_background: 是否后台执行
*/

export interface SayTool {
  tool:
  | 'updatePhase'
  | 'renderResearchPlan'
  | 'editFile'
  | 'searchAndReplace'
  | 'writeToFile'
  | 'readFile'
  | 'listFilesTopLevel'
  | 'listFilesRecursive'
  // | "listCodeDefinitionNames"
  // | "searchFiles"
  | 'grepSearch'
  | 'codebaseSearch'
  | 'useMcpTool'
  | 'executeCommand'
  | 'project_preview'
  | 'parseFigma'
  | 'getFigmaPreviewImage'
  | 'figmaExtraLong'
  | 'searchWeb'
  | 'fetchWeb'
  | 'saveMemory'
  | 'searchMemory'
  | 'getMemory'
  | 'writeTodo'
  | 'testCase'
  | 'browserAction'
  | 'readTodo'
  | 'searchSpec';
  path?: string;
  query?: string;
  target_directories?: string;
  diff?: string;
  content?: string;
  regex?: string;
  filePattern?: string;
  instructions?: string;
  language?: string;
  is_background?: boolean;
  startLine?: number;
  endLine?: number;
  shouldReadEntireFile?: boolean;
  hl?: string;
  gl?: string;
  url?: string;
  nocache?: string;
  url?: string;
  // 记忆工具相关属性
  mem_type?: string | string[];
  size?: number;
  from?: number;
  memory_id?: string;
  // 用于工具版本升级
  tool_version?: 'v1' | 'v2';
  searchReplaceInfo?: SearchReplace[];
}

export interface SearchReplace {
  search: string;
  replace: string;
}

export interface ReplaceInFileRequest {
  path: string;
  searchReplaceInfo: SearchReplace[];
  diff?: string;
  sessionId?: string;
  chatId?: string;
}

export interface EditFileRequest {
  path: string;
  content: string;
  instructions?: string;
  language?:
  | 'javascript'
  | 'typescript'
  | 'python'
  | 'java'
  | 'html'
  | 'css'
  | 'scss'
  | 'less'
  | 'json'
  | 'jsonc'
  | 'yaml'
  | 'markdown'
  | 'vue'
  | 'vue-html'
  | 'xml'
  | 'php'
  | 'shellscript'
  | 'tsx'
  | 'jsx'
  | 'sql'
  | 'wasm'
  | 'r'
  | 'ruby'
  | 'rust'
  | 'javascriptreact'
  | 'typescriptreact'
  | 'c'
  | 'csharp'
  | 'plaintext'
  | 'jade'
  | 'dockerfile'
  | 'js'
  | 'ts'
  | 'py'
  | 'yml'
  | 'md'
  | 'default';
  lastCheckpointHash?: string;
  sessionId?: string;
  chatId?: string;
}

export interface EditFileResponse {
  type: 'success' | 'failed';
  content: string;
  newFile?: boolean;
  noModified?: boolean;
}

export interface ExecuteCommandResponse {
  userSkip: boolean;
  result: string;
  completed?: boolean;
}

export interface CommandStatusCheckResponse {
  status: 'success' | 'skip' | 'error';
  output: string;
}

export interface DiffSet {
  relativePath: string;
  absolutePath: string;
  before: string;
  after: string;
}

export namespace Chat {
  export interface Function {
    name: string;
    description: string;
    strict: boolean;
    parameters: {
      type: 'object';
      properties: {
        [key: string]: {
          type: string;
          description?: string;
          enum?: string[];
          items?: {
            type: string;
            description?: string;
            properties?: {
              [key: string]: {
                type: string;
                description?: string;
              };
            };
            required?: string[];
          };
        };
      };
      required?: string[];
    };
  }

  export interface Tool {
    type: 'function' | 'mcpFunction';
    function: Function;
  }

  export interface FunctionCall {
    name: string;
    arguments: string;
  }

  export interface ToolCall {
    id: string;
    index: number;
    type: string;
    function: FunctionCall;
  }

  export interface LocalFile {
    path: string;
    content: string;
  }

  export interface TextBlockContent {
    type: 'text';
    text?: string;
  }

  export interface ImageBlockContent {
    type: 'image';
    source: {
      type: 'url';
      url: string;
    };
  }

  export interface AgentChatMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: (TextBlockContent | ImageBlockContent)[];
    name?: string;
    tool_calls?: ToolCall[];
    files?: LocalFile[];
    chatId?: string;
    tool_call_id?: string;
  }

  export enum RuleType {
    USER_RULES = 0,
    PROJECT_RULES = 1
  }

  export interface ChatRule {
    type: RuleType;
    content: string;
  }

  export interface Environment {
    workingDirectoryFiles: string[];
    workingDirectory: string;
    visibleFiles: string[];
    openTabs: string[];
  }

  export interface AgentChatRequest {
    // 会话id
    sessionId: string;
    // 聊天id
    chatId: string;
    // 模型
    model: string;
    // 消息
    messages: AgentChatMessage[] | ChatRequest.messages;
    // 工具
    tools?: Tool[];
    // 规则
    rules?: ChatRule[];
    // 环境变量
    environment?: Environment;
    // 轮次
    round: number;
    // 工具
    tools?: Tool[];
    // 系统提示词
    systemPrompt?: string;
    // mode，默认是 agent
    mode?: 'ask' | 'agent' | 'duet';
  }

  export enum AgentChatStatus {
    UNKNOWN = 0,
    SUCCESS = 1,
    FAIL = 2
  }

  export interface AgentChatResponse {
    choices: {
      index: number;
      message: AgentChatMessage;
      finishReason: 'stop' | 'tool_calls' | 'length';
    }[];
    status: AgentChatStatus;
    errorMessage: string;
    traceId?: string;
    code?: number;
    tip?: string;
  }
}

export type TodoItemStatus = 'pending' | 'in_progress' | 'completed';
export interface TodoItem {
  /* 任务唯一 ID， 不会重复 */
  id: string;
  /* 任务内容 */
  content: string;
  description: string;
  /* 任务状态 */
  status: TodoItemStatus;
}

export interface TodoWriteContent {
  todos: TodoItem[];
  type: 'write' | 'update';
}
