import { isSameViewNode, mergeNodeStyles } from '../../utils';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';

/**
 * 基础规则调优：
 * 1、冗余图层
 * 2、相同尺寸图层作为背景图片
 * 3、其它，待完善
 */
export function baseTuning(node: NodeDefinition, traverseContext: TraverseContext) {
  // 图片或者组件，不参与图层优化
  if (node.nodeType === 'component' || node.nodeType === 'image') {
    return;
  }

  // 图层优化：合并相同尺寸的图层
  mergeSameSizeNode(node, traverseContext);

  // 图层优化：去除文字图层无样式的容器图层，
  // 暂时去除这个优化，边界条件较多，影响布局
  // removeUnusedTextContainer(node, traverseContext);

  if (node.children?.length) {
    node.children.forEach(child => baseTuning(child, traverseContext));
  }
}

// 去除图层
function mergeNodeWithParent(node: NodeDefinition, sameNode: NodeDefinition) {
  node.children.splice(node.children.indexOf(sameNode), 1);
  sameNode.children?.length && (node.children = node.children.concat(sameNode.children));
}

/**
 * 合并相同尺寸的图层
 * 判断条件：坐标、尺寸、旋转一致的嵌套图层
 * 处理逻辑：1、合并图层；2、合并样式信息
 */
function mergeSameSizeNode(node: NodeDefinition, traverseContext: TraverseContext) {
  if (node.children?.length > 1) {
    // 处理兄弟节点里 boundingRect相同的节点对
    for (let i = 0; i < node.children.length; i++) {
      for (let j = i + 1; j < node.children.length; j++) {
        if (isSameViewNode(node.children[i], node.children[j])) {
          let sourceNodeIndex = i;
          let targetNodeIndex = j;

          // 根据zIndex决定谁合并谁（较高zIndex的节点作为保留目标）
          const zIndexI = node.children[i].styles?.zIndex !== undefined
            ? Number(node.children[i].styles.zIndex)
            : 0;
          const zIndexJ = node.children[j].styles?.zIndex !== undefined
            ? Number(node.children[j].styles.zIndex)
            : 0;

          if (zIndexI > zIndexJ) {
            sourceNodeIndex = j;
            targetNodeIndex = i;
          } else {
            sourceNodeIndex = i;
            targetNodeIndex = j;
          }

          const sourceNode = node.children[sourceNodeIndex];
          const targetNode = node.children[targetNodeIndex];

          // 合并样式
          targetNode.styles = mergeNodeStyles(targetNode.styles, sourceNode.styles);

          // 删除源节点
          node.children.splice(i, 1);
          i--;
          break;
        }
      }
    }

    const sameSizeNodes = node.children.filter(child => isSameViewNode(node, child));

    if (sameSizeNodes.length === 1) {
      const sameNode = sameSizeNodes[0];

      switch (sameNode.nodeType) {
        case 'view':
          // 合并样式属性
          node.styles = mergeNodeStyles(node.styles, sameNode.styles);
          mergeNodeWithParent(node, sameNode);
          mergeSameSizeNode(node, traverseContext);
          break;

        case 'image':
          // 只有在父节点没有背景时才设置背景图
          if (!node?.styles.background) {
            if (sameNode.imageUrl) {
              node.styles.background = `url(${sameNode.imageUrl}) center / cover no-repeat`;
              mergeNodeWithParent(node, sameNode);
              mergeSameSizeNode(node, traverseContext);
            }
          }
          break;
      }
    }
  } else if (node.children?.length === 1 && isSameViewNode(node, node.children[0])) {
    const childNode = node.children[0];

    if (childNode.nodeType === 'view') {
      // 合并图层
      node.children = childNode.children;
      // 合并属性
      node.styles = mergeNodeStyles(node.styles, childNode.styles);
    }

    if (childNode.nodeType === 'image') {
      node.styles.background = `url(${childNode.imageUrl} center / cover no-repeat`;
      node.children = [];
    }

    mergeSameSizeNode(node, traverseContext);
  }
}

/**
 * 去除文字图层的容器图层
 * 判断条件：Frame 或者 Group 图层，没有"样式"比如：border 和 background 以及旋转
 * 处理逻辑：去除容器图层
 */
function removeUnusedTextContainer(node: NodeDefinition, traverseContext: TraverseContext) {
  if (!node.children?.length) {
    return;
  }

  // 递归处理子节点（自底向上）
  node.children.forEach(child => removeUnusedTextContainer(child, traverseContext));

  // 检查当前节点的子节点中是否有"无样式容器"
  node.children = node.children.flatMap(child => {
    // 条件1: 子节点是 'view' 类型 (对应 Figma 的 Frame 或 Group)
    if (child.nodeType !== 'view') {
      return child; // 非 view 类型的子节点保持不变
    }

    const hasOnlyOneTextChild = child.children?.length === 1 && child.children[0].nodeType === 'text';

    if (!hasOnlyOneTextChild) {
      return child;
    }

    // 条件3: 子节点没有边框、背景或旋转样式
    const hasStyle = child.styles?.display
      || child.styles?.border
      || child.styles?.background
      || child.styles?.transform
      || child.styles?.boxShadow
      || child.styles?.borderWidth
      || child.styles?.borderColor
      || child.styles?.borderRadius
      || Boolean(child.styles?.opacity ?? 1)
      || child.styles?.borderTopWidth
      || child.styles?.borderTopColor
      || child.styles?.borderTopStyle
      || child.styles?.borderRightWidth
      || child.styles?.borderRightColor
      || child.styles?.borderRightStyle
      || child.styles?.borderBottomWidth
      || child.styles?.borderBottomColor
      || child.styles?.borderBottomStyle
      || child.styles?.borderLeftWidth
      || child.styles?.borderLeftColor
      || child.styles?.borderLeftStyle
      || child.styles?.outlineWidth
      || child.styles?.outlineStyle
      || child.styles?.outlineColor;

    const hasLayout = child.styles?.position === 'absolute'
      || child.styles?.position === 'fixed'
      || child.styles?.display;

    if (hasStyle || hasLayout) {
      return child; // 有样式的 view 类型子节点保持不变
    }

    // 如果所有条件都满足，则此子节点是一个"无样式容器"，将其子节点提升到当前位置
    return child.children || [];
  });
}
