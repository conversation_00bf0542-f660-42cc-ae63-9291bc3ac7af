import { cloneDeep, pick, omit } from 'lodash-es';
import {
  isContain,
  computeNodeArea,
  reComputeAbsolute,
  removeAbsoluteProperties,
  noNeedTuningNode,
} from '../../utils/layout';
import { LAYOUT_PROPS, LAYOUT_TYPES, POSITION_PROPS } from '../../const';

import type { TraverseContext } from '../../index';
import type { NodeDefinition } from '../../types';

const removeChildrenNode = (node: NodeDefinition, targetNode: NodeDefinition) => {
  if (!node.children) {
    return;
  }

  const index = node.children.findIndex(item => item.id === targetNode.id);
  node.children.splice(index, 1);
};

const sortChildrenByIndexAndCoord = (node: NodeDefinition) => {
  node.children.sort((a, b) => {
    // 首先比较 zIndex
    const aIndex = a.zIndex as number ?? 0;
    const bIndex = b.zIndex as number ?? 0;
    const indexDiff = aIndex - bIndex;

    if (indexDiff !== 0) return indexDiff;

    // 比较 Y 轴位置
    const yDiff = a.boundingRect.y - b.boundingRect.y;

    if (yDiff !== 0) return yDiff;

    // Y 轴相同时比较 X 轴
    return a.boundingRect.x - b.boundingRect.x;
  })
};

export default class RegroupContainProcessor {
  private traverseContext: TraverseContext;

  constructor(traverseContext: TraverseContext) {
    this.traverseContext = traverseContext;
  }

  resolve(node: NodeDefinition) {
    this.processComponent(node);
  }

  processComponent(node: NodeDefinition) {
    if (noNeedTuningNode(node)) {
      return;
    }

    const isFlexWrapper = node.styles.display === 'flex';

    // FlexLayout、标记过布局类型的节点不需要处理子元素的包含关系
    // TODO: 优化 - Flex 容器子元素可以设置绝对定位
    if (!isFlexWrapper && !node.layoutType && node.children.length > 1) {
      // 处理当前元素的子元素的包含关系
      this.processChildNodes(node);
    }

    // 继续处理子元素
    node.children.forEach(child => {
      this.resolve(child);
    });
  }

  processChildNodes(node: NodeDefinition) {
    // 该 component 的子图层中是否有图层包含关系
    let hasContainLayer = false;

    // 按面积从小到大排序，为了先合并最小图层，一层层合并
    const sortedChildren = cloneDeep(node.children || []).sort((node1, node2) => {
      const node1Area = computeNodeArea(node1);
      const node2Area = computeNodeArea(node2);

      return node1Area > node2Area ? 1 : -1;
    });

    const nodeArea = node.boundingRect.width * node.boundingRect.height;
    const childrenLen = sortedChildren.length;

    for (let i = 0; i < childrenLen; i++) {
      const baseNode = sortedChildren[i];

      for (let j = i + 1; j < childrenLen; j++) {
        const compareNode = sortedChildren[j];
        const compareNodeArea = computeNodeArea(compareNode);
        const baseNodeArea = computeNodeArea(compareNode);

        // 与父图层一样大的图层不参与图层合并
        if (compareNodeArea === nodeArea || baseNodeArea === nodeArea) {
          continue;
        }

        // compareNode 需要比 baseNode 面积大
        if (isContain(compareNode, baseNode)) {
          // 较大图层设置了旋转属性，不处理
          if (compareNode.styles?.transform?.includes('rotate')) {
            continue;
          }

          hasContainLayer = true;
          const rawCompareNode = node.children.find(item => item.id === compareNode.id);
          const rawBaseNode = node.children.find(item => item.id === baseNode.id);

          if (!rawCompareNode || !rawBaseNode) {
            continue;
          }

          this.mergeNodes(rawCompareNode, rawBaseNode, node);

          break;
        }
      }

      if (hasContainLayer) {
        break;
      }
    }

    if (hasContainLayer) {
      this.processComponent(node);
    }
  }

  mergeNodes(biggerNode: NodeDefinition, smallerNode: NodeDefinition, node: NodeDefinition) {
    // 如果 biggerNode 是组件或者图片，新建一个公共父图层
    if (biggerNode.nodeType === 'component' || biggerNode.nodeType === 'image') {

      const mergedNode: NodeDefinition = {
        id: `container_${biggerNode.id}_${smallerNode.id}`,
        name: `container_${biggerNode.name}_${smallerNode.name}`,
        nodeType: 'view',
        styles: pick(biggerNode.styles, LAYOUT_PROPS),
        renderBoundingRect: { ...biggerNode.renderBoundingRect },
        boundingRect: { ...biggerNode.boundingRect },
        children: [],
        parentId: node.id,
        zIndex: Math.max(biggerNode.zIndex as number, smallerNode.zIndex as number),
        layoutType: LAYOUT_TYPES.Relative,
      };

      const isBiggerIsImg = biggerNode.nodeType === 'image';

      if (isBiggerIsImg) {
        biggerNode.imageUrl && (mergedNode.styles.background = `url(${biggerNode.imageUrl}) center / cover no-repeat`);
        mergedNode.children.push(smallerNode);
      } else {
        mergedNode.children = [biggerNode, smallerNode];
        biggerNode.parentId = mergedNode.id

        // 修改 biggerNode css，去除绝对定位元素
        removeAbsoluteProperties(biggerNode);
      }

      // 修改 smallerNode 的 parentId
      smallerNode.parentId = mergedNode.id;

      // 修改 smallerNode css，重新计算 absolute 相对位置的值
      if (smallerNode.styles.position === 'absolute') {
        reComputeAbsolute(smallerNode, mergedNode);
      }

      // 更改 children
      removeChildrenNode(node, smallerNode);
      removeChildrenNode(node, biggerNode);

      node.children.push(mergedNode);
    } else {
      // biggerNode 不是组件，直接将 smallerNode 移到 biggerNode 的子图层中
      smallerNode.parentId = biggerNode.id;

      // 修改 smallerNode css，重新计算 absolute 相对位置的值
      if (smallerNode.styles.position === 'absolute') {
        reComputeAbsolute(smallerNode, biggerNode);
      }

      // 将 smallerNode 移到 biggerNode 的子图层中
      biggerNode.children.push(smallerNode);
      sortChildrenByIndexAndCoord(biggerNode);
      removeChildrenNode(node, smallerNode);
    }
  }

  convertAbsoluteToRelative(node: NodeDefinition, parentNode: NodeDefinition) {
    // const { x: pX, y: pY } = parentNode.boundingRect;
    // const { x, y } = node.nodeType === 'text' ? node.renderBoundingRect : node.boundingRect;

    // if (this.traverseContext.nodeRawMap?.[node.id]) {
    //   const { strokes, strokeWeight, strokeAlign } = this.traverseContext.nodeRawMap?.[node.id];
    // }

    // const borderWidth = strokes?.length > 0 && typeof strokeWeight === 'number' ? strokeWeight : 0;

    // TODO: 待优化 - 处理父元素的 padding？

    removeAbsoluteProperties(node);
  }
}
