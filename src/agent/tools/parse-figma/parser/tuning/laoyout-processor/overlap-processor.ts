import { remove, omit } from 'lodash-es';
import {
  hasOverlap,
  isContain,
  computeNodeArea,
  reComputeAbsolute,
  removeAbsoluteProperties,
  noNeedTuningNode,
} from '../../utils/layout';
import { LAYOUT_TYPES } from '../../const';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';

export default class RegroupOverlapProcessor {
  private traverseContext: TraverseContext;

  constructor(traverseContext: TraverseContext) {
    this.traverseContext = traverseContext;
  }

  resolve(node: NodeDefinition) {
    this.processNode(node);
  }

  processNode(node: NodeDefinition) {
    if (noNeedTuningNode(node)) {
      return;
    }

    // FlexLayout、标记过布局类型的节点不需要处理子元素的交叉关系
    if (node.children.length > 1 && node.styles.display !== 'flex' && !node.layoutType) {
      this.processOverlapNode(node);
    }

    node.children.forEach(child => {
      this.resolve(child);
    });
  }

  processOverlapNode(node: NodeDefinition) {
    const overlaps: Set<NodeDefinition>[] = [];
    const childrenLen = node.children.length;

    for (let i = 0; i < childrenLen; i++) {
      const baseNode = node.children[i];

      for (let j = i + 1; j < childrenLen; j++) {
        const comparedNode = node.children[j];

        // 忽略作为底层背景的图片
        if (
          (isContain(baseNode, comparedNode) && baseNode.nodeType === 'image')
          || (isContain(comparedNode, baseNode) && comparedNode.nodeType === 'image')
        ) {
          continue;
        }

        // 与父图层一样大的图层不参与图层合并
        if (computeNodeArea(node) === computeNodeArea(baseNode)) {
          baseNode.layoutType = LAYOUT_TYPES.Absolute;
          continue;
        }

        if (computeNodeArea(node) === computeNodeArea(comparedNode)) {
          comparedNode.layoutType = LAYOUT_TYPES.Absolute;
          continue;
        }

        if (hasOverlap(baseNode, comparedNode)) {
          this.recordOverlap(overlaps, baseNode, comparedNode);
        }
      }
    }

    if (overlaps.length === 0) {
      return;
    }

    // 将交叉关系进行合并
    const finalOverlaps = this.mergeOverlaps(overlaps);

    // 如果当前节点的所有子元素之间都相互交叉，无需处理；子元素都按照绝对定位处理
    if (finalOverlaps.length === 1 && finalOverlaps[0].size === node.children.length) {
      node.layoutType = LAYOUT_TYPES.Relative;

      // 子元素绝对定位
      node.children.forEach(child => {
        child.layoutType = LAYOUT_TYPES.Absolute;
      });
    } else {
      finalOverlaps.forEach(overlap => {
        this.mergeNode(node, overlap);
      });

      // 对当前节点再计算一次交叉关系，因为第一次合并后产生的新图层有可能也会交叉
      if (finalOverlaps.length > 0) {
        this.processNode(node);
      }
    }
  }

  recordOverlap(overlaps: Set<NodeDefinition>[], baseNode: NodeDefinition, comparedNode: NodeDefinition) {
    // 记录交叉关系
    let isComponentAlreadyInOverlaps = false;

    overlaps.forEach(overlap => {
      if (overlap.has(baseNode)) {
        isComponentAlreadyInOverlaps = true;
        overlap.add(comparedNode);
      } else if (overlap.has(comparedNode)) {
        isComponentAlreadyInOverlaps = true;
        overlap.add(baseNode);
      }
    });

    if (!isComponentAlreadyInOverlaps) {
      overlaps.push(new Set([baseNode, comparedNode]));
    }
  }

  // 对交叉关系的数组进行合并操作，最终得到几个互不包含的set
  mergeOverlaps(overlaps: Set<NodeDefinition>[]): Set<NodeDefinition>[] {
    if (overlaps.length <= 1) {
      return overlaps;
    }

    // 每次只做一次合并操作，递归多次合并操作，最终得到几个互不包含的set
    let hasMergeOverlap = false;
    const processOverlaps = [...overlaps];
    const overlapLen = overlaps.length;

    for (let i = 0; i < overlapLen; i++) {
      const baseOverlap = overlaps[i];

      for (let j = i + 1; j < overlapLen; j++) {
        const compareOverlap = overlaps[j];

        if ([...baseOverlap].filter(item => compareOverlap.has(item)).length > 0) {
          hasMergeOverlap = true;

          processOverlaps.push(new Set([...baseOverlap, ...compareOverlap]));
          remove(processOverlaps, item => item === baseOverlap);
          remove(processOverlaps, item => item === compareOverlap);
          break;
        }
      }

      if (hasMergeOverlap) {
        break;
      }
    }

    return hasMergeOverlap ? this.mergeOverlaps(processOverlaps) : processOverlaps;
  }

  // 合并 overlap 中记录的节点
  mergeNode(node: NodeDefinition, overlap: Set<NodeDefinition>) {
    // 新建 mergeNode，将 overlap 中记录的组件包到 mergedNode 中
    // @ts-ignore
    const mergedNode: NodeDefinition = {
      id: '',
      name: '',
      nodeType: 'view',
      parentId: node.id,
      styles: {},
      children: [],
      layoutType: LAYOUT_TYPES.Relative,
    };

    const overlapNodes = [...overlap].sort((a, b) => {
      const aIndex = a.zIndex as number ?? 0;
      const bIndex = b.zIndex as number ?? 0;
      const indexDiff = aIndex - bIndex;

      if (indexDiff !== 0) return indexDiff;

      const xAxis = a.boundingRect.x - b.boundingRect.x;
      const yAxis = a.boundingRect.y - b.boundingRect.y;

      // 从上到下，从左到右
      return yAxis === 0 ? xAxis : yAxis;
    });
    const mergedName = `overlap_${overlapNodes.map(overlapNode => overlapNode.name).join('_')}`;
    const mergedId = `overlap_${overlapNodes.map(overlapNode => overlapNode.id).join('_')}`;
    const coordX = Math.min(...overlapNodes.map(item => item.boundingRect.x));
    const coordY = Math.min(...overlapNodes.map(item => item.boundingRect.y));
    const coordRight = Math.max(...overlapNodes.map(item => item.boundingRect.right));
    const coordBottom = Math.max(...overlapNodes.map(item => item.boundingRect.bottom));
    const zIndex = Math.max(...overlapNodes.map(item => item.zIndex as number ?? 0));

    // 计算容器的坐标和坐标
    overlapNodes.forEach(overlapNode => {
      overlapNode.parentId = mergedId;

      remove(node.children, item => item.id === overlapNode.id || item === overlapNode);
    });

    mergedNode.id = mergedId;
    mergedNode.name = mergedName;
    mergedNode.zIndex = zIndex;
    mergedNode.children = overlapNodes;

    mergedNode.styles = {
      position: 'relative',
    };

    // 重新计算交叉节点的布局信息
    mergedNode.boundingRect = {
      x: coordX,
      y: coordY,
      width: coordRight - coordX,
      height: coordBottom - coordY,
      left: coordX,
      top: coordY,
      right: coordRight,
      bottom: coordBottom,
    };
    mergedNode.styles.width = `${mergedNode.boundingRect.width}px`;
    mergedNode.styles.height = `${mergedNode.boundingRect.height}px`;
    mergedNode.styles.top = `${coordY - node.boundingRect.y}px`;
    mergedNode.styles.left = `${coordX - node.boundingRect.x}px`;

    overlapNodes.forEach(overlapNode => {
      overlapNode.layoutType = LAYOUT_TYPES.Absolute;
      reComputeAbsolute(overlapNode, mergedNode);
    });

    node.children.push(mergedNode);

    // 暂时不采用这套逻辑
    // // 两个节点交叉：靠近左侧的节点作为非绝对定位元素，另外一个绝对定位
    // if (overlapNodes.length === 2) {
    //   const minXChild = [...overlapNodes].sort((a, b) => (a.boundingRect.x - b.boundingRect.x || 0))[0];

    //   mergedNode.boundingRect = { ...minXChild?.boundingRect };
    //   mergedNode.styles.width = `${mergedNode.boundingRect.width}px`;
    //   mergedNode.styles.height = `${mergedNode.boundingRect.height}px`;

    //   overlapNodes.forEach(overlapNode => {
    //     if (overlapNode.id === minXChild.id) {
    //       removeAbsoluteProperties(overlapNode);
    //     } else {
    //       reComputeAbsolute(overlapNode, mergedNode);
    //     }
    //   });
    // } else {
    //   // 两个以上的交叉节点：都按照绝对定位处理，重新计算子节点的位置
    //   mergedNode.boundingRect = {
    //     x: coordX,
    //     y: coordY,
    //     width: coordRight - coordX,
    //     height: coordBottom - coordY,
    //     left: coordX,
    //     top: coordY,
    //     right: coordRight,
    //     bottom: coordBottom,
    //   };
    //   mergedNode.styles.width = `${mergedNode.boundingRect.width}px`;
    //   mergedNode.styles.height = `${mergedNode.boundingRect.height}px`;

    //   overlapNodes.forEach(overlapNode => {
    //     overlapNode.layoutType = LAYOUT_TYPES.Absolute;
    //     reComputeAbsolute(overlapNode, mergedNode);
    //   });
    // }
  }
}
