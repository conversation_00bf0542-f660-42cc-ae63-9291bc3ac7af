import { remove } from 'lodash-es';
import {
  isRectangleIntersectionX,
  isRectangleIntersectionY,
  computeNodeArea,
  reComputeAbsolute,
  noNeedTuningNode,
} from '../../utils/layout';
import { LAYOUT_TYPES, LinearTypes } from '../../const';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';

export default class RegroupLinearProcessor {
  private traverseContext: TraverseContext;

  constructor(traverseContext: TraverseContext) {
    this.traverseContext = traverseContext;
  }

  resolve(node: NodeDefinition) {
    this.processNode(node);
  }

  processNode(node: NodeDefinition) {
    // 「可识别的组件、不包含子元素的节点」无需处理
    if (noNeedTuningNode(node)) {
      return;
    }

    const isFlexWrapper = node.styles.display === 'flex';

    // FlexLayout、标记过布局类型的节点不需要处理子元素的横纵关系
    // if (node.children.length >= 1 && !isFlexWrapper && !node.layoutType) {
    if (node.children.length >= 1 && !isFlexWrapper) {
      this.processLinearNode(node);
    }

    // TODO: 考虑 - 是否处理没有被识别为组件，但是类型是 COMPONENT 的节点
    node.children.forEach(child => {
      this.resolve(child);
    });
  }

  processLinearNode(node: NodeDefinition) {
    const linearRelation = this.getLinearRelation(node);

    switch (linearRelation) {
      case LinearTypes.VERTICAL:
        node.layoutType = LAYOUT_TYPES.FlexVertical;
        break;
      case LinearTypes.HORIZONTAL:
        node.layoutType = LAYOUT_TYPES.FlexHorizontal;
        break;

      case LinearTypes.UNKNOWN: {
        // 1. 先纵向合并，再横向合并
        const hasMerged = this.mergeLinear(node, LinearTypes.VERTICAL);

        if (!hasMerged) {
          this.mergeLinear(node, LinearTypes.HORIZONTAL);
        }
        break;
      }
      default:
        break;
    }
  }

  // 获取节点的线性关系：Vertical、Horizontal、Unkown
  getLinearRelation(node: NodeDefinition): LinearTypes {
    // TODO: 优化 - 优先合并的方向，是否可以更精确

    let isVerticalRelation = true;
    let isHorizontalRelation = true;
    const childrenLen = node.children.length;

    for (let i = 0; i < childrenLen; i++) {
      for (let j = i + 1; j < childrenLen; j++) {
        const node1 = node.children[i];
        const node2 = node.children[j];

        // 垂直方向是否有重叠
        if (isVerticalRelation && isRectangleIntersectionY(node1, node2)) {
          isVerticalRelation = false;
        }

        // 水平方向是否有重叠
        if (isHorizontalRelation && isRectangleIntersectionX(node1, node2)) {
          isHorizontalRelation = false;
        }
      }
    }

    if (isVerticalRelation) {
      return LinearTypes.VERTICAL;
    }

    if (isHorizontalRelation) {
      return LinearTypes.HORIZONTAL;
    }

    return LinearTypes.UNKNOWN;
  }

  mergeLinear(node: NodeDefinition, direction: LinearTypes) {
    // 进行纵向分组划分
    const intersectionList: Set<NodeDefinition>[] = [];
    const childrenLen = node.children.length;

    for (let i = 0; i < childrenLen; i++) {
      const baseNode = node.children[i];

      for (let j = i + 1; j < childrenLen; j++) {
        const compareNode = node.children[j];

        // 与父图层一样大的图层不参与图层合并
        if (computeNodeArea(node) === computeNodeArea(baseNode)) {
          baseNode.layoutType = LAYOUT_TYPES.Absolute;
          continue;
        }

        // 与父图层一样大的图层不参与图层合并
        if (computeNodeArea(node) === computeNodeArea(compareNode)) {
          compareNode.layoutType = LAYOUT_TYPES.Absolute;
          continue;
        }

        // 忽略作为底层背景的图片
        // TODO: 暂时注释
        // if (
        //   (isContain(baseNode, compareNode) && baseNode.nodeType === 'image')
        //   || (isContain(compareNode, baseNode) && compareNode.nodeType === 'image')
        // ) {
        //   continue;
        // }

        if (direction === LinearTypes.VERTICAL && isRectangleIntersectionY(baseNode, compareNode)) {
          this.recordIntersections(intersectionList, baseNode, compareNode);
        }

        if (direction === LinearTypes.HORIZONTAL && isRectangleIntersectionX(baseNode, compareNode)) {
          this.recordIntersections(intersectionList, baseNode, compareNode);
        }
      }
    }

    if (intersectionList.length < 1) {
      return false;
    }

    // 这里有两种情况：
    // 1、所有的子节点都在一个重叠集合里面，说明该节点的子节点无法用此线性规则描述
    // 2、只有一个重叠集合，但是子节点的个数比重叠集合的节点个数多，此时需要合并重叠集合
    if (intersectionList.length === 1 && node.children.length === intersectionList[0].size) {
      return false;
    }

    // 开始合并交叉关系
    const finalIntersections = this.mergeIntersectionList(intersectionList);

    // 合并之后需要再次判断：所有的子节点都在一个重叠集合里面，说明该节点的子节点无法用此线性规则描述
    if (finalIntersections.length === 1 && node.children.length === finalIntersections[0].size) {
      return false;
    }

    finalIntersections.forEach(intersectionSet => {
      this.mergeNodes(node, intersectionSet, direction);
    });

    return true;
  }

  recordIntersections(intersectionList: Set<NodeDefinition>[], baseNode: NodeDefinition, compareNode: NodeDefinition) {
    let nodeAlreadyInIntersectionList = false;

    intersectionList.forEach(intersection => {
      if (intersection.has(baseNode)) {
        nodeAlreadyInIntersectionList = true;
        intersection.add(compareNode);
      } else if (intersection.has(compareNode)) {
        nodeAlreadyInIntersectionList = true;
        intersection.add(baseNode);
      }
    });

    if (!nodeAlreadyInIntersectionList) {
      intersectionList.push(new Set([baseNode, compareNode]));
    }
  }

  // 对重叠关系数组进行合并操作，最终得到几个互不包含的 set
  mergeIntersectionList(intersectionList: Set<NodeDefinition>[]): Set<NodeDefinition>[] {
    if (intersectionList.length <= 1) {
      return intersectionList;
    }

    // 每次只做一次合并操作，递归多次合并操作，最终得到几个互不包含的set
    const processIntersectionList = [...intersectionList];
    const intersectionListLen = intersectionList.length;
    let hasMerge = false;

    for (let i = 0; i < intersectionListLen; i++) {
      const baseIntersection = intersectionList[i];

      for (let j = i + 1; j < intersectionListLen; j++) {
        const compareIntersection = intersectionList[j];

        if ([...baseIntersection].filter(item => compareIntersection.has(item)).length > 0) {
          processIntersectionList.push(new Set([...baseIntersection, ...compareIntersection]));
          remove(processIntersectionList, item => item === baseIntersection);
          remove(processIntersectionList, item => item === compareIntersection);
          hasMerge = true;
        }
      }

      if (hasMerge) {
        break;
      }
    }

    return hasMerge ? this.mergeIntersectionList(processIntersectionList) : processIntersectionList;
  }

  mergeNodes(node: NodeDefinition, intersectionSet: Set<NodeDefinition>, direction: LinearTypes) {
    // 新建 mergeNode，将 overlap 中记录的组件包到 mergedNode 中
    // @ts-ignore
    const mergedNode: NodeDefinition = {
      id: '',
      name: '',
      nodeType: 'view',
      parentId: node.id,
      styles: {},
    };

    node.layoutType = direction === LinearTypes.HORIZONTAL ? LAYOUT_TYPES.FlexHorizontal : LAYOUT_TYPES.FlexVertical;

    const { x, y } = node.boundingRect;
    const intersectionNodes = [...intersectionSet].sort((a, b) => {
      const compareVal = direction === LinearTypes.HORIZONTAL
        ? a.boundingRect.x - b.boundingRect.x
        : a.boundingRect.y - b.boundingRect.y;
      return compareVal || 0;
    });

    const mergedName = `linear_${intersectionNodes.map(overlapNode => overlapNode.name).join('_')}`;
    const mergedId = `linear_${intersectionNodes.map(overlapNode => overlapNode.id).join('_')}`;
    const mergedInfo = intersectionNodes.map(overlapNode => overlapNode.name).join(', ');
    const coordX = Math.min(...intersectionNodes.map(item => item.boundingRect.x));
    const coordY = Math.min(...intersectionNodes.map(item => item.boundingRect.y));
    const coordRight = Math.max(...intersectionNodes.map(item => item.boundingRect.right));
    const coordBottom = Math.max(...intersectionNodes.map(item => item.boundingRect.bottom));
    const zIndex = Math.max(...intersectionNodes.map(item => item.zIndex as number ?? 0));

    // 计算容器的坐标和坐标
    intersectionNodes.forEach(overlapNode => {
      overlapNode.parentId = mergedId;

      remove(node.children, item => item === overlapNode || item.id === overlapNode.id);
    });

    mergedNode.id = mergedId;
    mergedNode.name = mergedName;
    mergedNode.zIndex = zIndex;

    mergedNode.boundingRect = {
      x: coordX,
      y: coordY,
      width: coordRight - coordX,
      height: coordBottom - coordY,
      left: coordX,
      top: coordY,
      right: coordRight,
      bottom: coordBottom,
    };
    mergedNode.children = intersectionNodes;

    mergedNode.styles = {
      position: 'absolute',
      top: `${coordY - y}px`,
      left: `${coordX - x}px`,
      width: `${mergedNode.boundingRect.width}px`,
      height: `${mergedNode.boundingRect.height}px`,
    };

    // 重新计算子容器的相对位置
    intersectionNodes.forEach(overlapNode => {
      reComputeAbsolute(overlapNode, mergedNode);
    });

    node.children.push(mergedNode);
  }
}
