import { omit } from 'lodash-es';
import { getMedianOfArr } from '../../utils/common';
import { LAYOUT_TYPES, POSITION_PROPS } from '../../const';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';
import type { FrameNode } from '@figma/rest-api-spec';

export default class FlexProcessor {
  private traverseContext: TraverseContext;

  constructor(traverseContext: TraverseContext) {
    this.traverseContext = traverseContext;
  }

  resolve(node: NodeDefinition) {
    this.processNode(node);
  }

  processNode(node: NodeDefinition) {
    const processChildren = () => {
      if (node.children.length > 0) {
        node.children.forEach(childNode => this.processNode(childNode));
      }
    };

    // 如果是 flex 容器或文本节点，只处理子元素
    if (node.styles.display === 'flex' || node.nodeType === 'text') {
      processChildren();
      return;
    }

    // 处理布局信息
    this.processLayoutInfo(node);
    processChildren();
  }

  // 删除 CSS 中绝对定位相关的属性：top、right、bottom、left；position 值如果不是 absolute 要保留
  removeAbsoleteInfo(node: NodeDefinition) {
    node.styles = omit(node.styles, POSITION_PROPS);

    if (node.styles.position === 'absolute') {
      // position 需要设置为 relative - 1、组件类型节点子节点不会进行图层重组、行列等处理，内部可能存在绝对定位节点；2、子元素有 img；
      const childHasAbsolute = node.children.find(item =>
        item.layoutType === LAYOUT_TYPES.Absolute
        || item.styles?.position === 'absolute'
        || item.nodeType === 'image'
      );

      if (node.nodeType === 'component' || childHasAbsolute) {
        node.styles.position = 'relative';
      } else {
        delete node.styles.position;
      }

      if (node.styles.transform?.startsWith('translate')) {
        delete node.styles.transform;
      }
    }
  }

  // 生成布局容器的 flex 属性
  processLayoutInfo(node: NodeDefinition) {
    if (node.layoutType === LAYOUT_TYPES.FlexHorizontal) {
      node.children.forEach(childNode => {
        // TODO: 待优化 - 支持 AutoLayout 里绝对定位的子元素
        if (childNode.layoutType !== LAYOUT_TYPES.Absolute) {
          this.removeAbsoleteInfo(childNode);
        }
      });
      node.styles.display = 'flex';
      node.styles.flexDirection = 'row';

      // 设置主轴对齐
      this.processRowJustifyContent(node);
      // 设置交叉轴对齐
      this.processRowCrossAlign(node);
    } else if (node.layoutType === LAYOUT_TYPES.FlexVertical) {
      // TODO: 待优化 - 支持 AutoLayout 里绝对定位的子元素
      node.children.forEach(childNode => {
        if (childNode.name !== LAYOUT_TYPES.Absolute) {
          this.removeAbsoleteInfo(childNode);
        }
      });
      node.styles.display = 'flex';
      node.styles.flexDirection = 'column';

      // 设置主轴对齐
      this.processColJustifyContent(node);
      // 设置交叉轴对齐
      this.processColCrossAlign(node);
    } else if (node.layoutType === LAYOUT_TYPES.Relative) {
      node.styles.position = 'relative';
    } else if (node.layoutType === LAYOUT_TYPES.Absolute) {
      node.styles.position = 'absolute';
    }

    if (node.children.find(item => item.layoutType === LAYOUT_TYPES.Absolute)) {
      node.styles.position = 'relative';
    }
  }

  processRowJustifyContent(node: NodeDefinition) {
    const { x, right, width } = node.boundingRect;

    if (node.children?.length < 1) {
      return;
    }

    if (node.children?.length === 1) {
      const { x: childX, right: childRight } = node.children[0].boundingRect;
      const leftDistance = this.tunningPadding(node, Math.max(childX - x, 0));
      const rightDistance = this.tunningPadding(node, Math.max(right - childRight, 0));

      if (leftDistance === rightDistance) {
        node.styles.justifyContent = 'center';
        delete node.styles.paddingLeft;
        delete node.styles.paddingRight;
      } else {
        node.styles.paddingLeft = `${leftDistance}px`;
      }

      return;
    }

    const notAbsoluteChildren = node.children.filter(item => item.layoutType !== LAYOUT_TYPES.Absolute);
    const childMinX = Math.min(...notAbsoluteChildren.map(item => item.boundingRect.x));
    const childMaxX = Math.max(...notAbsoluteChildren.map(item => item.boundingRect.right));
    const thresholdVal = Math.max(width * 0.005, 1); // 暂时取节点宽度 5‰

    const childDistanceList: number[] = [];
    const leftDistance = this.tunningPadding(node, Math.max(childMinX - x, 0));
    const rightDistance = this.tunningPadding(node, Math.max(right - childMaxX, 0));

    // 计算子节点之间的距离
    for (let i = 0; i < node.children.length - 1; i++) {
      const curr = node.children[i];

      if (curr.layoutType === LAYOUT_TYPES.Absolute) {
        continue;
      }

      const next = node.children[i + 1];
      childDistanceList.push(next.boundingRect.x - curr.boundingRect.right);
    }

    const medianDistance = getMedianOfArr(childDistanceList);
    const isSpaceBetween = new Set(childDistanceList.map(distance => Math.abs(distance - medianDistance) < thresholdVal)).size === 1;

    // 设置容器 justify-content 的值：children 之间距离相等取 space-between
    if (isSpaceBetween) {
      node.styles.justifyContent = 'space-between';
    }

    // // 设置容器的左右 padding
    // leftDistance > 0 && (node.styles.paddingLeft = `${leftDistance}px`);
    // rightDistance > 0 && (node.styles.paddingRight = `${rightDistance}px`);

    // 设置子元素之间的 margin 值，space-between 不需要设置子元素 margin；需要考虑绝子元素里绝对定位元素
    if (!isSpaceBetween) {
      notAbsoluteChildren.forEach((childNode, index) => {
        if (index === 0) {
          const matchdNode = node.children.find(item => item.id === childNode.id);
          matchdNode && (matchdNode.styles.marginLeft = `${matchdNode.boundingRect.x - node.boundingRect.x}px`);
        } else {
          const matchdNode = node.children.find(item => item.id === childNode.id);
          matchdNode && (matchdNode.styles.marginLeft = `${childDistanceList[index - 1]}px`);
        }
      });
    } else {
      // 设置容器的左右 padding
      leftDistance > 0 && (node.styles.paddingLeft = `${leftDistance}px`);
      rightDistance > 0 && (node.styles.paddingRight = `${rightDistance}px`);
    }
  }

  processRowCrossAlign(node: NodeDefinition) {
    const { y, bottom, height } = node.boundingRect;
    const nodeMid = y + height / 2;
    const hasOnlyChild = node.children?.length === 1;

    // 根据每个子节点的 y, bottom, y-mid 坐标值，align-items: center | flex-start | flex-end
    const notAbsoluteChildren = node.children.filter(item => item.layoutType !== LAYOUT_TYPES.Absolute);
    const thresholdVal = Math.max(height * 0.005, 1); // 坐标对比阈值 - 暂时取节点宽度 5‰
    const childrenMidList = notAbsoluteChildren.map(childNode => childNode.boundingRect.y + childNode.boundingRect.height / 2);
    const childrenYList = notAbsoluteChildren.map(childNode => childNode.boundingRect.y);
    const childrenBottomList = notAbsoluteChildren.map(childNode => childNode.boundingRect.bottom);
    const childMedianMid = getMedianOfArr(childrenMidList);
    const childMedianY = getMedianOfArr(childrenYList);
    const childMedianBottom = getMedianOfArr(childrenBottomList);
    const isFlexCenter = Math.abs(nodeMid - childMedianMid) <= thresholdVal
      && new Set(childrenMidList.map(value => Math.abs(value - childMedianMid) <= thresholdVal)).size === 1;
    const isFlexStart = hasOnlyChild || new Set(childrenYList.map(value => Math.abs(value - childMedianY) <= thresholdVal)).size === 1;
    const isFlexEnd = hasOnlyChild ? false : new Set(childrenBottomList.map(value => Math.abs(value - childMedianBottom) <= thresholdVal)).size === 1;

    // 计算 padding top | padding bottom
    const childMinY = Math.min(...childrenYList);
    const childMaxBottom = Math.max(...childrenBottomList);
    const topDistance = this.tunningPadding(node, Math.max(childMinY - y, 0));
    const bottomDistance = this.tunningPadding(node, Math.max(bottom - childMaxBottom, 0));

    switch (true) {
      case isFlexCenter:
        node.styles.alignItems = 'center';
        break;
      case isFlexStart:
        node.styles.alignItems = 'flex-start';
        break;
      case isFlexEnd:
        node.styles.alignItems = 'flex-end';
        break;

      default: {
        node.styles.alignItems = 'flex-start';

        // 计算子元素的交叉轴对齐方式：先找出需要单独设置 align-self 的元素，剩余的设置 margin
        node.children.forEach(childNode => {
          if (childNode.layoutType !== LAYOUT_TYPES.Absolute) {
            const { y: childY, bottom: childBottom, height: childHeight } = childNode.boundingRect;

            if (childBottom === childMaxBottom) {
              childNode.styles.alignSelf = 'flex-end';
            } else if (Math.abs(childHeight / 2 + childY - nodeMid) < thresholdVal) {
              // childNode.styles.alignSelf = 'center';
              childNode.styles.alignSelf = 'stretch';
              childNode.styles.marginTop = `${childY - y - topDistance}px`;
              // childNode.styles.marginBottom = `${y + height - (childY + childHeight)}px`;
            } else if (childY !== childMinY) {
              childNode.styles.marginTop = `${childY - childMinY}px`;
            }
          }
        });
        break;
      }
    }

    // 设置 padding，align-items: center 无需设置 padding top | bottom
    if (!isFlexCenter) {
      topDistance && (node.styles.paddingTop = `${topDistance}px`);
      bottomDistance && (node.styles.paddingBottom = `${bottomDistance}px`);

      // 只有一个子元素时，只需要设置一个方向的 padding 值
      if (hasOnlyChild) {
        !isFlexEnd && topDistance && (node.styles.paddingTop = `${topDistance}px`);
        isFlexEnd && bottomDistance && (node.styles.paddingBottom = `${bottomDistance}px`);
      } else {
        topDistance && (node.styles.paddingTop = `${topDistance}px`);
        bottomDistance && (node.styles.paddingBottom = `${bottomDistance}px`);
      }
    } else {
      delete node.styles.paddingTop;
      delete node.styles.paddingBottom;
    }
  }

  tunningPadding(node: NodeDefinition, distance: number) {
    const nodeRaw = this.traverseContext?.nodeRawMap?.[node.id];

    if (nodeRaw) {
      const { strokes, strokeWeight, strokeAlign } = nodeRaw as FrameNode;
      const borderWidth = strokes && strokes?.length > 0 && typeof strokeWeight === 'number' ? strokeWeight : 0;

      return strokeAlign === 'INSIDE' ? Math.max(distance - borderWidth, 0) : distance;
    }

    return distance;
  }

  processColJustifyContent(node: NodeDefinition) {
    const { y, bottom, height } = node.boundingRect;

    if (node.children?.length < 1) {
      return;
    }

    if (node.children?.length <= 1) {
      const { y: childY, bottom: childBottom } = node.children[0].boundingRect;
      const topDistance = this.tunningPadding(node, Math.max(childY - y, 0));
      const bottomDistance = this.tunningPadding(node, Math.max(bottom - childBottom, 0));

      if (topDistance === bottomDistance) {
        node.styles.justifyContent = 'center';
        delete node.styles.paddingTop;
        delete node.styles.paddingBottom;
      } else {
        node.styles.paddingTop = `${topDistance}px`;
      }

      return;
    }

    const notAbsoluteChildren = node.children.filter(item => item.layoutType !== LAYOUT_TYPES.Absolute);
    const childMinY = Math.min(...notAbsoluteChildren.map(item => item.boundingRect.y));
    const childMaxBottom = Math.max(...notAbsoluteChildren.map(item => item.boundingRect.bottom));
    const thresholdVal = Math.max(height * 0.005, 1); // 暂时取节点宽度 5‰

    const childDistanceList: number[] = [];
    const topDistance = this.tunningPadding(node, Math.max(childMinY - y, 0));
    const bottomDistance = this.tunningPadding(node, Math.max(bottom - childMaxBottom, 0));

    // 计算子节点之间的距离
    for (let i = 0; i < node.children.length - 1; i++) {
      const curr = node.children[i];

      if (curr.layoutType === LAYOUT_TYPES.Absolute) {
        continue;
      }

      const next = node.children[i + 1];
      childDistanceList.push(next.boundingRect.y - curr.boundingRect.bottom);
    }

    const medianDistance = getMedianOfArr(childDistanceList);
    const isSpaceBetween = new Set(childDistanceList.map(distance => Math.abs(distance - medianDistance) < thresholdVal)).size === 1;

    // 设置容器 justify-content 的值：children 之间距离相等取 space-between
    if (isSpaceBetween) {
      node.styles.justifyContent = 'space-between';
    }

    // // 设置容器的左右 padding
    // topDistance > 0 && (node.styles.paddingTop = `${topDistance}px`);
    // bottomDistance > 0 && (node.styles.paddingBottom = `${bottomDistance}px`);

    // 设置子元素之间的 margin 值，space-between 不需要设置子元素 margin；需要考虑绝子元素里绝对定位元素
    if (!isSpaceBetween) {
      notAbsoluteChildren.forEach((childNode, index) => {
        if (index === 0) {
          const matchdNode = node.children.find(item => item.id === childNode.id);
          matchdNode && (matchdNode.styles.marginTop = `${matchdNode.boundingRect.y - node.boundingRect.y}px`);
        } else {
          const matchdNode = node.children.find(item => item.id === childNode.id);
          matchdNode && (matchdNode.styles.marginTop = `${childDistanceList[index - 1]}px`);
        }
      });
    } else {
      // 设置容器的上下 padding
      topDistance > 0 && (node.styles.paddingTop = `${topDistance}px`);
      bottomDistance > 0 && (node.styles.paddingBottom = `${bottomDistance}px`);
    }
  }

  processColCrossAlign(node: NodeDefinition) {
    const { x, right, width } = node.boundingRect;
    const nodeMid = x + width / 2;
    const hasOnlyChild = node.children?.length === 1;

    // 根据每个子节点的 x, right, x-mid 坐标值，align-items: center | flex-start | flex-end
    const notAbsoluteChildren = node.children.filter(item => item.layoutType !== LAYOUT_TYPES.Absolute);
    const thresholdVal = Math.max(width * 0.005, 1); // 坐标对比阈值 - 暂时取节点宽度 5‰
    const childrenMidList = notAbsoluteChildren.map(childNode => childNode.boundingRect.x + childNode.boundingRect.width / 2);
    const childrenXList = notAbsoluteChildren.map(childNode => childNode.boundingRect.x);
    const childrenRightList = notAbsoluteChildren.map(childNode => childNode.boundingRect.right);
    const childMedianMid = getMedianOfArr(childrenMidList);
    const childMedianX = getMedianOfArr(childrenXList);
    const childMedianRight = getMedianOfArr(childrenRightList);
    const isFlexCenter = Math.abs(nodeMid - childMedianMid) < thresholdVal
      && new Set(childrenMidList.map(value => Math.abs(value - childMedianMid) < thresholdVal)).size === 1;
    const isFlexStart = hasOnlyChild || new Set(childrenXList.map(value => Math.abs(value - childMedianX) < thresholdVal)).size === 1;
    const isFlexEnd = hasOnlyChild ? false : new Set(childrenRightList.map(value => Math.abs(value - childMedianRight) < thresholdVal)).size === 1;

    // 计算 padding top | padding right
    const childMinX = Math.min(...childrenXList);
    const childMaxRight = Math.max(...childrenRightList);
    const startDistance = this.tunningPadding(node, Math.max(childMinX - x, 0));
    const endDistance = this.tunningPadding(node, Math.max(right - childMaxRight, 0));

    // 设置 padding，align-items: center 无需设置 padding left | right
    if (!isFlexCenter) {
      // 只有一个子元素时，只需要设置一个方向的 padding 值
      if (hasOnlyChild) {
        !isFlexEnd && startDistance && (node.styles.paddingLeft = `${startDistance}px`);
        isFlexEnd && endDistance && (node.styles.paddingRight = `${endDistance}px`);
      } else {
        startDistance && (node.styles.paddingLeft = `${startDistance}px`);
        endDistance && (node.styles.paddingRight = `${endDistance}px`);
      }
    } else {
      delete node.styles.paddingLeft;
      delete node.styles.paddingRight;
    }

    // 设置子元素的布局
    switch (true) {
      case isFlexCenter:
        node.styles.alignItems = 'center';
        break;
      case isFlexStart:
        node.styles.alignItems = 'flex-start';
        break;
      case isFlexEnd:
        node.styles.alignItems = 'flex-end';
        break;
      default: {
        node.styles.alignItems = 'flex-start';

        // 计算子元素的交叉轴对齐方式：先找出需要单独设置 align-self 的元素，剩余的设置 margin
        node.children.forEach(childNode => {
          if (childNode.layoutType !== LAYOUT_TYPES.Absolute) {
            const { x: childX, right: childRight, width: childWidth } = childNode.boundingRect;

            if (childRight === childMaxRight) {
              childNode.styles.alignSelf = 'flex-end';
            } else if (Math.abs(childWidth / 2 + childX - nodeMid) < thresholdVal) {
              childNode.styles.alignSelf = 'stretch';
              childNode.styles.marginLeft = `${childX - x}px`;
            } else if (childX !== childMinX) {
              childNode.styles.marginLeft = `${childX - childMinX}px`;
            }
          }
        });
        break;
      }
    }
  }
}
