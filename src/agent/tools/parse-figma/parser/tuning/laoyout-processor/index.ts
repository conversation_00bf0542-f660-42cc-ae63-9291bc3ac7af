import ReorderProcessor from './reorder-processor';
import RegroupOverlapProcessor from './overlap-processor';
import RegroupLinearProcessor from './linear-processor';
import FlexProcessor from './flex-processor';
import RegroupContainProcessor from './container-processor';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';

export const layoutProcessor = (node: NodeDefinition, traverseContext: TraverseContext) => {
  const processors = [
    new RegroupContainProcessor(traverseContext),
    new RegroupOverlapProcessor(traverseContext),
    new RegroupLinearProcessor(traverseContext),
    new ReorderProcessor(traverseContext),
    new FlexProcessor(traverseContext),
  ];

  processors.forEach(processor => {
    processor.resolve(node);
  });

  return node;
};
