import { hasOverlap } from '../../utils/layout';
import { LAYOUT_TYPES } from '../../const';
import type { NodeDefinition } from '../../types';
import type { TraverseContext } from '../../index';

// 图层顺序调整：按照坐标从上到下，从左到右排序
export default class ReorderProcessor {
  private traverseContext: TraverseContext;

  constructor(traverseContext: TraverseContext) {
    this.traverseContext = traverseContext;
  }

  resolve(node: NodeDefinition) {
    this.processChildNodes(node);
  }

  private processChildNodes(node: NodeDefinition) {
    const isFlexWrapper = node?.styles.display === 'flex';

    if (node.nodeType === 'text' || node.children.length < 1) {
      return;
    }

    if (isFlexWrapper) {
      node.children.forEach(childNode => this.processChildNodes(childNode));
      return;
    }

    this.reorderNodes(node);
  }

  reorderNodes(node: NodeDefinition) {
    const isFlexWrapper = node.styles.display === 'flex';

    // 跳过不需要处理的节点
    if (isFlexWrapper || node.nodeType === 'text' || node.children.length < 1) {
      node.children?.forEach(childNode => this.reorderNodes(childNode));
      return;
    }

    if (node.children.length > 1) {
      // 根据布局类型选择不同的排序策略
      if (node.layoutType === LAYOUT_TYPES.FlexVertical) {
        // 垂直布局按 Y 轴排序
        this.sortByAxis(node.children, 'y');
      } else if (node.layoutType === LAYOUT_TYPES.FlexHorizontal) {
        // 水平布局按 X 轴排序
        this.sortByAxis(node.children, 'x');
      } else {
        // 普通布局综合考虑 zIndex、位置和重叠关系
        this.sortByComplexRules(node.children);
      }
    }

    // 递归处理子节点
    node.children.forEach(childNode => this.reorderNodes(childNode));
  }

  private sortByAxis(nodes: NodeDefinition[], axis: 'x' | 'y') {
    nodes.sort((a, b) => {
      const aValue = a.boundingRect[axis];
      const bValue = b.boundingRect[axis];
      return aValue - bValue;
    });
  }

  private sortByComplexRules(nodes: NodeDefinition[]) {
    nodes.sort((a, b) => {
      // FigmaJSON Children index 规则：
      // AutoLayout - index 顺序和坐标轴顺序一致
      // 非 AutoLayout - index 顺序和图层 Z 轴方向一致

      // 首先比较 zIndex
      const aIndex = a.zIndex as number ?? 0;
      const bIndex = b.zIndex as number ?? 0;
      const indexDiff = aIndex - bIndex;

      if (indexDiff !== 0) return indexDiff;

      // 检查重叠情况
      if (hasOverlap(a, b)) return 0;

      // 比较 Y 轴位置
      const yDiff = a.boundingRect.y - b.boundingRect.y;

      if (yDiff !== 0) return yDiff;

      // Y 轴相同时比较 X 轴
      return a.boundingRect.x - b.boundingRect.x;
    });
  }
}
