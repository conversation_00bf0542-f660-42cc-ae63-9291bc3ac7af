import type { NodeDefinition } from '../types';
import type { TraverseContext } from '../index';

import { layoutProcessor } from './laoyout-processor';
import { hasOverlap } from '../utils/layout';
import { baseTuning } from './base-tuning';

export interface NodeRelationshipTuningResult {
  nodes: NodeDefinition;
}

function processIndex(node: NodeDefinition) {
  if (!node || !node.children) {
    return;
  }

  // 使用广度优先遍历，从底层到顶层处理节点
  node.children.forEach(child => {
    processIndex(child);
  });

  if (node.children?.some(child => child.styles?.position === 'absolute')) {
    let index = 1;
    node.children.forEach(child => {
      if (child.styles?.position !== 'absolute' && !child.styles.hasOwnProperty('zIndex')) {
        child.styles.zIndex = String(index++);
      }

      processIndex(child);
    });
  }

  let index = 1;

  for (let i = 0; i < node.children.length - 1; i++) {
    const child = node.children[i];
    const nextChild = node.children[i + 1];

    if (!nextChild?.styles.hasOwnProperty('zIndex') && child?.children.some(item => {
      const hasOver = hasOverlap(item, nextChild);
      const isUseAbsolute = item.styles?.position === 'absolute';

      return hasOver && isUseAbsolute;
    })) {
      nextChild.styles.zIndex = String(index++);
    }
  }
}

export function tuningNodeLayout(
  node: NodeDefinition,
  traverseContext: TraverseContext,
): NodeRelationshipTuningResult {
  let tuningNode = node;

  baseTuning(node, traverseContext);

  layoutProcessor(node, traverseContext)

  processIndex(tuningNode);

  return {
    nodes: tuningNode,
  };
}
