// 组件类型节点
export const COMPONENT_TYPES: string[] = [
  'INSTANCE',
  'COMPONENT',
  'COMPONENT_SET',
];

export const IMAGE_NODE_TYPES = ['LINE', 'VECTOR', 'BOOLEAN_OPERATION', 'ELLIPSE', 'STAR', 'POLYGON', 'REGULAR_POLYGON'];

export enum LAYOUT_TYPES {
  FlexHorizontal = 'FlexHorizontal',
  FlexVertical = 'FlexVertical',
  Relative = 'Relative',
  Absolute = 'Absolute',
}

export enum LinearTypes {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
  UNKNOWN = 'unknown',
}

export const POSITION_PROPS = [
  // 'position',
  'top',
  'right',
  'bottom',
  'left',
];

// 决定元素布局的属性
export const LAYOUT_PROPS = [
  'boxSizing',
  'overflow',
  'position',
  'width',
  'minWidth',
  'height',
  'minHeight',
  'left',
  'right',
  'top',
  'bottom',
  'margin',
  'marginTop',
  'marginLeft',
  'marginRight',
  'marginBottom',
  'zIndex',
  'flexGrow',
  'flexShrink',
  'flexBasis',
  'alignSelf',
  'transform',
];

export const FRAME_AND_SHAPE_TYPES = [
  'COMPONENT',
  'COMPONENT_SET',
  'CONNECTOR',
  'ELLIPSE',
  'FRAME',
  'HIGHLIGHT',
  'INSTANCE',
  'BOOLEAN_OPERATION',
  'LINE',
  'POLYGON',
  'RECTANGLE',
  'SHAPE_WITH_TEXT',
  'STAMP',
  'STAR',
  'VECTOR',
  'WASHI_TAPE',
];
