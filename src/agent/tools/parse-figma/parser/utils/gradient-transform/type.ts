/** 向量 */
export type MatrixVector = [number, number, 1];

/** 3x3 矩阵：行 */
export type MatrixRow = [number, number, number];
/** 3x3 矩阵 */
export type Matrix3x3 = [MatrixRow, MatrixRow, MatrixRow];

/** 2D 平面的 点 */
export type Point2D = [x: number, y: number];
export type Point2DObject = { x: number; y: number };

/** Web 二维变换矩阵 */
export type Transform2D = [[a: number, c: number, tx: number], [b: number, d: number, ty: number]];

/** RGBA 颜色 */
export type RGBA = {
  readonly r: number;
  readonly g: number;
  readonly b: number;
  readonly a: number;
};

/** 渐变色标 */
export type ColorStop = {
  readonly position: number;
  readonly color: RGBA;
};

/** React Native 格式的 linear gradient 线性渐变 参数 */
export type ReactNativeLinearGradient = {
  colors: string[];
  locations: number[];
  start: Point2DObject;
  end: Point2DObject;
};
