import type { RGBA } from '../type';

/** 取两点之间指定位置的渐变颜色 */
export const getColourBetweenPoints = (
  startColour: RGBA,
  endColour: RGBA,
  startPosition: number,
  targetPosition: number,
  endPosition: number,
): RGBA => {
  const targetNumber = (start: number, end: number, diff: number): number =>
    start + (end - start) * diff;

  const distance = (targetPosition - startPosition) / (endPosition - startPosition);

  return {
    r: targetNumber(startColour.r, endColour.r, distance),
    g: targetNumber(startColour.g, endColour.g, distance),
    b: targetNumber(startColour.b, endColour.b, distance),
    a: targetNumber(startColour.a, endColour.a, distance),
  };
};
