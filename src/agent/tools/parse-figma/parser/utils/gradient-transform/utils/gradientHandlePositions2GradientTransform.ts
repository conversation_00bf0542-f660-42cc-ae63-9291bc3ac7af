import { rotateMatrix, rotateWithOrigin } from '../2DTransform/rotate';
import { scaleMatrix, scaleWithOrigin } from '../2DTransform/scale';
import { translationMatrix } from '../2DTransform/translation';
import matrix3x3MultiplyMatrix3x3 from '../matrix/matrix3x3MultiplyMatrix3x3';
import type { MatrixVector, Point2DObject, Transform2D } from '../type';

const transform = (
  finalScale: number,
  rotated: number,
  dX: number,
  dY: number,
  radian: number,
  scaleX: number,
  scaleY: number,
): Transform2D => {
  // 变换矩阵
  const matrix = matrix3x3MultiplyMatrix3x3(
    translationMatrix(0.5, 0.5),
    scaleMatrix(1 / scaleX, 1 / scaleY),
    rotateMatrix(-radian),
    scaleMatrix(finalScale, 1),
    rotateMatrix(rotated),
    translationMatrix(-dX, -dY),
  );
  return [matrix[0], matrix[1]];
};

/** 取最佳预旋转角度
 *
 * 如果 P₁P₂ 或 P₁P₃ 接近在同一条水平或垂直的线上，直接计算缩放系数会导致误差太大（因为两个相似三角形其中一个面积接近 0，要乘的系数太大）
 *
 * 那么就先转个角度，这样就好算一些了，误差就小了。
 *
 * 先获取 P₁P₂ 和 P₁P₃ 之间的夹角，将这个夹角旋转到垂直，用 Y 轴来平分两个角。
 *
 * 这样应该是最佳的旋转角度？（大概吧，如果遇到更极端的情况，比如 P₁P₂P₃ 三点接近共线 或者 P₁P₂、P₁P₃ 夹角过小，可能还是会出现较大误差。不过这个情况过于极端，遇到再想办法？）
 */
const getBestRotateRadian = (p1: MatrixVector, p2: MatrixVector, p3: MatrixVector): number => {
  const radian12 = Math.atan2(p2[1] - p1[1], p2[0] - p1[0]);
  const radian13 = Math.atan2(p3[1] - p1[1], p3[0] - p1[0]);
  const diff = Math.abs(radian12 - radian13);
  const target =
    diff > Math.PI
      ? Math.max(radian12, radian13) + (2 * Math.PI - diff) / 2
      : Math.min(radian12, radian13) + diff / 2;

  return (target < 0 ? -Math.PI / 2 : Math.PI / 2) - target;
};

/**
 * 将 REST API 的渐变数据转变为 Plugin API 的渐变矩阵
 *
 * REST API gradientHandlePositions 字段说明： https://www.figma.com/developers/api#get-files-endpoint
 *
 * Plugin API gradientTransform 字段说明： https://www.figma.com/plugin-docs/api/Paint/#gradienttransform
 *
 * 线性渐变采用 P₁=(0.5, 0), P₂=(0.5, 1), P₃=(0, 0)
 *
 * 其他渐变采用 P₁=(0.5, 0.5), P₂=(1, 0.5), P₃=(0.5, 1)
 *
 * @param isLinear 是否线性渐变
 * @param p1 第一个点
 * @param p2 第二个点
 * @param p3 第三个点
 */
export default (
  isLinear: boolean,
  p1: Point2DObject,
  p2: Point2DObject,
  p3: Point2DObject,
): Transform2D => {
  let [p1S, p2S, p3S]: MatrixVector[] = [
    [p1.x, p1.y, 1],
    [p2.x, p2.y, 1],
    [p3.x, p3.y, 1],
  ];

  // 中心点
  const [dX, dY] = isLinear ? [(p1S[0] + p2S[0]) / 2, (p1S[1] + p2S[1]) / 2] : [p1S[0], p1S[1]];

  // 先旋转一个角度，便于计算
  const rotated = getBestRotateRadian(p1S, p2S, p3S);
  p1S = rotateWithOrigin(p1S, rotated, dX, dY);
  p2S = rotateWithOrigin(p2S, rotated, dX, dY);
  p3S = rotateWithOrigin(p3S, rotated, dX, dY);

  // 缩放到正方形的缩放系数
  // 因为 ⦜P₂P₁P₃ 是直角，过 P₁ 作平行于 x 轴的辅助线，分别过 P₂、P₃ 作垂直于前面的辅助线的辅助线，得到两个三角形
  // 两个三角形相似，对应边成比例，用坐标 xy 值表示各边长度，化简可得缩放系数 n 的计算公式
  const n =
    Math.sqrt(
      Math.abs(((p2S[1] - p1S[1]) * (p3S[1] - p1S[1])) / ((p1S[0] - p2S[0]) * (p3S[0] - p1S[0]))),
    ) || 1;

  // 先缩放到正方形
  p1S = scaleWithOrigin(p1S, n, 1, dX, dY);
  p2S = scaleWithOrigin(p2S, n, 1, dX, dY);
  p3S = scaleWithOrigin(p3S, n, 1, dX, dY);

  // 旋转弧度
  const radian = Math.atan2(p2S[1] - p1S[1], p2S[0] - p1S[0]);
  // 横轴缩放（p1S, p2S 距离，在非 linear 渐变下需要 ✖️2）
  const scaleX = Math.sqrt((p2S[0] - p1S[0]) ** 2 + (p2S[1] - p1S[1]) ** 2) * (isLinear ? 1 : 2);
  // 纵轴缩放（p1S, p3S 距离 * 2）
  const scaleY = Math.sqrt((p3S[0] - p1S[0]) ** 2 + (p3S[1] - p1S[1]) ** 2) * 2;

  return transform(n, rotated, dX, dY, radian, scaleX, scaleY);
};
