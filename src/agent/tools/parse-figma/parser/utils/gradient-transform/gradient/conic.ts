import colourRGBA2Hex from '../format/colourRGBA2Hex';
import { roundFloat2 } from '../format/roundFloat';
import toPercent from '../format/toPercent';
import { radian2angle } from '../math/angle';
import isClosedTo from '../math/isClosedTo';
import inverse3x3 from '../matrix/inverse3x3';
import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type { ColorStop, Matrix3x3, Point2D, Transform2D } from '../type';
import { getColourBetweenPoints } from '../utils/colour';

/** 锥形渐变信息 */
type ConicGradientInfo = {
  /** Web 下锥形渐变的角度 */
  angle: number;
  /** 中心点 */
  centre: Point2D;
  /** 转换过角度的颜色坐标信息 */
  transformColours: ColorStop[];
};

/** conic gradient 锥形渐变 */
export const conicGradientTransform = (
  transform: Transform2D,
  colours: readonly ColorStop[],
): ConicGradientInfo => {
  // 补齐矩阵
  const matrix = transform.concat([[0, 0, 1]]) as Matrix3x3;
  // 求逆矩阵
  const inverseMatrix = inverse3x3(matrix);

  // 中心点
  const centrePoint = matrix3x3MultiplyVector3x1(
    inverseMatrix,
    [0.5, 0.5, 1],
  ) as number[] as Point2D;
  const [cx, cy] = centrePoint;

  // 判断角度
  const rxPointRotated = matrix3x3MultiplyVector3x1(inverseMatrix, [1, 0.5, 1]);
  const radian = Math.atan2(rxPointRotated[1] - cy, rxPointRotated[0] - cx);

  // 针对于渐变环上的每一个坐标，计算偏移，以支持非正方形不规则的渐变参数
  const transformColours = colours.map(colour => {
    const position = colour.position;

    // 圆心在 (0.5, 0.5) 的圆
    const X = (Math.cos(position * 2 * Math.PI) + 1) / 2;
    const Y = (Math.sin(position * 2 * Math.PI) + 1) / 2;

    // 判断角度
    const pointRotated = matrix3x3MultiplyVector3x1(inverseMatrix, [X, Y, 1]);
    const r = Math.atan2(pointRotated[1] - cy, pointRotated[0] - cx) - radian;
    const a = radian2angle(r) + (r < 0 ? 360 : 0);

    return { ...colour, position: a / 360 };
  });

  return { angle: radian2angle(radian) + 90, centre: [cx, cy], transformColours };
};

/** conic gradient 锥形渐变 输出 CSS 格式样式 */
export const conicGradientTransform2CSS = (
  transform: Transform2D,
  colours: readonly ColorStop[],
): string => {


  if (colours.length === 1) {
    return colourRGBA2Hex(colours[0].color);
  }

  // 色标排序
  const fixedColours = colours
    .map(color => ({
      ...color,
      // 色标位置规范到 0～1 之间
      position: color.position > 0 ? color.position % 1 : 1 - (-color.position % 1),
    }))
    .sort(({ position: pA }, { position: pB }) => pA - pB);

  // 如果没有给定 0%、100% 的色标，则 Web 下渐变会有异常
  // 这里通过第一个点和最后一个点之间，到 0%、100% 的渐变色，使渐变色连起来
  const first = fixedColours[0];
  const last = fixedColours[fixedColours.length - 1];
  if (first && last) {
    if (!isClosedTo(first.position, 0)) {
      fixedColours.unshift({
        color: getColourBetweenPoints(
          last.color,
          first.color,
          last.position,
          1,
          1 + first.position,
        ),
        position: 0,
      });
    }
    if (!isClosedTo(last.position, 1)) {
      fixedColours.push({
        color: getColourBetweenPoints(
          last.color,
          first.color,
          last.position,
          1,
          1 + first.position,
        ),
        position: 1,
      });
    }
  }

  const {
    angle,
    centre: [cx, cy],
    transformColours,
  } = conicGradientTransform(transform, fixedColours);

  return `conic-gradient(from ${roundFloat2(angle)}deg at ${toPercent(cx)} ${toPercent(
    cy,
  )}, ${transformColours
    .map(colour => `${colourRGBA2Hex(colour.color)} ${roundFloat2(colour.position * 360)}deg`)
    .join(', ')})`;
};
