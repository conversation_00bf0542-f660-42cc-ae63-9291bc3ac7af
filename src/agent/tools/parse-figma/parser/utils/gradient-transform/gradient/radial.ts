import colourRGBA2Hex from '../format/colourRGBA2Hex';
import toPercent from '../format/toPercent';
import PointsDistance2D from '../math/PointsDistance2D';
import inverse3x3 from '../matrix/inverse3x3';
import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type { ColorStop, Matrix3x3, Point2D, Transform2D } from '../type';

/** 径向渐变信息 */
type RadialGradientInfo = {
  /** 横轴、纵轴半径 */
  radius: Point2D;
  /** 中心点 */
  centre: Point2D;
  /** 旋转弧度（Web 不支持） */
  radian: number;
};

/** radial gradient 径向渐变 */
export const radialGradientTransform = (transform: Transform2D): RadialGradientInfo => {
  // 补齐矩阵
  const matrix = transform.concat([[0, 0, 1]]) as Matrix3x3;
  // 求逆矩阵
  const inverseMatrix = inverse3x3(matrix);

  // 中心点
  const centrePoint = matrix3x3MultiplyVector3x1(
    inverseMatrix,
    [0.5, 0.5, 1],
  ) as number[] as Point2D;
  const [cx, cy] = centrePoint;

  // 右边 X、下边 Y 坐标
  const rxPoint = matrix3x3MultiplyVector3x1(inverseMatrix, [1, 0.5, 1]) as number[] as Point2D;
  const ryPoint = matrix3x3MultiplyVector3x1(inverseMatrix, [0.5, 1, 1]) as number[] as Point2D;

  // 半长轴、半短轴（X 轴、Y 轴）长度
  const rx = PointsDistance2D(centrePoint, rxPoint);
  const ry = PointsDistance2D(centrePoint, ryPoint);

  // 判断角度，角度在 Web 下不支持
  const radian = Math.atan2(rxPoint[1] - cy, rxPoint[0] - cx);
  // 还有横轴缩放，但 Web 也不支持，就不写了

  return { radius: [rx, ry], centre: [cx, cy], radian };
};

/** radial gradient 径向渐变 输出 CSS 格式样式 */
export const radialGradientTransform2CSS = (
  transform: Transform2D,
  colours: readonly ColorStop[],
): string => {

  if (colours.length === 1) {
    return colourRGBA2Hex(colours[0].color);
  }

  const {
    radius: [rx, ry],
    centre: [cx, cy],
  } = radialGradientTransform(transform);

  return `radial-gradient(${toPercent(rx)} ${toPercent(ry)} at ${toPercent(cx)} ${toPercent(
    cy,
  )}, ${colours
    .map(colour => `${colourRGBA2Hex(colour.color)} ${toPercent(colour.position)}`)
    .join(', ')})`;
};
