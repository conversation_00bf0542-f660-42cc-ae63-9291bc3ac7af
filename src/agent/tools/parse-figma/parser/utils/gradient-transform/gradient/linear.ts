import translation from '../2DTransform/translation';
import colourRGBA2Hex from '../format/colourRGBA2Hex';
import { roundFloat2 } from '../format/roundFloat';
import toPercent from '../format/toPercent';
import { radian2angle } from '../math/angle';
import isClosedTo from '../math/isClosedTo';
import inverse3x3 from '../matrix/inverse3x3';
import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type {
  ColorStop,
  Matrix3x3,
  MatrixVector,
  ReactNativeLinearGradient,
  Transform2D,
} from '../type';
import { getColourBetweenPoints } from '../utils/colour';
import { ANGLE_MODE } from '../utils/const';

/** 数值范围 */
type Range = [start: number, end: number];
/** 线性渐变信息 */
type LinearGradientInfo = {
  /** Web 下线性渐变的角度 */
  angle: number;
  /** 渐变起始点、结束点百分比位置 */
  positionRange: Range;
  /** 起点坐标 */
  startPoint: MatrixVector;
  /** 结束点坐标 */
  endPoint: MatrixVector;
};

const getRealEndPoint = (
  radian: number,
  startPoint: MatrixVector,
  endPoint: MatrixVector,
): MatrixVector => {
  // 目标线
  // 斜率
  const K1 = Math.tan(radian);
  // 直线参数 y = K₁x + C₁, C₁ = y - K₁x
  const C1 = startPoint[1] - K1 * startPoint[0];

  // 过 P₂ 原角度线
  // 斜率
  const K2 = Math.tan(radian + Math.PI / 2);
  // 直线参数 y = K₂x + C₂, C₂ = y - K₂x
  const C2 = endPoint[1] - K2 * endPoint[0];

  // 两直线交点
  // K₁x + C₁ = K₂x + C₂
  // (K₁ - K₂)x = C₂ - C₁, x = (C₂ - C₁) / (K₁ - K₂)
  const x = (C2 - C1) / (K1 - K2);
  const y = K1 * x + C1;

  return [x, y, 1];
};

/** linear gradient 线性渐变 */
export const linearGradientTransform = (transform: Transform2D): LinearGradientInfo => {
  // 补齐矩阵
  const matrix = transform.concat([[0, 0, 1]]) as Matrix3x3;
  // 求逆矩阵
  const inverseMatrix = inverse3x3(matrix);

  // 起点坐标
  let startPoint = matrix3x3MultiplyVector3x1(inverseMatrix, [0, 0.5, 1]);
  // 角度参考点坐标
  const armPoint = matrix3x3MultiplyVector3x1(inverseMatrix, [0, 1, 1]);

  // 判断弧度
  let radian = Math.atan2(armPoint[1] - startPoint[1], armPoint[0] - startPoint[0]) - Math.PI / 2;
  if (radian < -Math.PI) {
    radian += 2 * Math.PI;
  }
  // 正弧度
  const positiveRadian = radian + (radian < 0 ? Math.PI : 0);

  // 终点坐标 🏁
  let endPoint = getRealEndPoint(
    radian,
    startPoint,
    matrix3x3MultiplyVector3x1(inverseMatrix, [1, 0.5, 1]),
  );

  // 起点、终点位置
  const positionRange: Range = [0, 1];

  // 如果是 0°、±90°、±180°，可以直接通过起点、终点得到坐标位置
  if (isClosedTo(radian, 0)) {
    // 0°
    [positionRange[0], positionRange[1]] = [startPoint[0], endPoint[0]];
  } else if (isClosedTo(Math.abs(radian), Math.PI)) {
    // ±180°
    [positionRange[0], positionRange[1]] = [1 - startPoint[0], 1 - endPoint[0]];
  } else if (isClosedTo(radian, Math.PI / 2)) {
    // 90°
    [positionRange[0], positionRange[1]] = [startPoint[1], endPoint[1] / 2];
  } else if (isClosedTo(radian, -Math.PI / 2)) {
    // -90°
    [positionRange[0], positionRange[1]] = [1 - startPoint[1], 1 - endPoint[1]];
  } else {
    // 否则需要根据 W3C 规范文档 https://www.w3.org/TR/css-images-3/#linear-gradients 来计算起点、终点的坐标位置

    // 斜率
    const K = Math.tan(radian);
    // 直线参数 Ax + By + C₁ = 0
    // y = Kx + C₁, C₁ = y - Kx
    // Kx -y + C₁ = 0
    // A=K, B=-1, C₁=y-Kx
    const [A, B, C1] = [K, -1, startPoint[1] - K * startPoint[0]];
    // 过中心点 (0.5, 0.5) 直线参数 Ax + By + C₂ = 0
    // y = Kx + C₂, C₂ = y - Kx = 0.5 - K*0.5
    const C2 = 0.5 * (1 - K); // 平行线，A、B 相等
    // 平行线间距离
    const D = Math.abs(C1 - C2) / Math.sqrt(A ** 2 + B ** 2);
    // 平移
    const isPositive = positiveRadian < Math.PI / 2;
    let dX, dY;
    if (isPositive) {
      [dX, dY] = [Math.abs(D * Math.sin(positiveRadian)), Math.abs(D * Math.cos(positiveRadian))];
      if (C1 > C2) {
        dY *= -1;
      } else {
        dX *= -1;
      }
    } else {
      [dX, dY] = [
        Math.abs(D * Math.cos(positiveRadian - Math.PI / 2)),
        Math.abs(D * Math.sin(positiveRadian - Math.PI / 2)),
      ];
      if (C1 > C2) {
        dX *= -1;
        dY *= -1;
      }
    }
    // 实际起点、终点坐标
    startPoint = translation(startPoint, dX, dY);
    endPoint = translation(endPoint, dX, dY);

    // 渐变轴长度
    const L =
      Math.SQRT2 *
      Math.cos(isPositive ? positiveRadian - 0.25 * Math.PI : 0.75 * Math.PI - positiveRadian);
    // 渐变轴 delta X
    const LdX = L * Math.cos(radian);

    // 起点、终点位置
    positionRange[0] = (startPoint[0] - (0.5 - LdX / 2)) / LdX;
    positionRange[1] = (endPoint[0] - (0.5 - LdX / 2)) / LdX;
  }

  return { angle: radian2angle(radian) + 90, positionRange, startPoint, endPoint };
};

/**
 * linear gradient 线性渐变 输出 CSS 格式样式
 * 线性渐变角度体系说明（可视化）：
 *
 * Web H5 (CSS) 体系：
 * 
 *         0deg
 *          ↑
 *          |
 * 270deg ← + → 90deg
 *          |
 *        180deg
 * 
 * - 0度朝上，顺时针为正方向
 * - 90度朝右，180度朝下，270度朝左
 */
export const linearGradientTransform2CSS = (
  transform: Transform2D,
  colours: readonly ColorStop[],
): string => {
  if (colours.length === 1) {
    return colourRGBA2Hex(colours[0].color);
  }

  const { angle, positionRange } = linearGradientTransform(transform);

  return `linear-gradient(${roundFloat2(angle)}deg, ${colours
    .map(
      colour =>
        `${colourRGBA2Hex(colour.color)} ${toPercent(
          positionRange[0] + colour.position * (positionRange[1] - positionRange[0]),
        )}`,
    )
    .join(', ')})`;
};

/** linear gradient 线性渐变 输出 React Native 格式样式
 *
 * RN 采用 start-end 的形式
 *
 * angle 形式得到的结果与 Web 有差异，就不提供了
 */
export const linearGradientTransform2ReactNative = (
  transform: Transform2D,
  colours: readonly ColorStop[],
): ReactNativeLinearGradient => {
  const { startPoint, endPoint } = linearGradientTransform(transform);

  // 确保色标位置是升序
  const colourAscend = colours.filter(
    (colour, index, arr) => index === 0 || colour.position >= arr[index - 1].position,
  );

  // 色标位置不能小于 0，不能大于 1，否则 iOS 渐变失败会显示为纯黑

  // 去除开头的负数色标，计算 0 点的渐变色，从 0 点开始
  const firstPositive = colourAscend.findIndex(colour => colour.position >= 0);
  if (firstPositive > 0) {
    const lastNegative = firstPositive - 1;
    colourAscend.splice(0, firstPositive, {
      color: getColourBetweenPoints(
        colourAscend[lastNegative].color,
        colourAscend[firstPositive].color,
        colourAscend[lastNegative].position,
        0,
        colourAscend[firstPositive].position,
      ),
      position: 0,
    });
  }
  // 去除尾部超过 100% 的色标，计算 100% 点的渐变色，到 100% 为止
  const firstOverflow = colourAscend.findIndex(colour => colour.position > 1);
  if (firstOverflow >= 0) {
    const lastUnderflow = firstOverflow - 1;
    colourAscend.splice(firstOverflow, colourAscend.length - firstOverflow, {
      color: getColourBetweenPoints(
        colourAscend[lastUnderflow].color,
        colourAscend[firstOverflow].color,
        colourAscend[lastUnderflow].position,
        1,
        colourAscend[firstOverflow].position,
      ),
      position: 1,
    });
  }

  return {
    colors: colourAscend.map(({ color }) => colourRGBA2Hex(color)),
    locations: colourAscend.map(({ position }) => roundFloat2(position)),
    start: { x: roundFloat2(startPoint[0]), y: roundFloat2(startPoint[1]) },
    end: { x: roundFloat2(endPoint[0]), y: roundFloat2(endPoint[1]) },
  };
};
