import { Paint } from '@figma/rest-api-spec';

import { conicGradientTransform2CSS } from './conic';
import { linearGradientTransform2CSS } from './linear';
import { radialGradientTransform2CSS } from './radial';
import gradientHandlePositions2GradientTransform from '../utils/gradientHandlePositions2GradientTransform';

export function transformGradient(paint: Paint) {
  if (paint.type !== 'GRADIENT_LINEAR' && paint.type !== 'GRADIENT_RADIAL' && paint.type !== 'GRADIENT_ANGULAR') {
    return '';
  }

  const gradientTransform = gradientHandlePositions2GradientTransform(
    paint.type === 'GRADIENT_LINEAR',
    paint.gradientHandlePositions[0],
    paint.gradientHandlePositions[1],
    paint.gradientHandlePositions[2],
  );

  if (!gradientTransform) {
    return '';
  }
  
  if (paint.type === 'GRADIENT_LINEAR') {
    return linearGradientTransform2CSS(gradientTransform, paint.gradientStops);
  }

  if (paint.type === 'GRADIENT_RADIAL') {
    return radialGradientTransform2CSS(gradientTransform, paint.gradientStops);
  }

  if (paint.type === 'GRADIENT_ANGULAR') {
    return conicGradientTransform2CSS(gradientTransform, paint.gradientStops);
  }
}