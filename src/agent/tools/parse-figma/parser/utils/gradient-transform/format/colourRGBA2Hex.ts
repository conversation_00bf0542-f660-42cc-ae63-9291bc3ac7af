import type { RGBA } from '../type';

/** RGBA 颜色转换为 Hex 颜色字符串 */
export default (colour: RGBA): string => {
  const numberToHex = (num: number): string =>
    Math.round(num * 255)
      .toString(16)
      .padStart(2, '0');
  const isRepeated = (hex: string): boolean => hex[1] === hex[0];
  const toSingle = (hex: string): string => hex[0];
  const RGBA = [
    numberToHex(colour.r),
    numberToHex(colour.g),
    numberToHex(colour.b),
    numberToHex(colour.a),
  ];
  if (RGBA[3] === 'ff') {
    RGBA.splice(3, 1);
  }
  return `#${(RGBA.every(isRepeated) ? RGBA.map(toSingle) : RGBA).join('')}`;
};
