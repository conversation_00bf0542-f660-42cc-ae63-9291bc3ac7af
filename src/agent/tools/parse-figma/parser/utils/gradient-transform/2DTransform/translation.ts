import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type { Matrix3x3, MatrixVector } from '../type';

/** 平移矩阵 */
export const translationMatrix = (dX: number, dY: number): Matrix3x3 => [
  [1, 0, dX],
  [0, 1, dY],
  [0, 0, 1],
];

/** 向量平移 */
export default (vector: MatrixVector, dX: number, dY: number): MatrixVector =>
  matrix3x3MultiplyVector3x1(translationMatrix(dX, dY), vector);
