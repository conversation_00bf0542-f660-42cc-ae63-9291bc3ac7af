import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type { Matrix3x3, MatrixVector } from '../type';
import translation from './translation';

/** 缩放矩阵 */
export const scaleMatrix = (scaleX: number, scaleY: number): Matrix3x3 => [
  [scaleX, 0, 0],
  [0, scaleY, 0],
  [0, 0, 1],
];

/** 向量缩放 */
const scale = (vector: MatrixVector, scaleX: number, scaleY: number): MatrixVector =>
  matrix3x3MultiplyVector3x1(scaleMatrix(scaleX, scaleY), vector);

/** 向量绕指定中心点缩放 */
export const scaleWithOrigin = (
  vector: MatrixVector,
  scaleX: number,
  scaleY: number,
  dX: number,
  dY: number,
): MatrixVector => translation(scale(translation(vector, -dX, -dY), scaleX, scaleY), dX, dY);
