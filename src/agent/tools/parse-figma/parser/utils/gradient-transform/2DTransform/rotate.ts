import matrix3x3MultiplyVector3x1 from '../matrix/matrix3x3MultiplyVector3x1';
import type { Matrix3x3, MatrixVector } from '../type';
import translation from './translation';

/** 旋转矩阵 */
export const rotateMatrix = (radian: number): Matrix3x3 => [
  [Math.cos(radian), -Math.sin(radian), 0],
  [Math.sin(radian), Math.cos(radian), 0],
  [0, 0, 1],
];

/** 向量旋转 */
const rotate = (vector: MatrixVector, radian: number): MatrixVector =>
  matrix3x3MultiplyVector3x1(rotateMatrix(radian), vector);

export default rotate;

/** 向量绕指定中心点旋转 */
export const rotateWithOrigin = (
  vector: MatrixVector,
  radian: number,
  dX: number,
  dY: number,
): MatrixVector => translation(rotate(translation(vector, -dX, -dY), radian), dX, dY);
