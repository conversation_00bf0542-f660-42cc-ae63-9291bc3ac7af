import type { Matrix3x3 } from '../type';

/** 3x3 矩阵 ❌ 3x3 矩阵
 *
 * 注：叉乘
 */
export default (matrixLeft: Matrix3x3, ...matrixRight: Matrix3x3[]): Matrix3x3 => {
  let left = matrixLeft;
  for (const right of matrixRight) {
    const result: Matrix3x3 = [
      [0, 0, 0],
      [0, 0, 0],
      [0, 0, 0],
    ];
    for (let r = 0; r < 3; ++r) {
      for (let c = 0; c < 3; ++c) {
        for (let index = 0; index < 3; ++index) {
          result[r][c] += left[r][index] * right[index][c];
        }
      }
    }
    left = result;
  }
  return left;
};
