import type { Matrix3x3, MatrixVector } from '../type';

/** 3x3 矩阵 ❌ 3x1 向量
 *
 * 注：叉乘
 */
export default (matrix: Matrix3x3, vector: MatrixVector): MatrixVector => {
  const ret: MatrixVector = [0, 0, 0 as 1]; // 因为 2D 变换的 3x3 矩阵最后一行固定为 [0, 0, 1]，2D 向量第三位固定为 1，所以第三位计算结果固定为 1
  for (let r = 0; r < 3; ++r) {
    for (let c = 0; c < 3; ++c) {
      ret[r] += matrix[r][c] * vector[c];
    }
  }
  return ret;
};
