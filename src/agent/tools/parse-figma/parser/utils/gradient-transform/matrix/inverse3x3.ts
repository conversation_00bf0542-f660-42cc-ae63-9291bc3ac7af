import type { Matrix3x3 } from '../type';

/** 矩阵求逆
 *
 * https://www.delftstack.com/howto/cpp/cpp-inverse-matrix/
 */
export default (matrix: Matrix3x3): Matrix3x3 => {
  // 求行列式
  let determinant = 0;
  for (let r = 0; r < 3; ++r) {
    determinant +=
      matrix[0][r] *
      (matrix[1][(r + 1) % 3] * matrix[2][(r + 2) % 3] -
        matrix[1][(r + 2) % 3] * matrix[2][(r + 1) % 3]);
  }
  if (determinant === 0) {
    throw new Error('奇异矩阵无法求逆');
  }
  // 求逆矩阵
  const ret: number[][] = [];
  for (let r = 0; r < 3; ++r) {
    ret[r] = [];
    for (let c = 0; c < 3; ++c) {
      ret[r][c] =
        (matrix[(c + 1) % 3][(r + 1) % 3] * matrix[(c + 2) % 3][(r + 2) % 3] -
          matrix[(c + 1) % 3][(r + 2) % 3] * matrix[(c + 2) % 3][(r + 1) % 3]) /
        determinant;
    }
  }
  return ret as Matrix3x3;
};
