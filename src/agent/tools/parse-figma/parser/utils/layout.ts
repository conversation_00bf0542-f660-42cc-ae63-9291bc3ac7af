import { FrameNode, RectangleNode } from '@figma/rest-api-spec';
import { omit } from 'lodash-es';
import { POSITION_PROPS, FRAME_AND_SHAPE_TYPES } from '../const';
import { notEmptyNumber, keepDecimal } from './common';
import type { NodeDefinition, FigmaNode } from '../types';

/**
 * 处理布局相关的工具函数
 */

export const computeNodeArea = (node: NodeDefinition) => {
  return node.boundingRect.width * node.boundingRect.height;
};

/**
 * FUNC_DES: 判断两个矩形节点是否是包含关系
 */
export const isContain = (node1: NodeDefinition, node2: NodeDefinition): boolean => {
  const { x: node1X, y: node1Y, right: node1Right, bottom: node1Bottom } = node1.boundingRect;
  const { x: node2X, y: node2Y, right: node2Right, bottom: node2Bottom } = node2.boundingRect;

  return node1X <= node2X && node1Y <= node2Y && node1Right >= node2Right && node1Bottom >= node2Bottom;
};

// TODO: 考虑旋转的情况
/**
 * FUNC_DES:  判断两个矩形节点是重叠部分，判断方法：将二维投射到一维，根据线段是否相交进行判断（基于浏览器坐标系）
 */
export const hasOverlap = (node1: NodeDefinition, node2: NodeDefinition): boolean => {
  const {
    x: node1X,
    y: node1Y,
    right: node1Right,
    bottom: node1Bottom,
    width: node1Width,
    height: node1Height,
  } = node1.boundingRect;
  const {
    x: node2X,
    y: node2Y,
    right: node2Right,
    bottom: node2Bottom,
    width: node2Width,
    height: node2Height,
  } = node2.boundingRect;

  // TODO: 待优化 - 处理相交是根据图层 bound 处理的，需要配合视觉稿内容信息进行辅助判断是否真实相交；

  // 计算阈值，通过阈值解决「仅部分重叠」的边界场景
  const verticalThreshold = Math.max(Math.max(node1Width, node2Width) * 0.005, 1);
  const horizontalThreshold = Math.max(Math.max(node1Height, node2Height) * 0.005, 1);

  return (
    isLineIntersection([node1X, node1Right], [node2X, node2Right], verticalThreshold) &&
    isLineIntersection([node1Y, node1Bottom], [node2Y, node2Bottom], horizontalThreshold)
  );
};


/**
 * FUNC_DES: 判断两条线是否有重叠部分（基于浏览器坐标系）
 */
export const isLineIntersection = (line1: [number, number], line2: [number, number], thresholdVal: number = 0): boolean => {
  const [start1, end1] = line1;
  const [start2, end2] = line2;

  return Math.min(end1, end2) > Math.max(start1, start2) + thresholdVal;
};

/**
 * FUNC_DES: 判断两个矩形是否在 X 轴方向有重叠
 */
export const isRectangleIntersectionX = (node1: NodeDefinition, node2: NodeDefinition): boolean => {
  const { x: x1, right: right1, width: node1Width, } = node1.boundingRect;
  const { x: x2, right: right2, width: node2Width, } = node2.boundingRect;

  // 计算阈值，通过阈值解决「仅部分重叠」的边界场景
  const verticalThreshold = Math.max(Math.max(node1Width, node2Width) * 0.005, 1);

  return isLineIntersection([x1, right1], [x2, right2], verticalThreshold);
};

/**
 * FUNC_DES: 判断两个矩形是否在 Y 轴方向有重叠
 */
export const isRectangleIntersectionY = (node1: NodeDefinition, node2: NodeDefinition): boolean => {
  const { y: y1, bottom: bottom1, height: node1Height, } = node1.boundingRect;
  const { y: y2, bottom: bottom2, height: node2Height } = node2.boundingRect;

  const horizontalThreshold = Math.max(Math.max(node1Height, node2Height) * 0.005, 1);

  return isLineIntersection([y1, bottom1], [y2, bottom2], horizontalThreshold);
};

/**
 * FUNC_DES: 判断嵌套的矩形是否水平居中
 */
export const isHorizontalCenter = (parentNode: NodeDefinition, node: NodeDefinition): boolean => {
  const { x: parentX, width: parentWidth } = parentNode.boundingRect;
  const { x, width } = node.boundingRect;

  // 父子节点相同尺寸，不算居中
  if (width === parentWidth) {
    return false;
  }

  return parentX + parentWidth / 2 === x + width / 2;
};

/**
 * FUNC_DES: 判断嵌套的矩形是否垂直居中
 */
export const isVerticalCenter = (parentNode: NodeDefinition, node: NodeDefinition): boolean => {
  const { y: parentY, height: parentHeight } = parentNode.boundingRect;
  const { y, height } = node.boundingRect;

  // 父子节点相同尺寸，不算居中
  if (height === parentHeight) {
    return false;
  }

  return parentY + parentHeight / 2 === y + height / 2;
};

/**
 * 判断是否是尺寸、坐标、旋转一致的 View 图层
 */
export const isSameViewNode = (node1: NodeDefinition, node2: NodeDefinition): boolean => {
  const nodeTypes = ['view', 'image'];

  if (!nodeTypes.includes(node1.nodeType) || !nodeTypes.includes(node2.nodeType)) {
    return false;
  }

  const { width: w1, height: h1, x: x1, y: y1 } = node1.boundingRect;
  const { width: w2, height: h2, x: x2, y: y2 } = node2.boundingRect;
  const thresholdVal = Math.max(w1 * 0.005, 1); // 坐标对比阈值 - 暂时取节点宽度 5‰
  const isSameSizeAndCoord = Math.abs(w1 - w2) < thresholdVal
    && Math.abs(h1 - h2) < thresholdVal
    && Math.abs(x1 - x2) < thresholdVal
    && Math.abs(y1 - y2) < thresholdVal;
  const isSameRotation = node1.styles?.transform === node2.styles?.transform;

  return isSameRotation && isSameSizeAndCoord;
};

// TODO: 后续优化，支持其它单位
export const isPxValue = (value: string): boolean => typeof value === 'string' && /^([0]{1})$|^(\d+px)$/.test(value);

/**
 * 根据坐标重新计算节点相对父节点的绝对定位属性
 * @param node 当前节点
 * @param parentNode 父节点
 */
export const reComputeAbsolute = (node: NodeDefinition, parentNode: NodeDefinition) => {
  const { x: pX, y: pY, right: pRight, bottom: pBottom } = parentNode.boundingRect;
  const { x, y, right, bottom } = node.boundingRect;

  const topDistance = y - pY;
  const rightDistance = pRight - right;
  const bottomDistance = pBottom - bottom;
  const leftDistance = x - pX;

  node.styles = omit(node.styles, ['top', 'right', 'bottom', 'left']);

  node.styles[topDistance <= bottomDistance ? 'top' : 'bottom'] = `${Math.min(topDistance, bottomDistance)}px`;
  node.styles[leftDistance <= rightDistance ? 'left' : 'right'] = `${Math.min(leftDistance, rightDistance)}px`;
};

/**
 * 去除绝对定位相关属性
 */
export const removeAbsoluteProperties = (node: NodeDefinition) => {
  // 去除绝对定位相关属性
  node.styles = omit(node.styles, POSITION_PROPS);

  if (node.styles.position === 'absolute') {
    delete node.styles.position;

    if (node.styles.transform?.startsWith('translate')) {
      delete node.styles.transform;
    }
  }
}

/**
 * 不需要调优的图层：可识别的组件、文本、不包含子元素的节点
 */
export const noNeedTuningNode = (node: NodeDefinition) => {
  return ['component', 'text'].includes(node.nodeType) || (node?.children?.length ?? 0) < 1;
}

export const hasBorderStyle = (node: FrameNode): boolean => {
  const { strokeWeight, individualStrokeWeights } = node;
  const { top: strokeTopWeight, right: strokeRightWeight, bottom: strokeBottomWeight, left: strokeLeftWeight } = individualStrokeWeights ?? {};

  const borderWidth = notEmptyNumber(strokeWeight)
    || notEmptyNumber(strokeTopWeight)
    || notEmptyNumber(strokeRightWeight)
    || notEmptyNumber(strokeBottomWeight)
    || notEmptyNumber(strokeLeftWeight);

  if (!borderWidth) {
    return false;
  }

  return node.strokes?.some(paint => {
    return paint.visible !== false
      && ['SOLID', 'GRADIENT_LINEAR', 'GRADIENT_RADIAL', 'GRADIENT_ANGULAR'].includes(paint.type)
      && notEmptyNumber(paint.opacity ?? 1)
  }) ?? false;
}

export const hasBackgroundStyle = (node: FrameNode): boolean => {
  return Boolean(node.fills?.find(fill =>
    ['SOLID', 'GRADIENT_LINEAR', 'GRADIENT_RADIAL', 'GRADIENT_ANGULAR', 'GRADIENT_DIAMOND', 'IMAGE'].includes(fill.type)
    && fill.visible !== false
  ));
}

export const notAffectAppearanceFrameAndShape = (node: FigmaNode) => {
  if (node.type === 'BOOLEAN_OPERATION'
    || node.type === 'COMPONENT'
    || node.type === 'COMPONENT_SET'
    || node.type === 'CONNECTOR'
    || node.type === 'ELLIPSE'
    || node.type === 'FRAME'
    || node.type === 'GROUP'
    || node.type === 'INSTANCE'
    || node.type === 'LINE'
    || node.type === 'REGULAR_POLYGON'
    || node.type === 'RECTANGLE'
    || node.type === 'SHAPE_WITH_TEXT'
    || node.type === 'STAR'
    || node.type === 'VECTOR'
    || node.type === 'WASHI_TAPE'
  ) {
    // 是否有边框
    const hasBorder = hasBorderStyle(node as FrameNode);

    // 是否有边框
    const hasBackground = hasBackgroundStyle(node as FrameNode);

    // 暂不考虑阴影
    return !hasBorder && !hasBackground && !(node as FrameNode)?.children?.length;
  }

  return false;
}

/**
 * 是否无需渲染的图层
 */
export const isNotAffectAppearance = (node: FrameNode) => {
  return node.visible === false
    || !notEmptyNumber(node.opacity ?? 1)
    || !notEmptyNumber(node.absoluteBoundingBox?.width)
    || !notEmptyNumber(node.absoluteBoundingBox?.height)
    || notAffectAppearanceFrameAndShape(node);
}

const calculateOriginalCorners = (
  width: number,
  height: number,
  newWidth: number,
  newHeight: number,
  newX: number,
  newY: number,
) => {
  const rotaeCorner = {
    x: newX + newWidth / 2,
    y: newY + newHeight / 2,
  };

  return {
    x: rotaeCorner.x - width / 2,
    y: rotaeCorner.y - height / 2,
  }
}

// TODO: 待优化，考虑旋转的情况 @cuileizhen
export const getRawLeftTopCoordinate = (node: FrameNode) => {
  if (!node.absoluteBoundingBox) {
    return {
      x: 0,
      y: 0,
    };
  }

  // 处理旋转，计算旋转之前的 top left corner x/y coordinate
  if (typeof node.rotation === 'number' && Number(keepDecimal(node.rotation)) !== 0) {
    return calculateOriginalCorners(
      node.size?.x ?? node.absoluteBoundingBox?.width,
      node.size?.y ?? node.absoluteBoundingBox?.height,
      node.absoluteBoundingBox.width,
      node.absoluteBoundingBox.height,
      node.absoluteBoundingBox.x,
      node.absoluteBoundingBox.y,
    );
  }

  return {
    x: node.absoluteBoundingBox?.x ?? 0,
    y: node.absoluteBoundingBox?.y ?? 0,
  };
}
