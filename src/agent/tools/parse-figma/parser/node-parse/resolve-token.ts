import { HasTokenProperties, CssPropVariableMap, StylesIdCssMap } from '../../../../constant';
import type { NodeStyle, M0Theme } from '@ks-m0/schema';

const parseTokenName = (tokenName: string): string => {
  if (!tokenName) {
    return '';
  }

  // 移除汉字
  let result = tokenName.replace(/[\u4e00-\u9fff]/g, '');

  // 替换斜杠为横杠
  result = result.replace(/\//g, '-');

  // 移除特殊字符（保留字母、数字、横杠和空格）
  result = result.replace(/[^a-zA-Z0-9\- ]/g, '');

  // 连续的空格替换为单个横杠
  result = result.replace(/\s+/g, '-');

  // 多个连续的横杠替换为单个横杠
  result = result.replace(/-+/g, '-');

  // 去除首尾的横杠
  result = result.replace(/^-+|-+$/g, '');

  return `--${result}`;
};

// Token 解析逻辑：https://docs.corp.kuaishou.com/k/home/<USER>/fcADkCqlumTIcXvyWPgtD5IDZ
export function resolveCssToken(
  cssObj: NodeStyle,
  node: FigmaNode,
  themeData: M0Theme
): Record<string, string> {
  const tokenMap: Record<string, string> = {};

  if (!node) {
    return;
  }

  const { variables: variableMap, styles: styleMap } = themeData || {};

  if (!variableMap && !styleMap) {
    return;
  }

  const processStyleIdMapping = (styleId: string, styleMapKey: keyof typeof StylesIdCssMap) => {
    if (styleId && typeof styleId === 'string') {
      const tokenName = parseTokenName(styleMap?.[styleId]?.name);

      if (tokenName) {
        StylesIdCssMap[styleMapKey].forEach((prop) => {
          cssObj[prop] && (tokenMap[prop] = tokenName);
        });
      }
    }
  };

  // 处理 Styles
  processStyleIdMapping((node as FigmaNode).fillStyleId as string, 'fillStyleId');
  processStyleIdMapping((node as FigmaNode).strokeStyleId, 'strokeStyleId');

  // 处理 Variable
  for (const propName in cssObj) {
    if (!HasTokenProperties.includes(propName)) {
      continue;
    }

    const propVal = cssObj[propName];
    const variableFieldName = CssPropVariableMap[propName];

    switch (true) {
      case Boolean(variableFieldName): {
        const variableAlias = Array.isArray(node.boundVariables?.[variableFieldName])
          ? node.boundVariables?.[variableFieldName]?.[0]
          : node.boundVariables?.[variableFieldName];

        const variableName = parseTokenName(variableMap?.[variableAlias?.id]?.name);

        variableName && (tokenMap[propName] = variableName);

        break;
      }
      case propName === 'borderRadius': {
        if (/^\d+(px|%)$/.test(String(propVal)) || Number(propVal) === 0) {
          let variableName = variableMap?.[node.boundVariables?.topLeftRadius?.id]?.name;

          if (typeof variableName === 'string') {
            variableName = parseTokenName(variableName);
            variableName && (tokenMap[propName] = variableName);
          }
        }
        break;
      }
      // @ts-ignore
      case propName === 'color' && node.type === 'TEXT':
      case propName === 'background' && typeof propVal === 'string' && propVal.indexOf(',') !== -1: {
        const variableId = node.boundVariables?.fills?.[0]?.id;
        const variableName = parseTokenName(variableMap?.[variableId]?.name);

        variableName && (tokenMap[propName] = variableName);
      }
      default:
        break;
    }
  }

  return tokenMap;
}
