import axios, { AxiosRequestConfig, AxiosInstance, AxiosResponse } from 'axios';
import {
  GetImagesResponse,
  GetFileNodesResponse
} from '@figma/rest-api-spec';

// 自定义错误处理选项
interface RequestOptions {
  customErrorHandler?: (error: any) => Error;
  skipGlobalErrorHandler?: boolean;
}

export class FigmaClient {
  private axiosInstance: AxiosInstance;
  private oauthToken: string;
  private readonly baseUrl = 'https://api.figma.com';

  constructor(oauthToken?: string) {
    this.oauthToken = oauthToken || '';
    
    if (!this.oauthToken) {
      throw new Error('Figma access token is required. Please provide it via constructor or environment variable FIGMA_ACCESS_TOKEN');
    }

    // 创建 axios 实例
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000, // 30秒超时
      headers: {
        // TODO: 联调阶段用 OAuth Token
        'Authorization': `Bearer ${this.oauthToken}`,
        // 'X-Figma-Token': this.oauthToken,
        'Content-Type': 'application/json',
      },
    });

    // 设置拦截器
    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 响应拦截器 - 统一错误处理
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        // 检查是否有自定义错误处理器或跳过全局处理
        const requestOptions = error.config?.requestOptions as RequestOptions;
        
        if (requestOptions?.skipGlobalErrorHandler) {
          return Promise.reject(error);
        }
        
        if (requestOptions?.customErrorHandler) {
          return Promise.reject(requestOptions.customErrorHandler(error));
        }
        
        return Promise.reject(this.handleApiError(error));
      }
    );
  }

  /**
   * 统一的 Figma API 错误处理
   */
  private handleApiError(error: any): Error {
    // 处理 HTTP 错误状态码
    if (error.response) {
      const status = error.response.status;
      const url = error.config?.url || 'unknown';
      
      switch (status) {
        case 401:
          return new Error('Figma API Token Invalid or Expired');
        case 403:
          return new Error('No Figma Authorization');
        case 404:
          return new Error(`Figma Resource Not Found: ${url}`);
        case 429:
          return new Error('Figma API Rate Limit Exceeded');
        case 500:
          return new Error('Figma Internal Server Error');
        default:
          return new Error(`Figma API Error (${status}): ${error.response.statusText}`);
      }
    }
    
    // 处理网络错误
    if (error.code === 'ECONNABORTED') {
      return new Error('Figma API Request Timeout');
    }

    return new Error(`Figma API Network Error: ${error.message}`);
  }

  /**
   * 通用请求方法，支持自定义错误处理
   */
  private async request<T>(
    config: AxiosRequestConfig,
    options?: RequestOptions
  ): Promise<T> {
    // 将自定义选项附加到请求配置中
    const requestConfig = {
      ...config,
      requestOptions: options
    };
    
    const response = await this.axiosInstance.request<T>(requestConfig);
    return response.data;
  }

  /**
   * 通过 Figma API 获取节点数据
   */
  public async getFileNodes(
    fileKey: string, 
    nodeIds: string,
    options?: RequestOptions
  ): Promise<GetFileNodesResponse> {
    const endpoint = `/v1/files/${fileKey}/nodes?ids=${nodeIds}&geometry=paths`;
    
    return this.request<GetFileNodesResponse>({
      method: 'GET',
      url: endpoint
    }, options);
  }

  /**
   * 获取 Figma 文件中的图片
   */
  public async getImages(
    fileKey: string, 
    nodeIds: string, 
    scale: number = 1,
    format: 'jpg' | 'png' | 'svg' | 'pdf' = 'png',
    options?: RequestOptions
  ): Promise<GetImagesResponse> {
    const endpoint = `/v1/images/${fileKey}?ids=${nodeIds}&scale=${scale}&format=${format}`;

    return this.request<GetImagesResponse>({
      method: 'GET',
      url: endpoint
    }, {
      // 图片获取可能需要特殊的错误处理
      customErrorHandler: (error) => {
        if (error.response?.status === 404) {
          return new Error(`Image not found for nodes: ${nodeIds}`);
        }
        return this.handleApiError(error);
      },
      ...options
    });
  }
}
