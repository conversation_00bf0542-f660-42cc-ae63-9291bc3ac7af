import { ASSISTANT_NAMESPACE } from '@/util/const';
import { GlobalConfig } from '@/util/global';
import { Logger } from '@/util/log';
import * as childProcess from 'child_process';
import * as path from 'path';
import * as readline from 'readline';
import * as fs from 'fs';
import safe from 'safe-regex2';
const logger = new Logger('assistant-agent');

/*
This file provides functionality to perform regex searches on files using ripgrep.
Inspired by: https://github.com/DiscreteTom/vscode-ripgrep-utils

Key components:
1. getBinPath: Locates the ripgrep binary within the VSCode installation.
2. execRipgrep: Executes the ripgrep command and returns the output.
3. regexSearchFiles: The main function that performs regex searches on files.
   - Parameters:
     * cwd: The current working directory (for relative path calculation)
     * directoryPath: The directory to search in
     * regex: The regular expression to search for (Rust regex syntax)
     * filePattern: Optional glob pattern to filter files (default: '*')
   - Returns: A formatted string containing search results with context

The search results include:
- Relative file paths
- 2 lines of context before and after each match
- Matches formatted with pipe characters for easy reading

Usage example:
const results = await regexSearchFiles('/path/to/cwd', '/path/to/search', 'TODO:', '*.ts');

rel/path/to/app.ts
│----
│function processData(data: any) {
│  // Some processing logic here
│  // TODO: Implement error handling
│  return processedData;
│}
│----

rel/path/to/helper.ts
│----
│  let result = 0;
│  for (let i = 0; i < input; i++) {
│    // TODO: Optimize this function for performance
│    result += Math.pow(i, 2);
│  }
│----
*/

const isWindows = /^win/.test(process.platform);
const binName = isWindows ? 'rg-kwaipilot.exe' : 'rg-kwaipilot';

// Make ripgrep binary executable on startup (non-Windows platforms only)
const rgPath = path.join(process.cwd(), 'bin', `${binName}`);
if (!isWindows && fs.existsSync(rgPath)) {
  try {
    fs.chmodSync(rgPath, 0o755);
    // console.log('Made ripgrep binary executable on startup');
  } catch (error) {
    // console.error('Error making ripgrep binary executable on startup:', error);
  }
}

interface SearchResult {
  filePath: string;
  line: number;
  column: number;
  match: string;
  beforeContext: string[];
  afterContext: string[];
}

const MAX_RESULTS = 300;
const RIPGREP_TIMEOUT_MS = 15000; // 15 seconds timeout

async function execRipgrep(bin: string, args: string[]): Promise<string> {
  return new Promise((resolve, reject) => {
    // 使用内置的 timeout 选项
    const rgProcess = childProcess.spawn(bin, args, {
      timeout: RIPGREP_TIMEOUT_MS,
      killSignal: 'SIGTERM'
    });
    
    // cross-platform alternative to head, which is ripgrep author's recommendation for limiting output.
    const rl = readline.createInterface({
      input: rgProcess.stdout,
      crlfDelay: Infinity // treat \r\n as a single line break even if it's split across chunks. This ensures consistent behavior across different operating systems.
    });

    let output = '';
    let lineCount = 0;
    let matchCount = 0;
    const maxLines = MAX_RESULTS * 5; // limiting ripgrep output with max lines since there's no other way to limit results. it's okay that we're outputting as json, since we're parsing it line by line and ignore anything that's not part of a match. This assumes each result is at most 5 lines.

    rl.on('line', (line) => {
      if (lineCount < maxLines && matchCount < MAX_RESULTS) {
        output += line + '\n';
        lineCount++;
        
        // 检查是否是匹配行，如果是则增加匹配计数
        try {
          const parsed = JSON.parse(line);
          if (parsed.type === 'match') {
            matchCount++;
          }
        } catch (e) {
          // 忽略解析错误，继续处理
        }
      } else {
        rl.close();
        rgProcess.kill();
      }
    });

    let errorOutput = '';
    rgProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    rl.on('close', () => {
      if (errorOutput) {
        reject(new Error(`ripgrep process error: ${errorOutput}`));
      } else {
        resolve(output);
      }
    });
    
    rgProcess.on('error', (error) => {
      reject(new Error(`ripgrep process error: ${error.message}`));
    });
    
    rgProcess.on('exit', (code, signal) => {
      // 检查是否因超时被终止
      if (signal === 'SIGTERM') {
        reject(new Error(`ripgrep process timeout after ${RIPGREP_TIMEOUT_MS / 1000} seconds`));
      } else if (code !== 0 && code !== null) {
        reject(new Error(`ripgrep process exited with code ${code}`));
      }
    });
  });
}

// 检测是否为复杂正则表达式
function isComplexRegex(regex: string): boolean {
  // 使用 safe-regex2 库检测危险的正则表达式模式
  // 如果返回 false，说明正则表达式可能不安全（存在 ReDoS 风险）
  if (!safe(regex)) {
    return true;
  }
  
  // 额外的业务特定检查（针对 ripgrep 搜索场景的保守策略）
  const specificPatterns = [
    // 保留一些可能影响 ripgrep 性能的模式（即使 safe-regex2 认为安全）
    /\.\*\\{[^}]*\$/,     // .*{[^}]*$ 模式 - 可能在大文件中性能较差
    /.*\\{[^}]*\$/,       // 任何字符后跟 {[^}]*$ 模式
    /\\{[^}]*\$/,         // {[^}]*$ 模式
    /\.\*.*\\{[^}]*\$/,   // .* 后跟 {[^}]*$ 模式
    
    // 特定的危险组合 - 针对代码搜索场景
    /function.*\\{[^}]*\$/, // function 后跟贪婪花括号模式
    /export.*function.*\\{[^}]*\$/, // export function 后跟贪婪模式
    
    // 双重贪婪匹配 - 可能在大文件中性能较差
    /\.\*.*\*/,          // .* 后跟另一个 * 量词
    /.*\*.*\$/,          // 包含 * 和 $ 的贪婪模式
  ];
  
  return specificPatterns.some(pattern => pattern.test(regex));
}

// 验证正则表达式语法是否正确
function validateRegex(regex: string): { isValid: boolean; error?: string } {
  try {
    // 只检查正则表达式语法是否正确
    new RegExp(regex);
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: `无效的正则表达式: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

export async function regexSearchFiles(
  cwd: string,
  directoryPath: string,
  regex: string,
  filePattern?: string
): Promise<string> {
  // 验证正则表达式语法
  const validation = validateRegex(regex);
  if (!validation.isValid) {
    // 返回空结果，不抛出错误
    return JSON.stringify([]);
  }
  
  let rgPath = path.join(process.cwd(), 'bin', `${binName}`);
  if (process.env.NODE_ENV === 'test') {
    rgPath = require('@vscode/ripgrep').rgPath;
  }
  // console.log('rgPath', rgPath); // 移除调试输出

  if (!rgPath) {
    throw new Error('Could not find ripgrep binary');
  }

  // 根据正则表达式复杂度调整参数
  const isComplex = isComplexRegex(regex);
  
  // 检测是否包含环视功能（look-around）
  const hasLookAround = /\(\?[=!<>]/.test(regex);
  
  const args = [
    '--json',
    '-e',
    regex,
    '--glob',
    filePattern || '*',
    '--context',
    '1',
    '--multiline',
    '--multiline-dotall',
    '--threads',             // 限制线程数以提高稳定性
    isComplex ? '4' : '0',   // 复杂正则表达式使用4线程，普通使用默认线程
    ...(hasLookAround ? ['--pcre2'] : []), // 如果包含环视功能，启用 PCRE2
    directoryPath
  ];

  let output: string;
  try {
    output = await execRipgrep(rgPath, args);
  } catch (e) {
    console.error('Error executing ripgrep:', e);
    return 'No results found' + e;
  }
  const results: SearchResult[] = [];
  let currentResult: Partial<SearchResult> | null = null;

  output.split('\n').forEach((line) => {
    if (line) {
      try {
        const parsed = JSON.parse(line);
        if (parsed.type === 'match') {
          if (currentResult) {
            results.push(currentResult as SearchResult);
          }
          currentResult = {
            filePath: parsed.data.path.text,
            line: parsed.data.line_number,
            column: parsed.data.submatches[0].start,
            match: parsed.data.lines.text,
            beforeContext: [],
            afterContext: []
          };
        } else if (parsed.type === 'context' && currentResult) {
          if (parsed.data.line_number < currentResult.line!) {
            currentResult.beforeContext!.push(parsed.data.lines.text);
          } else {
            currentResult.afterContext!.push(parsed.data.lines.text);
          }
        }
      } catch (error) {
        console.error('Error parsing ripgrep output:', error);
        logger.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'ripgrep error',
          extra3: GlobalConfig.getConfig().getUsername(),
          extra5: `Error parsing ripgrep output:${error}`,
          extra6: new Error().stack || ''
        });
      }
    }
  });

  if (currentResult) {
    results.push(currentResult as SearchResult);
  }

  const filteredResults = results;

  return formatResults(filteredResults, cwd);
}

function formatResults(results: SearchResult[], cwd: string): string {
  const MAX_LINE_LENGTH = 500; // 设置最大行长度
  const ELLIPSIS = '...'; // 省略号

  // 处理单行文本，限制长度
  const truncateLine = (line: string): string => {
    if (line.length <= MAX_LINE_LENGTH) {
      return line;
    }
    return line.slice(0, MAX_LINE_LENGTH - ELLIPSIS.length) + ELLIPSIS;
  };

  const groupedResults: { [key: string]: SearchResult[] } = {};

  let output = '';
  if (results.length >= MAX_RESULTS) {
    output += `Showing first ${MAX_RESULTS} of ${MAX_RESULTS}+ results. Use a more specific search if necessary.\n\n`;
  } else {
    output += `Found ${results.length === 1 ? '1 result' : `${results.length.toLocaleString()} results`}.\n\n`;
  }

  // Group results by file name
  results.slice(0, MAX_RESULTS).forEach((result) => {
    const relativeFilePath = path.relative(cwd, result.filePath);
    if (!groupedResults[relativeFilePath]) {
      groupedResults[relativeFilePath] = [];
    }
    // 处理匹配行和上下文
    const processedResult = {
      ...result,
      match: truncateLine(result.match),
      beforeContext: result.beforeContext.map(truncateLine),
      afterContext: result.afterContext.map(truncateLine)
    };
    groupedResults[relativeFilePath].push(processedResult);
  });
  return JSON.stringify(groupedResults);

  // for (const [filePath, fileResults] of Object.entries(groupedResults)) {
  //   output += `${filePath.toPosix()}\n│----\n`;

  //   fileResults.forEach((result, index) => {
  //     const allLines = [...result.beforeContext, result.match, ...result.afterContext];
  //     allLines.forEach((line) => {
  //       output += `│${line?.trimEnd() ?? ''}\n`;
  //     });

  //     if (index < fileResults.length - 1) {
  //       output += '│----\n';
  //     }
  //   });

  //   output += '│----\n\n';
  // }

  // return output.trim();
}
