import { getKwaipilotGlobalPath } from '@/util/paths';
import path from 'path';
import fs from 'fs';
import { GlobalConfig } from '@/util/global';
import fm from 'front-matter';
import { Logger } from '@/util/log';

const logger = new Logger('RULES');
const APP_NAME = process.env.APP_NAME || 'kwaipilot';

// 定义front-matter属性的接口
interface RuleAttributes {
  alwaysApply?: boolean;
  [key: string]: string | boolean | undefined;
}

const MAX_RULES_LENGTH = 10000;
const MAX_FILE_READ_LENGTH = 5200;

const rulesGlobalPath = path.join(getKwaipilotGlobalPath(), 'rules');
if (!fs.existsSync(rulesGlobalPath)) {
  fs.mkdirSync(rulesGlobalPath, { recursive: true });
}
export const userRulePath = path.join(rulesGlobalPath, 'user_rules.mdr');

export const getUserRules = () => {
  if (!fs.existsSync(userRulePath)) {
    return null;
  }
  const content = readContent(userRulePath);
  const { attributes, body } = getMdrContent(content);
  logger.info(`getUserRules, file: ${userRulePath} , ${JSON.stringify(attributes)}`);

  return {
    ...(attributes || { alwaysApply: false }),
    content: body
  };
};

export const getMdrContent = (content: string) => {
  try {
    const { attributes, body } = fm<RuleAttributes>(content);
    return {
      attributes: attributes || { alwaysApply: false },
      body: body
    };
  } catch (e) {
    logger.error(`getMdrContent error, ${e}, content: ${content}`);
    return {
      attributes: { alwaysApply: false },
      body: content
    };
  }
};
export const getProjectRules = (rulesPath: string[]) => {
  const cwd = GlobalConfig.getConfig().getCwd();
  if (!fs.existsSync(cwd)) {
    return [];
  }
  const rulesFiles = findRulesFiles(cwd);
  const rulesContent = rulesFiles
    .map((file) => {
      const content = readContent(file);

      const { attributes, body } = getMdrContent(content);
      const relativePath = path.relative(cwd, file);
      logger.info(`getProjectRules, file: ${file}, relativePath: ${relativePath}, ${JSON.stringify(attributes)}`);
      // 如果规则文件在 rulesPath 中，或者 alwaysApply 为 true，则返回规则内容
      if ((rulesPath.includes(relativePath) || attributes?.alwaysApply) && body.trim()) {
        return {
          ...(attributes || {}),
          filePath: file,
          content: body.trim()
        };
      }
      return null;
    })
    .filter(Boolean);
  // 将 alwaysApply 为 true 的规则放在前面
  const sortedRules = rulesContent
    .filter((rule) => rule?.alwaysApply)
    .sort()
    .concat(rulesContent.filter((rule) => !rule?.alwaysApply).sort());
  return sortedRules;
};

export const getAllProjectRules = () => {
  const cwd = GlobalConfig.getConfig().getCwd();
  if (!fs.existsSync(cwd)) {
    return [];
  }
  const rulesFiles = findRulesFiles(cwd);
  const rulesContent = rulesFiles.map((file) => {
    const content = readContent(file);

    const { attributes, body } = getMdrContent(content);
    const relativePath = path.relative(cwd, file);
    logger.info(`getProjectRules, file: ${file}, relativePath: ${relativePath}, ${JSON.stringify(attributes)}`);
    // 如果规则文件在 rulesPath 中，或者 alwaysApply 为 true，则返回规则内容
    return {
      ...(attributes || {}),
      filePath: file,
      content: body.trim()
    };
  });

  return rulesContent;
};

/**
 * Traverses a directory recursively and returns an array of all file paths
 * @param dirPath - The directory path to traverse
 * @param fileList - Optional array to accumulate file paths (used for recursion)
 * @returns Array of file paths
 */
export const traverseDirectory = (dirPath: string, fileList: string[] = []): string[] => {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      traverseDirectory(filePath, fileList);
    } else {
      fileList.push(filePath);
    }
  });

  return fileList;
};

/**
 * Finds all markdown files with names ending with 'rules.mdr' in the specified directory
 * @param dirPath - The directory path to search in
 * @returns Array of paths to rules.mdr files
 */
export const findRulesFiles = (dirPath: string): string[] => {
  const rulesDir = path.join(dirPath, `.${APP_NAME}`, 'rules');
  // 判断 dirpath 文件夹是否存在
  if (!fs.existsSync(rulesDir)) {
    return [];
  }
  const allFiles = traverseDirectory(rulesDir);
  return allFiles.filter((filePath) => {
    const fileName = path.basename(filePath);
    return fileName.toLowerCase().endsWith('.mdr');
  });
};

export const getRulesPrompt = (rules: string[] = []) => {
  const content = getRulesContent(rules);
  if (!content) {
    logger.info('getRulesPrompt, content is empty' + rules.join(','));
    return '';
  }
  const finalContent = `PRIORITY:
- **MUST** followed to the best of your ability without interfering with the TOOL USE guidelines
- Project rules take precedence over user rules if any conflicts exist
- If any custom instructions conflict with system guidelines, please alert the user
The following additional instructions:

${content}`;
  logger.info('getRulesPrompt, content: ' + finalContent) + rules.join(',');
  return finalContent;
};

/**
 * Truncates text to the specified maximum length, ensuring only complete lines are included
 * @param text - The text to truncate
 * @param maxLength - The maximum length allowed
 * @returns The truncated text with only complete lines
 */
export const truncateToCompleteLines = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) {
    return text;
  }

  // Truncate to maxLength
  let truncated = text.substring(0, maxLength);

  // Find the last newline character to ensure we return only complete lines
  const lastNewlineIndex = truncated.lastIndexOf('\n');
  if (lastNewlineIndex !== -1) {
    return truncated.substring(0, lastNewlineIndex);
  }
  // 如果不存在换行符，则返回空字符串
  return '';
};

export const getRulesList = (rules: string[] = []) => {
  const userRules = getUserRulesContent();
  const projectRules = getProjectRules(rules);
  return [...(userRules ? [userRules] : []), ...projectRules.map((rule) => rule?.content)].reverse();
};

export const getRulesContent = (rules: string[] = []) => {
  const userRules = getUserRulesContent();
  const projectRules = getProjectRulesContent(rules, MAX_RULES_LENGTH - (userRules?.length || 0));

  if (!userRules && !projectRules?.length) {
    return '';
  }
  if (userRules && !projectRules?.length) {
    // 如果用户规则内容超过 MAX_RULES_LENGTH，则截断
    return `USER RULES:\n${truncateToCompleteLines(userRules, MAX_RULES_LENGTH)}`;
  }
  if (!userRules && projectRules?.length) {
    return `PROJECT RULES:\n${projectRules.reverse().join('\n')}`;
  }
  if (userRules.length > MAX_RULES_LENGTH) {
    // 如果加上用户规则内容超过 MAX_RULES_LENGTH，则只返回项目的规则内容
    return `USER RULES:\n${truncateToCompleteLines(userRules, MAX_RULES_LENGTH)}`;
  }
  // 同时存在时，项目规则优先级高于用户规则优先级，prompt 中优先级高的写在后边
  return `USER RULES:\n${userRules}\n\nPROJECT RULES:\n${projectRules.reverse().join('\n')}`;
};

const getUserRulesContent = () => {
  const userRules = getUserRules();
  if (!userRules || !userRules.content) {
    return '';
  }
  return userRules.content;
};

// 获取项目规则内容, 如果内容超过 maxLength，则不添加
const getProjectRulesContent = (rules: string[], maxLength: number) => {
  const projectRules = getProjectRules(rules);

  if (!projectRules || projectRules.length === 0) {
    return [];
  }

  let rulesContents = [];
  let remainLength = maxLength;
  for (const rule of projectRules) {
    if (rule && rule.content && rule.content.length > remainLength) {
      break;
    }
    rulesContents.push(rule?.content);
    remainLength -= rule!.content!.length;
  }

  return rulesContents;
};

/**
 * Reads file content with a specified maximum length
 * @param filePath - The path of the file to read
 * @param maxLength - The maximum length of content to read (default: 10000)
 * @returns The file content, truncated if it exceeds the maximum length
 */
export const readContent = (filePath: string, maxLength: number = MAX_FILE_READ_LENGTH): string => {
  if (!fs.existsSync(filePath)) {
    return '';
  }

  // 对于大文件使用流式读取，避免内存溢出
  const fd = fs.openSync(filePath, 'r');
  const buffer = Buffer.alloc(Math.min(maxLength, 8192)); // 使用较小的缓冲区
  const bytesRead = fs.readSync(fd, buffer, 0, buffer.length, 0);
  fs.closeSync(fd);

  // 转换为字符串并确保不超过最大长度
  return buffer.slice(0, bytesRead).toString('utf-8').substring(0, maxLength);
};

export const reportUserRules = async () => {
  const userRule = getUserRules();
  if (userRule) {
    await logger.reportUserAction({
      key: 'rules_content',
      type: 'user_rule',
      content: JSON.stringify({
        project_name: GlobalConfig.getConfig().getRepoInfo()?.git_url,
        type: userRule?.alwaysApply ? 'Always' : 'Manual',
        file_name: 'user_rules.mdr',
        content: userRule?.content
      })
    });
  }

  const projectRules = getAllProjectRules();
  for (let rule of projectRules) {
    await logger.reportUserAction({
      key: 'rules_content',
      type: 'project_rule',
      content: JSON.stringify({
        project_name: GlobalConfig.getConfig().getRepoInfo()?.git_url,
        type: rule?.alwaysApply ? 'Always' : 'Manual',
        file_name: (rule?.filePath && path.basename(rule?.filePath)) || '',
        content: rule?.content
      })
    });
  }
};

export const getRulesListForAgent = (rules: string[] = []) => {
  const userRules = getUserRulesContent();
  const projectRules = getProjectRulesContent(rules, MAX_RULES_LENGTH - (userRules?.length || 0));
  return {
    userRules: userRules ? [userRules] : [],
    projectRules: projectRules
  };
};
