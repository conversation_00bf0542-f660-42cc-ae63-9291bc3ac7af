import i18n from 'i18n';

export const APP_NAME = process.env.APP_NAME || 'kwaipilot';
const IS_EXTERNAL = APP_NAME === 'codeflicker';

// 直接内联翻译数据，避免文件系统操作
const translations = {
  en: {
    'indexing.indexingFile': 'Indexing: {{name}}',
    'indexing.completed': 'Indexing task completed',
    'indexing.noDirPath': 'No open directory',
    'indexing.error':
      'The project is already open in another window. Indexing is paused. Please close the other window to resume automatically.',
    'indexing.startIndexing': 'Starting project indexing',
    'indexing.nonGitRepoLimit':
      'Non-git repository indexing is currently not supported for repositories with more than 1000 files',
    'indexing.maxFilesExceeded': '{{message}}, current file count: {{count}}',
    'indexing.gitRepoError': 'Git repository error, unable to fetch latest commit information',
    'indexing.gitUpdateError':
      'Index update error, unable to fetch latest commit information, please run git pull command',
    'agent.error': 'Sorry, the request failed. Please try again later or report the issue to us.',
    'indexing.startIndexing2': 'Starting indexing ···',
    'wiki.progressing': 'Generating wiki ···',
    'wiki.error': '🚫 Wiki',
    'wiki.success': 'Wiki generation completed',
    // Project structure errors
    'wiki.error.invalidDirectory': 'Invalid directory path',
    'wiki.error.tooFewFiles': "Codebase wiki is not required by default due to current repository's minimal scale",
    'wiki.error.readError': 'Failed to read directory',
    // File operation errors
    'wiki.error.filePathRequired': 'File path is required',
    'wiki.error.contentRequired': 'Content is required',
    'wiki.error.writeFileFailed': 'Failed to write file',
    'wiki.error.notAFile': 'Path is not a file',
    'wiki.error.readFileFailed': 'Failed to read file',
    'wiki.cancelled': 'Wiki generation cancelled',
    'wiki.cancell_failed': 'Wiki generation failed',
    'wiki.error.tooManyFailures': 'Too many generation failures (3/3), please manually click "Start Build" button'
  },
  zh: {
    'indexing.indexingFile': '索引中: {{name}} ',
    'indexing.completed': '索引任务执行完毕',
    'indexing.noDirPath': '当前未打开文件夹',
    'indexing.error': `当前项目已在其他窗口中被打开，暂时无法继续索引。请关闭其他窗口，系统将自动恢复。`,
    'indexing.startIndexing': '开始索引项目',
    'indexing.nonGitRepoLimit': '非 git 仓库，当前暂不支持文件数超过 1000 的非 git 仓库索引',
    'indexing.maxFilesExceeded': '{{message}}, 当前文件数量: {{count}}',
    'indexing.gitRepoError': 'git 仓库异常，无法拉取到最新 commit信息',
    'indexing.gitUpdateError': '索引更新异常，当前无法拉取到最新commit信息，请执行 git pull 命令',
    'agent.error': IS_EXTERNAL
      ? '抱歉，请求失败。请稍后再试，或将问题反馈给我们。'
      : '抱歉，服务请求失败，建议稍后再试或联系 @Kwaipilot 智能客服',
    'indexing.startIndexing2': '索引开始构建 ···',
    'wiki.progressing': '生成项目 Wiki ···',
    'wiki.error': '🚫 项目 Wiki 生成失败',
    'wiki.success': '项目 Wiki 生成完成',
    // Project structure errors
    'wiki.error.invalidDirectory': '无效目录路径',
    'wiki.error.tooFewFiles': '因当前打开项目较小，默认无需生成项目 Wiki',
    'wiki.error.readError': '读取目录失败',
    // File operation errors
    'wiki.error.filePathRequired': '文件路径必填',
    'wiki.error.contentRequired': '内容必填',
    'wiki.error.writeFileFailed': '写入文件失败',
    'wiki.error.notAFile': '路径不是文件',
    'wiki.error.readFileFailed': '读取文件失败',
    'wiki.cancelled': '项目 Wiki 生成取消',
    'wiki.cancell_failed': '项目 Wiki 生成失败',
    'wiki.error.tooManyFailures': '生成失败次数过多 (3/3)，请手动点击“开始构建”按钮'
  }
};

// 配置 i18n，使用 staticCatalog 完全避免文件系统操作
i18n.configure({
  staticCatalog: translations
  // objectNotation: true
});

export default i18n;
