import { callLLM } from '@/http/llm';
import { AssistantMessage, ToolCall, WikiList, WikiBuildState, WikiCheckResult } from './types';
import { getProjectStructure, getFileContent, searchCodeBase, writeFile, getProjectWikiBasePath } from './functions';
import { PROJECT_WIKI_NAMESPACE } from '@/util/const';
import fs from 'fs';
import path from 'path';
import { Logger } from '@/util/log';
import { IMessenger } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import i18n from '@/i18n';
import { GlobalConfig } from '@/util/global';

const logger = new Logger('project-wiki');

let globalState = false;

// 全局构建状态管理
let buildState: WikiBuildState = {
  isBuilding: false,
  progress: 0,
  message: ''
};

// 取消标志
let isCancelled = false;

// 统一的wiki状态文件名
const WIKI_STATE_FILE = 'wiki_state.json';

// Wiki状态数据结构
interface WikiStateData {
  failureCount: number;
  creationTime?: number;
  lastUpdated: number;
}

// 获取wiki状态文件路径
const getWikiStatePath = (): string => {
  const prefixPath = getProjectWikiBasePath();
  return path.join(prefixPath, WIKI_STATE_FILE);
};

// 读取wiki状态
const getWikiState = (): WikiStateData => {
  try {
    const wikiStatePath = getWikiStatePath();
    if (!fs.existsSync(wikiStatePath)) {
      return {
        failureCount: 0,
        lastUpdated: Date.now()
      };
    }
    const data = fs.readFileSync(wikiStatePath, 'utf8');
    const parsed = JSON.parse(data);
    return {
      failureCount: typeof parsed.failureCount === 'number' ? parsed.failureCount : 0,
      creationTime: typeof parsed.creationTime === 'number' ? parsed.creationTime : undefined,
      lastUpdated: typeof parsed.lastUpdated === 'number' ? parsed.lastUpdated : Date.now()
    };
  } catch (error) {
    logger.warn('读取wiki状态时发生错误:', error);
    return {
      failureCount: 0,
      lastUpdated: Date.now()
    };
  }
};

// 保存wiki状态
const saveWikiState = (state: Partial<WikiStateData>): void => {
  try {
    const wikiStatePath = getWikiStatePath();
    const dirPath = path.dirname(wikiStatePath);

    // 确保目录存在
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // 获取当前状态并合并新状态
    const currentState = getWikiState();
    const newState: WikiStateData = {
      ...currentState,
      ...state,
      lastUpdated: Date.now()
    };

    fs.writeFileSync(wikiStatePath, JSON.stringify(newState, null, 2), 'utf8');
    logger.info('Wiki状态已保存:', newState);
  } catch (error) {
    logger.error('保存wiki状态时发生错误:', error);
  }
};

// 性能上报管理器
const perfReporter = {
  // 上报成功事件
  reportSuccess: (params: { count: number; stage: string; startTime?: number }) => {
    const endTime = Date.now();
    const duration = params.startTime ? endTime - params.startTime : 0;

    logger.perf({
      namespace: PROJECT_WIKI_NAMESPACE,
      subtag: 'generation-success',
      millis: duration,
      extra3: params.count.toString(),
      extra6: params.stage
    });
  },

  // 上报失败事件
  reportFailure: (params: {
    count?: number;
    failureCount: number;
    reason: string;
    stage: string;
    startTime?: number;
    dirPath?: string;
  }) => {
    const endTime = Date.now();
    const duration = params.startTime ? endTime - params.startTime : 0;
    const username = GlobalConfig.getConfig().getUsername();

    logger.perf({
      namespace: PROJECT_WIKI_NAMESPACE,
      subtag: 'generation-failed',
      millis: duration,
      extra3: params.count?.toString(),
      extra4: params.failureCount.toString(),
      extra5: params.reason,
      extra6: `${params.stage}${params.dirPath ? ` dirPath: ${params.dirPath}` : ''}${username ? ` user: ${username}` : ''}`
    });
  }
};

// Wiki状态管理器 - 合并所有状态操作
const wikiStateManager = {
  // 读取失败次数
  getFailureCount: (): number => {
    return getWikiState().failureCount;
  },

  // 读取创建时间
  getCreationTime: (): Date | null => {
    const state = getWikiState();
    return state.creationTime ? new Date(state.creationTime) : null;
  },

  // 保存创建时间
  saveCreationTime: (creationTime: Date): void => {
    saveWikiState({ creationTime: creationTime.getTime() });
    logger.info('创建时间已保存:', creationTime.toISOString());
  },

  // 重置失败次数
  resetFailureCount: (): void => {
    saveWikiState({ failureCount: 0 });
  },

  // 增加失败次数
  incrementFailureCount: (): number => {
    const currentCount = wikiStateManager.getFailureCount();
    const newCount = currentCount + 1;
    saveWikiState({ failureCount: newCount });
    return newCount;
  }
};

// 进度状态管理函数
const updateBuildState = (
  isBuilding: boolean,
  progress: number = 0,
  message: string = '',
  failureCount?: number,
  creationTime?: Date
) => {
  buildState = {
    isBuilding,
    progress,
    message,
    startTime: isBuilding ? buildState.startTime || new Date() : undefined,
    failureCount: failureCount !== undefined ? failureCount : wikiStateManager.getFailureCount(),
    creationTime: creationTime || wikiStateManager.getCreationTime() || undefined
  };
};

// 获取当前构建状态
export const getBuildState = (): WikiBuildState => {
  return { ...buildState };
};

// 取消Wiki生成
export const cancelGenerate = (): boolean => {
  if (buildState.isBuilding) {
    isCancelled = true;
    updateBuildState(false, buildState.progress, i18n.__('wiki.cancelled'));
    logger.info('Wiki generation cancelled by user');
    return true;
  }
  return false;
};
export const initProjectWiki = async (
  dirPath: string,
  messager: IMessenger<ToCoreProtocol, FromCoreProtocol>,
  force: boolean = false
) => {
  const sendProgress = (progress: number, message: string) => {
    if (isCancelled) {
      updateBuildState(false, 0, i18n.__('wiki.cancelled'));
      return;
    }
    updateBuildState(true, progress, message);
    messager.send('wiki/generateProgress', {
      progress,
      message
    });
  };
  if (!dirPath) {
    sendProgress(0, i18n.__('wiki.error.invalidDirectory'));
    return;
  }
  try {
    if (buildState.isBuilding) {
      return;
    }
    // 重置取消标志
    isCancelled = false;

    // 检查失败次数，如果超过3次且不是强制生成，则不再生成
    const currentFailureCount = wikiStateManager.getFailureCount();
    if (currentFailureCount >= 3 && !force) {
      const failureMessage =
        i18n.__('wiki.error.tooManyFailures') ||
        `生成失败次数过多 (${currentFailureCount}/3)，请手动点击“开始构建”按钮`;
      // sendProgress(0, failureMessage);
      updateBuildState(false, 0, failureMessage, currentFailureCount);

      logger.warn(`Wiki generation skipped due to too many failures: ${currentFailureCount}`);
      return;
    }

    const checkResult = await checkProjectWiki();
    if (checkResult.exists && !force) {
      updateBuildState(false, 1, i18n.__('wiki.success'));
      return;
    }
    updateBuildState(true, 0.01, i18n.__('wiki.progressing'));

    let finalAnalysis = '';
    let progress = 0.01;
    let miniStep = 0.03;
    let count = 1;

    sendProgress(progress, i18n.__('wiki.progressing'));
    const analysisMessages: any[] = [];

    // Step 1: Get initial analysis
    let { data: currentResponse } = await callLLM(analysisMessages, dirPath);
    logger.info('currentResponse:', currentResponse);
    while (currentResponse && currentResponse.tool_calls) {
      // 检查是否被取消
      if (isCancelled) {
        logger.info('Wiki generation was cancelled, terminating...');
        return;
      }

      count++;
      // 构建助手消息对象
      const assistantMessage: AssistantMessage = {
        role: 'assistant',
        content: currentResponse.content,
        tool_calls: currentResponse.tool_calls
      };
      analysisMessages.push(assistantMessage);
      logger.info('添加助手消息到历史:', JSON.stringify(assistantMessage, null, 2));
      const completeTask = currentResponse.tool_calls.find((toolCall: ToolCall) => toolCall.name === 'completeTask');
      if (completeTask) {
        // 解析 completeTask 的参数
        const taskParams = completeTask.input;
        const { summary, finishReason } = taskParams;

        if (finishReason === 'success') {
          // 成功时重置失败次数并保存创建时间
          wikiStateManager.resetFailureCount();
          const creationTime = new Date();
          wikiStateManager.saveCreationTime(creationTime);

          // 性能上报日志 - 成功
          perfReporter.reportSuccess({
            count,
            stage: 'llm_complete',
            startTime: buildState.startTime?.getTime()
          });

          sendProgress(1, i18n.__('wiki.success'));
          updateBuildState(false, 1, i18n.__('wiki.success'), 0, creationTime);
        } else {
          logger.info('finishReason:', summary, finishReason);
          // 如果不是成功，通过 sendProgress 返回失败原因

          // 失败时增加失败次数
          const newFailureCount = wikiStateManager.incrementFailureCount();

          // 性能上报日志 - 失败
          perfReporter.reportFailure({
            count,
            failureCount: newFailureCount,
            reason: finishReason,
            stage: 'llm_failed',
            startTime: buildState.startTime?.getTime(),
            dirPath
          });

          if (finishReason.toLowerCase() === 'too_few_files' || finishReason.toLowerCase() === 'empty_directory') {
            const failureMessage = `${i18n.__('wiki.error')}: ${i18n.__('wiki.error.tooFewFiles')}`;
            sendProgress(0, failureMessage);
            updateBuildState(false, 0, failureMessage, newFailureCount);
            // 抛出特殊错误来中断整个流程
            throw new Error(`PROJECT_VALIDATION_FAILED: ${failureMessage}`);
          } else {
            const failureMessage = `${i18n.__('wiki.error')}: ${summary}`;
            sendProgress(0, failureMessage);
            updateBuildState(false, 0, failureMessage, newFailureCount);
            // 抛出特殊错误来中断整个流程
            throw new Error(`PROJECT_VALIDATION_FAILED: ${failureMessage}`);
          }
        }

        logger.info('completeTask called:', { summary, finishReason });
        break;
      }
      const functionResults = await Promise.all(
        currentResponse.tool_calls.map(async (toolCall: ToolCall) => {
          const functionName = toolCall.name;
          const functionArgs = toolCall.input;
          let result;
          logger.info('执行工具调用:', {
            id: toolCall.id,
            name: functionName,
            args: functionArgs
          });

          if (functionName === 'getProjectStructure') {
            const structureResult = getProjectStructure(functionArgs.dirPath);

            // 检查是否返回了错误（只有包含error字段的对象才是真正的错误）
            if (
              structureResult &&
              typeof structureResult === 'object' &&
              !Array.isArray(structureResult) &&
              'error' in structureResult
            ) {
              const errorInfo = structureResult as { error: string; reason: string };

              // 获取项目根目录路径，判断是否需要对错误进行严格处理
              const projectRootPath = dirPath;
              const isProjectRoot = path.resolve(functionArgs.dirPath) === path.resolve(projectRootPath);

              // 只有在项目根目录且是文件数量不足的错误时才中断流程
              if (isProjectRoot && errorInfo.error === 'TOO_FEW_FILES') {
                // 失败时增加失败次数
                const newFailureCount = wikiStateManager.incrementFailureCount();

                // 性能上报日志 - 项目结构验证失败
                perfReporter.reportFailure({
                  count,
                  failureCount: newFailureCount,
                  reason: 'project_structure_validation',
                  stage: 'structure_check',
                  startTime: buildState.startTime?.getTime(),
                  dirPath
                });

                // 直接发送失败消息并结束生成过程
                const failureMessage = `${i18n.__('wiki.error')}: ${errorInfo.reason}`;
                sendProgress(0, failureMessage);
                updateBuildState(false, 0, failureMessage, newFailureCount);
                logger.error('Project structure validation failed:', errorInfo);

                // 抛出特殊错误来中断整个流程
                throw new Error(`PROJECT_VALIDATION_FAILED: ${errorInfo.reason}`);
              } else {
                // 对于非根目录或其他类型的错误，只记录日志但不中断流程
                logger.warn('Project structure issue (non-critical):', errorInfo);
                result = []; // 返回空数组继续处理
              }
            } else {
              result = structureResult;
            }
          } else if (functionName === 'getFileContent') {
            result = getFileContent(functionArgs.filePath);
          } else if (functionName === 'searchCodeBase') {
            result = await searchCodeBase(functionArgs.query, functionArgs.targetDirectory);
          } else if (functionName === 'writeFile') {
            miniStep = 0.1;
            if (progress < 0.6) {
              progress = 0.6;
            }
            result = writeFile(functionArgs.filePath, functionArgs.content);
            progress = Math.min(progress + miniStep, 0.95);
            sendProgress(progress, i18n.__('wiki.progressing'));
          }
          const toolResult = {
            role: 'tool' as const,
            tool_call_id: toolCall.id,
            content: typeof result === 'string' ? result : JSON.stringify(result)
          };

          logger.info('工具调用结果:', {
            tool_call_id: toolResult.tool_call_id,
            tool_name: toolCall.name,
            contentLength: toolResult.content.length,
            toolResult: toolResult.content
          });

          return toolResult;
        })
      );

      analysisMessages.push(...functionResults);
      logger.info('当前消息历史长度:', analysisMessages.length);
      logger.info(
        '最后两条消息类型:',
        analysisMessages.slice(-2).map((msg) => ({
          role: msg.role,
          hasToolCalls: 'tool_calls' in msg && (msg.tool_calls?.length ?? 0) > 0,
          hasToolCallId: 'tool_call_id' in msg
        }))
      );

      logger.info(
        '发送给LLM的消息历史:',
        JSON.stringify(
          analysisMessages.map((msg) => ({
            role: msg.role,
            content: msg.content ? msg.content.substring(0, 100) + '...' : undefined,
            tool_calls: 'tool_calls' in msg ? msg.tool_calls?.length : undefined,
            tool_call_id: 'tool_call_id' in msg ? msg.tool_call_id : undefined
          })),
          null,
          2
        )
      );

      // 在调用LLM前再次检查取消状态
      if (isCancelled) {
        logger.info('Wiki generation was cancelled before LLM call, terminating...');
        return;
      }

      const { data: nextResponse } = await callLLM(analysisMessages, dirPath);
      currentResponse = nextResponse;
      if (progress > 0.4) {
        miniStep = 0.01;
      }
      progress = progress <= 0.6 ? Math.min(progress + miniStep, 0.6) : progress;
      sendProgress(progress, i18n.__('wiki.progressing'));
    }
    const checkResultFinal = await checkProjectWiki();
    if (!checkResultFinal.exists) {
      // 最终检查失败时增加失败次数
      const newFailureCount = wikiStateManager.incrementFailureCount();

      // 性能上报日志 - 最终检查失败
      perfReporter.reportFailure({
        count,
        failureCount: newFailureCount,
        reason: 'final_check_failed',
        stage: 'final_check',
        startTime: buildState.startTime?.getTime(),
        dirPath
      });

      sendProgress(0, i18n.__('wiki.error'));
      updateBuildState(false, 0, i18n.__('wiki.error'), newFailureCount);
      return;
    }
    // 成功时重置失败次数并保存创建时间
    wikiStateManager.resetFailureCount();
    const creationTime = new Date();
    wikiStateManager.saveCreationTime(creationTime);

    // 性能上报日志 - 最终成功
    perfReporter.reportSuccess({
      count,
      stage: 'final_check',
      startTime: buildState.startTime?.getTime()
    });

    sendProgress(1, i18n.__('wiki.success'));
    updateBuildState(false, 1, i18n.__('wiki.success'), 0, creationTime);
    return finalAnalysis;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // 如果是项目验证失败，不需要重复发送错误消息
    if (errorMessage.startsWith('PROJECT_VALIDATION_FAILED:')) {
      logger.info('Project validation failed, terminating wiki generation');
    } else {
      // 其他错误正常处理，增加失败次数
      const newFailureCount = wikiStateManager.incrementFailureCount();

      // 性能上报日志 - 异常错误
      perfReporter.reportFailure({
        failureCount: newFailureCount,
        reason: 'exception',
        stage: 'exception',
        startTime: buildState.startTime?.getTime(),
        dirPath
      });

      logger.error('Error executing generate wiki:', {
        error: error,
        context: 'generateWiki',
        errorMessage: errorMessage,
        timestamp: new Date().toISOString()
      });
      sendProgress(0, i18n.__('wiki.error'));
      updateBuildState(false, 0, i18n.__('wiki.error'), newFailureCount);
    }
  }
};

export const checkProjectWiki = async (): Promise<WikiCheckResult> => {
  const requiredFiles = [
    'ARCHITECTURE.md',
    'DEVELOPMENT_GUIDE.md',
    'PROJECT_OVERVIEW.md',
    'TECH_STACK_AND_PREFERENCES.md'
  ];

  // 获取当前构建状态
  const currentBuildState = getBuildState();

  // 辅助函数：生成不存在的返回结果（带构建状态检查）
  const createNotExistsResult = (): WikiCheckResult => {
    if (currentBuildState.isBuilding || currentBuildState.message) {
      return {
        exists: false,
        buildState: currentBuildState
      };
    }
    return { exists: false };
  };

  // 如果正在构建中，或者有构建状态消息，返回构建状态
  if (currentBuildState.isBuilding) {
    return {
      exists: false,
      buildState: currentBuildState
    };
  }

  if (globalState) {
    const creationTime = wikiStateManager.getCreationTime();
    return {
      exists: true,
      buildState: {
        isBuilding: false,
        progress: 1,
        message: i18n.__('wiki.success'),
        failureCount: 0,
        creationTime: creationTime || undefined
      }
    };
  }
  try {
    // 构建projectWiki文件夹路径，使用与writeFile相同的逻辑
    const prefixPath = getProjectWikiBasePath();

    // 检查projectWiki文件夹是否存在
    if (!fs.existsSync(prefixPath)) {
      return createNotExistsResult();
    }

    // 检查每个必需的文件
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        return createNotExistsResult();
      }

      // 检查文件内容是否为空
      try {
        const content = fs.readFileSync(filePath, 'utf8').trim();
        if (content.trim().length === 0) {
          return createNotExistsResult();
        }
      } catch (error) {
        // 如果读取文件失败，返回false
        return createNotExistsResult();
      }
    }
    globalState = true;
    // 所有文件都存在且内容不为空
    const creationTime = wikiStateManager.getCreationTime();
    return {
      exists: true,
      buildState: {
        isBuilding: false,
        progress: 1,
        message: i18n.__('wiki.success'),
        failureCount: 0,
        creationTime: creationTime || undefined
      }
    };
  } catch (error) {
    // 如果发生任何错误，返回false
    logger.error('检查项目信息文件时发生错误:', error);
    return createNotExistsResult();
  }
};

export const getWikiList = async (): Promise<WikiList | null> => {
  const checkResult = await checkProjectWiki();
  if (!checkResult.exists) {
    return null;
  }

  try {
    const requiredFiles = [
      'ARCHITECTURE.md',
      'DEVELOPMENT_GUIDE.md',
      'PROJECT_OVERVIEW.md',
      'TECH_STACK_AND_PREFERENCES.md'
    ];
    const prefixPath = getProjectWikiBasePath();

    const wikis = [];

    // 读取每个文件的内容
    for (const fileName of requiredFiles) {
      const filePath = path.join(prefixPath, fileName);
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        wikis.push({
          content,
          filename: fileName
        });
      } catch (error) {
        logger.error(`读取文件 ${fileName} 时发生错误:`, error);
        wikis.push({
          content: '',
          filename: fileName
        });
      }
    }

    const creationTime = wikiStateManager.getCreationTime();
    return {
      wikis,
      path: prefixPath,
      lastUpdated: Date.now(),
      creationTime: creationTime ? creationTime.getTime() : undefined
    };
  } catch (error) {
    logger.error('获取项目信息详情时发生错误:', error);
    return null;
  }
};
export const deleteProjectWiki = async () => {
  try {
    const prefixPath = getProjectWikiBasePath();
    if (fs.existsSync(prefixPath)) {
      fs.rmSync(prefixPath, { recursive: true, force: true });
      globalState = false;
      // 删除项目wiki时重置失败次数
      wikiStateManager.resetFailureCount();
      logger.info('项目wiki已删除，失败次数和创建时间已重置');
    }
    return true;
  } catch (error) {
    logger.error('删除项目信息时发生错误:', error);
    return false;
  }
};

// Wiki状态访问器 - 合并对外暴露的状态操作
export const wikiStateAccessor = {
  // 手动重置失败次数
  resetFailureCount: (): boolean => {
    try {
      wikiStateManager.resetFailureCount();
      logger.info('Wiki failure count has been reset');
      return true;
    } catch (error) {
      logger.error('重置失败次数时发生错误:', error);
      return false;
    }
  },

  // 获取当前失败次数
  getFailureCount: (): number => {
    return wikiStateManager.getFailureCount();
  },

  // 获取wiki创建时间
  getCreationTime: (): Date | null => {
    return wikiStateManager.getCreationTime();
  }
};
