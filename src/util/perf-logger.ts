import { createLogStream } from './base-log';
import { getLogsDirPath } from './paths';
import * as rfs from 'rotating-file-stream';
import path from 'path';
import fs from 'fs';

/**
 * 性能日志记录器
 * 专门用于记录各个异步函数的耗时信息
 */
export class PerfLogger {
  private logStream: rfs.RotatingFileStream;
  private sessionId: string;
  private chatId: string;
  private startTimes: Map<string, number> = new Map();
  private stageIndex: number = 0;

  constructor(sessionId: string, chatId?: string) {
    this.sessionId = sessionId;
    this.chatId = chatId || sessionId; // 如果没有提供 chatId，则使用 sessionId

    // 确保日志目录存在
    const logDir = getLogsDirPath();
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 使用 chatId 作为日志文件的唯一标识
    const logFileName = `perf-agent-${this.chatId}`;

    // 检查是否已存在该 chatId 的日志文件，如果存在则复用
    const existingLogFile = this.findExistingLogFile(logDir, logFileName);
    if (existingLogFile) {
      console.log(`复用已存在的日志文件: ${existingLogFile}`);
      // 读取现有文件的最后一个 stageIndex，确保新日志的索引连续
      this.stageIndex = this.getLastStageIndex(path.join(logDir, existingLogFile)) + 1;
    }

    this.logStream = createLogStream(logFileName, {
      maxSize: '50M',
      maxFiles: 10,
      interval: '1d'
    });
  }

  /**
   * 记录函数开始执行
   * @param functionName 函数名称
   * @param context 上下文信息
   */
  start(functionName: string, context?: string): void {
    const timestamp = Date.now();
    this.startTimes.set(functionName, timestamp);

    const stage = context ? `${context}_${functionName}` : functionName;
    this.writeLog(stage, context || 'performance', true, false, 'info', 'start');
  }

  /**
   * 记录函数执行结束并计算耗时
   * @param functionName 函数名称
   * @param context 上下文信息
   * @returns 耗时（毫秒）
   */
  end(functionName: string, context?: string): number {
    const endTime = Date.now();
    const startTime = this.startTimes.get(functionName);

    if (!startTime) {
      const stage = context ? `${context}_${functionName}` : functionName;
      this.writeLog(stage, context || 'performance', false, true, 'error', 'no_start_time_found');
      return 0;
    }

    const duration = endTime - startTime;
    this.startTimes.delete(functionName);

    const stage = context ? `${context}_${functionName}` : functionName;
    this.writeLog(stage, context || 'performance', false, true, 'info', 'end', duration);

    return duration;
  }

  /**
   * 记录单次性能数据（不需要start/end配对）
   * @param functionName 函数名称
   * @param duration 耗时（毫秒）
   * @param context 上下文信息
   */
  record(functionName: string, duration: number, context?: string): void {
    const stage = context ? `${context}_${functionName}` : functionName;
    this.writeLog(stage, context || 'performance', false, false, 'info', 'record', duration);
  }

  /**
   * 记录关键性能里程碑
   * @param milestone 里程碑名称
   * @param totalDuration 总耗时
   * @param details 详细信息
   */
  milestone(milestone: string, totalDuration: number, details?: Record<string, any>): void {
    const message = details ? JSON.stringify(details) : 'milestone';
    this.writeLog(milestone, 'milestone', false, false, 'info', message, totalDuration);
  }

  /**
   * 记录阶段性能汇总
   * @param phase 阶段名称
   * @param functions 函数耗时列表
   * @param totalDuration 总耗时
   */
  phaseSummary(phase: string, functions: Array<{name: string, duration: number}>, totalDuration: number): void {
    this.writeLog(`${phase}_summary`, 'phase', true, false, 'info', 'phase_start');

    functions.forEach(func => {
      const percentage = totalDuration > 0 ? ((func.duration / totalDuration) * 100).toFixed(1) : '0.0';
      this.writeLog(`${phase}_${func.name}`, 'phase', false, false, 'info', `${percentage}%`, func.duration);
    });

    this.writeLog(`${phase}_summary`, 'phase', false, true, 'info', 'phase_end', totalDuration);
  }

  /**
   * 记录错误信息
   * @param functionName 函数名称
   * @param error 错误信息
   */
  error(functionName: string, error: any): void {
    const errorStr = error instanceof Error ? error.message : String(error);
    this.writeLog(functionName, 'error', false, false, 'error', errorStr);
  }

  /**
   * 记录警告信息
   * @param functionName 函数名称
   * @param warning 警告信息
   */
  warn(functionName: string, warning: string): void {
    this.writeLog(functionName, 'warning', false, false, 'warn', warning);
  }

  /**
   * 记录调试信息
   * @param functionName 函数名称
   * @param debug 调试信息
   */
  debug(functionName: string, debug: string): void {
    this.writeLog(functionName, 'debug', false, false, 'debug', debug);
  }

  /**
   * 写入日志 - JSON格式
   * @param stage 阶段名称
   * @param messageType 消息类型
   * @param isStart 是否开始
   * @param isEnd 是否结束
   * @param level 日志级别
   * @param message 消息内容
   * @param duration 耗时（可选）
   */
  private writeLog(stage: string, messageType: string, isStart: boolean, isEnd: boolean, level: string = 'info', message: string = 'timestamp', duration?: number): void {
    const now = new Date();
    const localtime = now.toLocaleString('zh-CN', {
      year: '2-digit',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    }).replace(/\//g, '/').replace(/,/g, '');

    const logEntry = {
      localtime,
      sessionId: this.sessionId,
      stage,
      time: now.toISOString(),
      messageType,
      stageIndex: this.stageIndex++,
      isStart,
      isEnd,
      level,
      message,
      ...(duration !== undefined && { duration })
    };

    try {
      this.logStream.write(JSON.stringify(logEntry) + '\n');
    } catch (error) {
      console.error('Failed to write performance log:', error);
    }
  }

  /**
   * 关闭日志流
   */
  close(): void {
    this.writeLog('session_end', 'session', false, true, 'info', 'session_closed');

    try {
      this.logStream.end();
    } catch (error) {
      console.error('Failed to close performance log stream:', error);
    }
  }

  /**
   * 获取当前正在计时的函数列表
   */
  getActiveFunctions(): string[] {
    return Array.from(this.startTimes.keys());
  }

  /**
   * 清除所有计时器
   */
  clearAllTimers(): void {
    this.startTimes.clear();
    this.writeLog('clear_timers', 'system', false, false, 'info', 'all_timers_cleared');
  }

  /**
   * 查找已存在的日志文件
   */
  private findExistingLogFile(logDir: string, baseFileName: string): string | null {
    try {
      const files = fs.readdirSync(logDir);
      // 查找匹配的日志文件（包括轮转后的文件）
      const matchingFiles = files.filter(file =>
        file.startsWith(baseFileName) && file.endsWith('.log')
      );

      if (matchingFiles.length > 0) {
        // 返回最新的日志文件（通常是没有数字后缀的主文件）
        const mainFile = matchingFiles.find(file => file === `${baseFileName}.log`);
        return mainFile || matchingFiles[0];
      }
    } catch (error) {
      console.warn(`检查已存在日志文件时出错: ${error}`);
    }
    return null;
  }

  /**
   * 获取现有日志文件中的最后一个 stageIndex
   */
  private getLastStageIndex(filePath: string): number {
    try {
      if (!fs.existsSync(filePath)) {
        return 0;
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.trim());

      let lastIndex = 0;
      // 从后往前读取，找到最后一个有效的 stageIndex
      for (let i = lines.length - 1; i >= 0; i--) {
        try {
          const logEntry = JSON.parse(lines[i]);
          if (typeof logEntry.stageIndex === 'number') {
            lastIndex = logEntry.stageIndex;
            break;
          }
        } catch (error) {
          // 忽略解析错误，继续查找
        }
      }

      return lastIndex;
    } catch (error) {
      console.warn(`读取现有日志文件的 stageIndex 时出错: ${error}`);
      return 0;
    }
  }
}

/**
 * 性能日志管理器
 * 管理多个会话的性能日志记录器
 */
export class PerfLoggerManager {
  private static instance: PerfLoggerManager;
  private loggers: Map<string, PerfLogger> = new Map();

  private constructor() {}

  static getInstance(): PerfLoggerManager {
    if (!PerfLoggerManager.instance) {
      PerfLoggerManager.instance = new PerfLoggerManager();
    }
    return PerfLoggerManager.instance;
  }

  /**
   * 获取或创建指定会话的性能日志记录器
   * @param sessionId 会话ID
   * @param chatId 聊天ID（可选，用作日志文件的唯一标识）
   */
  getLogger(sessionId: string, chatId?: string): PerfLogger {
    // 使用 chatId 作为 key，如果没有 chatId 则使用 sessionId
    const loggerKey = chatId || sessionId;

    let logger = this.loggers.get(loggerKey);
    if (!logger) {
      logger = new PerfLogger(sessionId, chatId);
      this.loggers.set(loggerKey, logger);
    }
    return logger;
  }

  /**
   * 关闭指定会话的性能日志记录器
   * @param sessionId 会话ID
   * @param chatId 聊天ID（可选，用于匹配正确的日志记录器）
   */
  closeLogger(sessionId: string, chatId?: string): void {
    // 使用 chatId 作为 key，如果没有 chatId 则使用 sessionId
    const loggerKey = chatId || sessionId;

    const logger = this.loggers.get(loggerKey);
    if (logger) {
      logger.close();
      this.loggers.delete(loggerKey);
    }
  }

  /**
   * 关闭所有性能日志记录器
   */
  closeAllLoggers(): void {
    for (const [, logger] of this.loggers) {
      logger.close();
    }
    this.loggers.clear();
  }
}
