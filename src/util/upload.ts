// 从 vscode 插件搬过来的

import axios, { AxiosProgressEvent, CanceledError } from "axios";
import { v4 as uuidv4 } from "uuid";
import { GlobalConfig } from "./global";

export interface UploadParams {
    filePath: string;
    onStart?: (file: UploadFile) => void;
    onProgress?: (file: UploadFile) => void;
    onSuccess?: (data: UploadFile) => void;
    onFailed?: (file: UploadFile) => void;
}

export interface UploadBufferParams {
    buffer: Buffer;
    filename: string;
    mimeType?: string;
    onStart?: (file: UploadFile) => void;
    onProgress?: (file: UploadFile) => void;
    onSuccess?: (data: UploadFile) => void;
    onFailed?: (file: UploadFile) => void;
}

export interface UploadFile {
    biz?: string;
    filename: string;
    id?: number;
    uid: string;
    path?: string;
    url?: string;
    type?: string;
    size: number;
    username?: string;
    progress?: number;
    status?: "uploading" | "done" | "error";
    [key: string]: any;
}

export const uploadBuffer = async (url: string, { buffer, filename, mimeType, onStart, onProgress, onSuccess, onFailed }: UploadBufferParams): Promise<UploadFile> => {
    // 使用 form-data 包创建 FormData
    const formData = new FormData();
    formData.append("file", new Blob([buffer as any]), filename || "file");

    const fileInfo: UploadFile = {
        filename: filename || "",
        type: mimeType,
        size: buffer.length,
        uid: uuidv4(),
        status: "uploading",
        progress: 0,
    };
    onStart?.(fileInfo);

    const isExternal = GlobalConfig.getConfig().getVersionType() === 'External';

    const path = isExternal ? `/api/proxy/upload/eapi/kwaipilot/file/upload` : `/eapi/kwaipilot/file/upload`;

    const token = isExternal ? GlobalConfig.getConfig().getJwtToken() : undefined;

    const headers = isExternal && token
        ? {
            "Content-Type": "multipart/form-data",
            "Authorization": `Bearer ${token}`,
        }
        : { "Content-Type": "multipart/form-data" };

    const uploadUrl = normalizeUrl(url, path);

    const promise: Promise<UploadFile> = new Promise((resolve, reject) => {
        // temp?.("取消请求"); // 取消上一次请求
        axios({
            method: "post",
            url: uploadUrl,
            data: formData,
            // 针对大文件优化超时设置
            timeout: Math.max(60000, fileInfo.size / 1024), // 至少1分钟，每KB多1ms
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
            headers,
            onUploadProgress: (e: AxiosProgressEvent) => {
                const progress = Math.round((e.loaded / (e.total || e.bytes || e.loaded)) * 100);
                onProgress?.({
                    ...fileInfo,
                    progress: Math.min(progress, 100),
                    status: "uploading",
                    loaded: e.loaded,
                    total: e.total,
                });
            },
        }).then((response) => {
            if (response?.status == 200 && response?.data && response.data?.status == 200 && response.data.data) {
                // console.log('success',response.data.data);
                onSuccess?.({
                    ...fileInfo,
                    ...response.data.data,
                    progress: 100,
                    status: "done",
                });
                resolve(response.data.data);
            }
            else {
                const errorMsg = `Upload Failed: HTTP ${response?.status}, Response: ${JSON.stringify(response?.data)}`;
                onFailed?.({
                    ...fileInfo,
                    status: "error",
                    error: errorMsg,
                });
                reject(new Error(errorMsg));
            }
        }).catch((err) => {
            if (err instanceof CanceledError) {
                onFailed?.({ ...fileInfo, status: "error", error: "Request Canceled" });
                reject(null);
            }
            else {
                const errorMsg = `Network Error: ${err.message || "Unknown Error"}`;
                onFailed?.({
                    ...fileInfo,
                    status: "error",
                    error: errorMsg,
                    originalError: err,
                });
                reject(new Error(errorMsg));
            }
        });
    });
    return promise;
};

// 标准化 URL，确保路径拼接正确
const normalizeUrl = (baseUrl: string, path: string): string => {
    const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `${cleanBaseUrl}${cleanPath}`;
};
