import fs from 'node:fs';
import { getErrorLogsPath, getLogsDirPath } from '@/util/paths';
import * as rfs from 'rotating-file-stream';
import path from 'path';
import { IS_DEV } from './const';

type LogStreamConfig = {
  interval: string;
  compress: string;
  size: string;
  path: string;
  teeToStdout: boolean;
  maxFiles: number;
  maxSize: string;
};

// 延迟初始化，避免模块级别的函数调用
let _streamErrorLogPath: string | null = null;
let _streamConfig: LogStreamConfig | null = null;

function getStreamErrorLogPath(): string {
  if (!_streamErrorLogPath) {
    _streamErrorLogPath = getErrorLogsPath();

    // 确保日志目录存在
    const logDir = path.dirname(_streamErrorLogPath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }
  return _streamErrorLogPath;
}

function getStreamConfig(): LogStreamConfig {
  if (!_streamConfig) {
    _streamConfig = {
      interval: '1h',
      compress: 'gzip',
      size: '10M',
      path: getLogsDirPath(),
      teeToStdout: IS_DEV,
      maxFiles: 30,
      maxSize: '100M'
    } as const;
  }
  return _streamConfig;
}

function writeStreamError(message: string, error: any) {
  const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }).replace(/,/g, '');
  const errorMessage = `[${timestamp}] ${message}: ${error instanceof Error ? error.stack : JSON.stringify(error)}\n`;
  try {
    fs.appendFileSync(getStreamErrorLogPath(), errorMessage);
  } catch (e) {
    // 如果连写入错误日志都失败了，只能输出到控制台
    console.error('Failed to write to stream error log:', e);
  }
}

const logStreamStore = new Map<string, rfs.RotatingFileStream>();

export function createLogStream(logName: string, options?: Partial<LogStreamConfig>) {
  const stream = logStreamStore.get(logName);
  if (stream) {
    return stream;
  }

  const streamConfig = getStreamConfig();
  let logStream: rfs.RotatingFileStream | null = rfs.createStream(`${logName}.log`, {
    ...streamConfig,
    ...options
  });
  logStream.on('error', (err) => {
    writeStreamError('Log stream error', err);
  });

  logStreamStore.set(logName, logStream);
  return logStream;
}

process.on('beforeExit', () => {
  for (const [key, logStream] of logStreamStore) {
    try {
      logStream.end();
    } catch (err) {
      logStreamStore.delete(key);
      writeStreamError('Error closing log stream', err);
    }
  }
});

export function createLogger(logName: string, options?: Partial<LogStreamConfig>) {
  return class Logging {
    private _logStream?: rfs.RotatingFileStream;
    private _errorLogStream?: rfs.RotatingFileStream;
    private _warnLogStream?: rfs.RotatingFileStream;
    private _debugLogStream?: rfs.RotatingFileStream;

    readonly scope: string;
    readonly logName = logName;

    constructor(scope: string) {
      this.scope = scope;
    }

    private get logStream(): rfs.RotatingFileStream {
      if (!this._logStream) {
        this._logStream = createLogStream(logName, options);
      }
      return this._logStream;
    }

    private get errorLogStream(): rfs.RotatingFileStream {
      if (!this._errorLogStream) {
        this._errorLogStream = createLogStream(`${logName}-error`, options);
      }
      return this._errorLogStream;
    }

    private get warnLogStream(): rfs.RotatingFileStream {
      if (!this._warnLogStream) {
        this._warnLogStream = createLogStream(`${logName}-warn`, options);
      }
      return this._warnLogStream;
    }

    private get debugLogStream(): rfs.RotatingFileStream {
      if (!this._debugLogStream) {
        this._debugLogStream = createLogStream(`${logName}-debug`, options);
      }
      return this._debugLogStream;
    }

    private format(level: string, message: any, ...optionalParams: any[]) {
      const timestamp = new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }).replace(/,/g, '');
      const formattedMessage = typeof message === 'object' ? JSON.stringify(message) : message;
      const formattedParams = optionalParams.map((param) =>
        typeof param === 'object' ? JSON.stringify(param) : param
      );
      const logMessage = `[${timestamp}] [${level}] pid: ${process.pid}, ppid: ${process.ppid} ${this.scope
        } ${formattedMessage} ${formattedParams.join(' ')} ${optionalParams} \n`;
      return logMessage;
    }

    info(message: any, ...optionalParams: any[]) {
      this.logStream.write(this.format('info', message, ...optionalParams));
    }

    error(message: any, ...optionalParams: any[]) {
      this.errorLogStream.write(this.format('error', message, ...optionalParams));
    }

    debug(message: any, ...optionalParams: any[]) {
      this.debugLogStream.write(this.format('debug', message, ...optionalParams));
    }

    warn(message: any, ...optionalParams: any[]) {
      this.warnLogStream.write(this.format('warn', message, ...optionalParams));
    }
  };
}
