import { IS_DEV } from './const';
import { Logger } from '@/util/log';

const logger = new Logger('mod');

export async function loadSqlite3() {
  try {
    const sqlite3 = await import('sqlite3');
    const mod = IS_DEV ? sqlite3.default : sqlite3;
    logger.info(`sqlite3 module keys ${Object.keys(mod)}`);
    logger.info(`sqlite3 module version ${JSON.stringify(process.env)}`);
    return mod;
  } catch (err) {
    logger.error('load Sqlite3 error', String(err));
    throw err;
  }
}
loadSqlite3();
