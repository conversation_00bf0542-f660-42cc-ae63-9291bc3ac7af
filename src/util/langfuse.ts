import Langfuse, { type LangfuseTraceClient } from 'langfuse';
import { hostname } from 'os';
import { config } from 'dotenv';
import { IS_DEV } from './const';
import { Api } from '@/http';
import { GlobalConfig } from '@/util/global';
// @IMP: 生产环境在 build.js 中注入 .env.trace 文件 防止打包执行找不到 .env.trace 文件
IS_DEV && config({ path: '.env.trace' });

let langfuse: Langfuse;
const env = IS_DEV ? 'development' : 'production';


const getTraceAgentName = () => {
  if (GlobalConfig.getConfig().getVersionType() === 'External') {
    return 'codeflicker-agent';
  } else {
    return 'kwaipilot-agent';
  }
}


function getCodeflickerDefaultTraceConfig() {
  const baseUrl = GlobalConfig.getConfig().getKwaiPilotDomain() + 'api/proxy/langfuse';
  
  return {
   "baseUrl": baseUrl,
   "defaultTraceName": "codeflicker-agent",
   // 不传 secretKey 走 publicKey 鉴权，但 LangfuseCore 需要patch处理
   "publicKey": GlobalConfig.getConfig().getJwtToken()
  }
}

interface LangfuseConfig {
  secretKey: string;
  publicKey: string;
  baseUrl: string;
}

// 缓存配置，避免重复请求
let cachedTraceConfig: LangfuseConfig | null = null;
let configFetchPromise: Promise<LangfuseConfig> | null = null;

/**
 * 从API获取trace配置
 */
async function fetchTraceConfig(): Promise<LangfuseConfig> {
  // 如果已有缓存，直接返回
  if (cachedTraceConfig) {
    return cachedTraceConfig;
  }

  // 如果正在请求中，等待请求完成
  if (configFetchPromise) {
    return configFetchPromise;
  }

  // 创建新的请求
  configFetchPromise = (async () => {
    try {
      const httpClient = new Api();
      const response = await httpClient.get<LangfuseConfig>(
        '/eapi/kwaipilot/plugin/v2/config?key=dmo.aidevops.kwaipilot-trace-config',
        {},
        {
          signal: AbortSignal.timeout(3000) // 3秒超时
        }
      );

      cachedTraceConfig = response.data;
      return cachedTraceConfig;
    } catch (error) {
      // 如果API请求失败，回退到环境变量
      console.warn('Failed to fetch trace config from API, falling back to environment variables:', error);
      const fallbackConfig: LangfuseConfig = JSON.parse(process.env.TRACE_CONFIG || '{}');
      cachedTraceConfig = fallbackConfig;
      return fallbackConfig;
    } finally {
      // 清除请求Promise，允许后续重试
      configFetchPromise = null;
    }
  })();

  return configFetchPromise;
}

/**
 * 获取Langfuse实例，支持异步配置加载
 */
async function getLangfuse(): Promise<Langfuse> {
  if (langfuse) {
    return langfuse;
  }

  const traceConfig = await fetchTraceConfig();
  console.log('Trace config from KCONF:', traceConfig);
  langfuse = new Langfuse(traceConfig);
  return langfuse;
}

/**
 * 同步版本的getLangfuse，用于向后兼容
 * 如果配置未加载，会使用环境变量作为回退
 */
const getLangfuseSync = (): Langfuse => {
  if (langfuse) {
    return langfuse;
  }

  let fallbackConfig: LangfuseConfig;
  if (GlobalConfig.getConfig().getVersionType() === 'External') {
    fallbackConfig = getCodeflickerDefaultTraceConfig() as unknown as LangfuseConfig;
  } else {
    fallbackConfig = cachedTraceConfig || JSON.parse(process.env.TRACE_CONFIG || '{}');
  }

  // 如果还没有异步加载配置，使用环境变量作为回退
  console.log('Trace config:', fallbackConfig);
  langfuse = new Langfuse({...fallbackConfig, enabled: true});
  return langfuse;
};


/**
 * 同步版本的getTrace，保持向后兼容
 * 注意：如果配置未从API加载，可能使用环境变量配置
 */
const getTrace = (
  id?: string | null,
  username?: string,
  options?: Partial<Parameters<Langfuse['trace']>[0]>
): LangfuseTraceClient => {
  return getLangfuseSync().trace({
    ...options,
    name: getTraceAgentName(),
    userId: username,
    tags: [env, hostname()],
    id
  });
};

/**
 * 初始化trace配置，建议在应用启动时调用
 * 这样可以预加载配置，减少后续getTrace调用的延迟
 */
async function initTraceConfig(): Promise<void> {
  try {
    await fetchTraceConfig();
    console.log('Trace config initialized successfully');
  } catch (error) {
    console.warn('Failed to initialize trace config:', error);
  }
}

export { getTrace, initTraceConfig };