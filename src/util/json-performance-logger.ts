/**
 * JSON格式性能日志记录器
 * 每行输出一个JSON对象，便于后续解析和上报
 */

import fs from 'fs';
import path from 'path';

export interface JsonPerformanceLog {
  localtime: string;           // 本地时间，格式：2025/09/19 19:05:33.009
  messageType: string;         // 消息类型：newTask, stage, milestone等
  totalDuration: number;       // 总耗时（毫秒）
  stage?: string;              // 阶段名称
  time?: string;               // 时间戳
  messageType2?: string;       // 二级消息类型
  duration?: number;           // 单个阶段耗时
}


const PERF_END_FLAG = 'first-token-received';
export class JsonPerformanceLogger {
  private sessionId: string;
  private chatId: string; // 默认值，后续可更新
  private logFilePath: string;
  private startTime: number;
  private stageCount: number = 0;
  private timers: Map<string, number> = new Map();

  constructor(sessionId: string, chatId: string) {
    this.sessionId = sessionId;
    this.chatId = chatId;
    this.startTime = Date.now();
    
    // 创建日志文件路径
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    this.logFilePath = path.join(logsDir, `perf-agent-${chatId}.log`);
    
    // 记录任务开始
    this.logNewTask();
  }

  /**
   * 格式化本地时间
   */
  private formatLocalTime(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  /**
   * 格式化耗时为秒（保留3位小数）
   */
  private formatDurationSeconds(durationMs: number): string {
    return (durationMs / 1000).toFixed(3);
  }

  /**
   * 写入日志
   */
  private writeLog(logData: JsonPerformanceLog): void {
    // 检查日志文件是否已经存在结束标识
    if (this.hasEndFlag()) {
      console.warn(`日志文件 ${this.logFilePath} 已经有结束标识，跳过写入`);
      return;
    }

    const logLine = JSON.stringify(logData) + '\n';
    fs.appendFileSync(this.logFilePath, logLine, 'utf8');
  }

  /**
   * 检查日志文件是否已经包含结束标识
   */
  private hasEndFlag(): boolean {
    try {
      if (!fs.existsSync(this.logFilePath)) {
        return false;
      }

      const content = fs.readFileSync(this.logFilePath, 'utf8');
      return content.includes(`"stage":"${PERF_END_FLAG}"`);
    } catch (error) {
      console.warn(`检查结束标识时出错: ${error}`);
      return false;
    }
  }

  /**
   * 写入结束标识，标记该聊天的性能日志记录结束
   */
  writeEndFlag(): void {
    const endLog: JsonPerformanceLog = {
      localtime: this.formatLocalTime(),
      stage: PERF_END_FLAG,
      time: new Date().toISOString(),
      messageType: 'system',
      totalDuration: Date.now() - this.startTime,
    };

    // 直接写入结束标识，不检查是否已存在
    const logLine = JSON.stringify(endLog) + '\n';
    fs.appendFileSync(this.logFilePath, logLine, 'utf8');
  }

  /**
   * 记录新任务开始
   */
  logNewTask(): void {
    const totalDuration = Date.now() - this.startTime;
    this.writeLog({
      localtime: this.formatLocalTime(),  
      messageType: 'newTask',
      totalDuration,
    });
  }

  /**
   * 开始记录阶段
   */
  startStage(stageName: string): void {
    this.timers.set(stageName, Date.now());
    this.stageCount++;
    
    const totalDuration = Date.now() - this.startTime;
    this.writeLog({
      localtime: this.formatLocalTime(),
      messageType: 'stage',
      totalDuration,
      stage: stageName,
      time: Date.now().toString(),
      messageType2: 'newTask'
    });
  }

  /**
   * 结束记录阶段
   */
  endStage(stageName: string): number {
    const startTime = this.timers.get(stageName);
    if (!startTime) {
      console.warn(`Stage "${stageName}" was not started`);
      return 0;
    }

    const stageDuration = Date.now() - startTime;
    const totalDuration = Date.now() - this.startTime;
    
    this.writeLog({
      localtime: this.formatLocalTime(),
      messageType: 'stage',
      totalDuration,
      stage: stageName,
      time: Date.now().toString(),
      messageType2: 'newTask',
      duration: stageDuration,
    });

    this.timers.delete(stageName);
    return stageDuration;
  }

  /**
   * 记录里程碑事件
   */
  logMilestone(milestoneName: string, additionalData?: Record<string, any>): void {
    const totalDuration = Date.now() - this.startTime;
    this.writeLog({
      localtime: this.formatLocalTime(),
      messageType: `milestone-${milestoneName}`,
      totalDuration,
      time: Date.now().toString(),
      ...additionalData
    });
  }

  /**
   * 记录第一个字符返回
   */
  logFirstToken(apiDuration: number): void {
    const totalDuration = Date.now() - this.startTime;
    this.writeLog({
      localtime: this.formatLocalTime(),
      messageType: 'firstToken',
      totalDuration,
      stage: 'first_token_received',
      duration: apiDuration,
    });
  }

  /**
   * 记录任务完成
   */
  logTaskComplete(): void {
    const totalDuration = Date.now() - this.startTime;
    this.writeLog({
      localtime: this.formatLocalTime(),
      messageType: 'taskComplete',
      totalDuration,
    });
  }

  /**
   * 记录API请求相关
   */
  logApiRequest(requestType: string, duration?: number): void {
    const totalDuration = Date.now() - this.startTime;
    const logData: JsonPerformanceLog = {
      localtime: this.formatLocalTime(),
      messageType: `api_${requestType}`,
      totalDuration,
    };

    if (duration !== undefined) {
      logData.duration = duration;
    }

    this.writeLog(logData);
  }

  /**
   * 获取当前总耗时
   */
  getTotalDuration(): number {
    return Date.now() - this.startTime;
  }

  /**
   * 获取日志文件路径
   */
  getLogFilePath(): string {
    return this.logFilePath;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.logTaskComplete();
    this.timers.clear();
  }
}

/**
 * JSON性能日志管理器
 */
export class JsonPerformanceLoggerManager {
  private static loggers: Map<string, JsonPerformanceLogger> = new Map();

  static getLogger(sessionId: string , chatId: string) : JsonPerformanceLogger {
    if (!this.loggers.has(sessionId)) {
      this.loggers.set(sessionId, new JsonPerformanceLogger(sessionId,chatId));
    }
    return this.loggers.get(sessionId)!;
  }

  static removeLogger(sessionId: string): void {
    const logger = this.loggers.get(sessionId);
    if (logger) {
      // 在移除之前写入结束标识
      logger.writeEndFlag();
      logger.cleanup();
      this.loggers.delete(sessionId);
    }
  }

  /**
   * 为指定会话写入结束标识
   */
  static writeEndFlag(sessionId: string): void {
    const logger = this.loggers.get(sessionId);
    if (logger) {
      logger.writeEndFlag();
    }
  }

  /**
   * 为所有活跃的日志记录器写入结束标识
   */
  static writeEndFlagForAll(): void {
    for (const logger of this.loggers.values()) {
      logger.writeEndFlag();
    }
  }

  static getAllLoggers(): JsonPerformanceLogger[] {
    return Array.from(this.loggers.values());
  }
}
