import { Weblog } from '@ks/weblogger/lib/log.core';
import { AGENT_VERSION } from './const';
import { GlobalConfig } from './global';

export interface CUSTOM_USER_ACTION {
  key: string;
  type?: string;
  content?: string;
  subType?: string;
  applyId?: string;
  agentMode?: 'jam' | 'duet';
}
export class WebLogger {
  private static webLogger: WebLogger;
  private weblog: Weblog;
  private static username: string = '';
  constructor() {
    const config = GlobalConfig.getConfig();
    WebLogger.username = config?.getUsername();

    const versionType = config.getVersionType();
    this.weblog = new Weblog(
      // 初始化参数配置
      {
        // 对外版 nodejs的包需要请求到外部域名
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        env: versionType === 'External' ? 'https://log-sdk.ksapisrv.com/rest/wd/common/log/collect/misc2': 'production'
      },
      // 埋点基础信息配置
      {
        product_name: 'data_kwaipilot', // 当前埋点产品的 product_name
        user_id: config?.getUsername() || 'KwaiStaff',
        device_id: AGENT_VERSION
      }
    );
  }
  static getWebLogger() {
    if (!WebLogger.webLogger || !WebLogger.username) {
      WebLogger.webLogger = new WebLogger();
    }
    return WebLogger.webLogger;
  }
  collectPV = (page: string, params?: any) => {
    this.weblog?.collect('PV', {
      type: 'enter',
      page: page,
      params: { source: 'node', ...params }
    });
    this.reportUserAction({
      key: page,
      type: 'enter',
      content: page
    });
  };

  collectClick = (action: string, params: any = {}) => {
    this.weblog?.collect('CUSTOM', {
      key: action,
      value: {
        time: Date.now(),
        device: GlobalConfig.getConfig()?.getDevice() || GlobalConfig.getConfig()?.getPlatform(),
        operator: GlobalConfig.getConfig()?.getUsername()
      }
    });
  };
  reportUserAction = async (opt: CUSTOM_USER_ACTION) => {
    if (!opt) return;
    const { key, type, content, subType, applyId, agentMode } = opt;
    console.log('reportUserAction', {
      key: key,
      value: {
        time: Date.now(),
        device: GlobalConfig.getConfig()?.getDevice() || GlobalConfig.getConfig()?.getPlatform(),
        operator: GlobalConfig.getConfig()?.getUsername(),
        type,
        content,
        subType,
        applyId,
        agentMode: agentMode,
        pluginVersion: GlobalConfig.getConfig()?.getPluginVersion(),
        ideVersion: GlobalConfig.getConfig()?.getIdeVersion()
      }
    });
    this.weblog?.sendImmediately('CUSTOM', {
      key: key,
      value: {
        time: Date.now(),
        device: GlobalConfig.getConfig()?.getDevice() || GlobalConfig.getConfig()?.getPlatform(),
        operator: GlobalConfig.getConfig()?.getUsername(),
        type,
        content,
        subType,
        applyId,
        agentMode,
        pluginVersion: GlobalConfig.getConfig()?.getPluginVersion(),
        ideVersion: GlobalConfig.getConfig()?.getIdeVersion()
      }
    });
  };
}
