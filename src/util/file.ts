import fs from 'fs/promises';
import crypto from 'crypto';
import path from 'path';
import { existsSync, readFileSync } from 'fs';
import { GlobalConfig } from './global';
// 获取文件 hash 值
export const getFileMd5 = async (filePath: string) => {
  const content = await fs.readFile(filePath);
  return crypto.createHash('md5').update(content).digest('hex');
};
const APP_NAME = process.env.APP_NAME ?? 'kwaipilot';
export const getKwaipilotIgnoreFiles = (dirPath: string): string[] => {
  const indexIgnorePath = path.join(dirPath, `.${APP_NAME}`, '.indexignore');
  if (existsSync(indexIgnorePath)) {
    const content = readFileSync(indexIgnorePath, 'utf-8');
    return content.split('\n').filter((line) => line.trim() !== '');
  }
  return [];
};
