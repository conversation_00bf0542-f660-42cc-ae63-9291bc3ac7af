import * as path from 'path';
import * as fs from 'fs/promises';
import { Logger } from './log';

const logger = new Logger('git-path');

export async function getRelativePath(absolutePath: string, workspacePath: string) {
  let currentPath = absolutePath;
  let deep = 1;
  const { root } = path.parse(absolutePath);
  while (currentPath !== root) {
    const gitDir = path.join(currentPath, '.git');
    try {
      await fs.access(gitDir);
      return path.relative(currentPath, absolutePath);
    } catch (err) {
      // .git目录不存在，再检查上一级
    }
    currentPath = path.dirname(currentPath);
    if (deep++ > 500) {
      logger.error(`getRelativePath: 目录层级超过500， 大概率是方法存在bug`, {
        absolutePath,
        workspacePath,
        currentPath,
        deep
      });
      break;
    }
  }
  // 如果没有找到.git目录，相对workspace得相对路径
  return path.relative(workspacePath, absolutePath);
}
