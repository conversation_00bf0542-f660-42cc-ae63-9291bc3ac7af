/**
 * 性能数据收集器
 * 用于收集各个阶段的性能数据，方便上报和图表绘制
 */

export interface PerformanceMetric {
  label: string;           // 阶段标签
  duration: number;        // 耗时（毫秒）
  startTime: number;       // 开始时间戳
  endTime: number;         // 结束时间戳
  category: string;        // 分类（如：init, processing, api, etc.）
  level: number;           // 层级（用于多层柱状图）
  parentLabel?: string;    // 父级标签
  metadata?: Record<string, any>; // 额外元数据
}

export interface PerformancePhase {
  phase: string;           // 阶段名称
  totalDuration: number;   // 总耗时
  metrics: PerformanceMetric[]; // 该阶段的所有指标
  percentage: number;      // 占总时间的百分比
}

export interface PerformanceReport {
  sessionId: string;
  taskId: string;
  totalDuration: number;
  phases: PerformancePhase[];
  keyMetrics: {
    firstTokenTime: number;    // 第一个字符返回时间
    apiRequestTime: number;    // API请求耗时
    preProcessingTime: number; // 预处理耗时
  };
  timestamp: number;
}

export class PerformanceCollector {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private startTimes: Map<string, number> = new Map();
  private sessionId: string;
  private taskStartTime: number;
  private onReportCallback?: (report: PerformanceReport) => void;

  constructor(sessionId: string, onReport?: (report: PerformanceReport) => void) {
    this.sessionId = sessionId;
    this.taskStartTime = Date.now();
    this.onReportCallback = onReport;
  }

  /**
   * 开始计时
   */
  start(label: string, category: string = 'default', level: number = 0, parentLabel?: string): void {
    const startTime = Date.now();
    this.startTimes.set(label, startTime);
    
    // 预创建指标对象
    this.metrics.set(label, {
      label,
      duration: 0,
      startTime,
      endTime: 0,
      category,
      level,
      parentLabel,
      metadata: {}
    });
  }

  /**
   * 结束计时
   */
  end(label: string, metadata?: Record<string, any>): number {
    const endTime = Date.now();
    const startTime = this.startTimes.get(label);
    
    if (!startTime) {
      console.warn(`Performance metric "${label}" was not started`);
      return 0;
    }

    const duration = endTime - startTime;
    const metric = this.metrics.get(label);
    
    if (metric) {
      metric.duration = duration;
      metric.endTime = endTime;
      if (metadata) {
        metric.metadata = { ...metric.metadata, ...metadata };
      }
    }

    this.startTimes.delete(label);
    return duration;
  }

  /**
   * 直接记录性能数据
   */
  record(label: string, duration: number, category: string = 'default', level: number = 0, parentLabel?: string, metadata?: Record<string, any>): void {
    const now = Date.now();
    this.metrics.set(label, {
      label,
      duration,
      startTime: now - duration,
      endTime: now,
      category,
      level,
      parentLabel,
      metadata: metadata || {}
    });
  }

  /**
   * 记录关键里程碑
   */
  milestone(label: string, metadata?: Record<string, any>): void {
    const duration = Date.now() - this.taskStartTime;
    this.record(label, duration, 'milestone', 0, undefined, metadata);
  }

  /**
   * 生成性能报告
   */
  generateReport(taskId: string = 'unknown'): PerformanceReport {
    const totalDuration = Date.now() - this.taskStartTime;
    const allMetrics = Array.from(this.metrics.values());
    
    // 按分类分组
    const phaseMap = new Map<string, PerformanceMetric[]>();
    allMetrics.forEach(metric => {
      if (!phaseMap.has(metric.category)) {
        phaseMap.set(metric.category, []);
      }
      phaseMap.get(metric.category)!.push(metric);
    });

    // 生成阶段数据
    const phases: PerformancePhase[] = Array.from(phaseMap.entries()).map(([phase, metrics]) => {
      const phaseTotalDuration = metrics.reduce((sum, m) => sum + m.duration, 0);
      return {
        phase,
        totalDuration: phaseTotalDuration,
        metrics: metrics.sort((a, b) => a.startTime - b.startTime),
        percentage: totalDuration > 0 ? (phaseTotalDuration / totalDuration) * 100 : 0
      };
    });

    // 提取关键指标
    const firstTokenMetric = allMetrics.find(m => m.label === 'first-token-received');
    const apiRequestMetric = allMetrics.find(m => m.category === 'api');
    const preProcessingMetrics = allMetrics.filter(m => m.category === 'preprocessing');
    
    const keyMetrics = {
      firstTokenTime: firstTokenMetric?.duration || 0,
      apiRequestTime: apiRequestMetric?.duration || 0,
      preProcessingTime: preProcessingMetrics.reduce((sum, m) => sum + m.duration, 0)
    };

    const report: PerformanceReport = {
      sessionId: this.sessionId,
      taskId,
      totalDuration,
      phases: phases.sort((a, b) => b.totalDuration - a.totalDuration),
      keyMetrics,
      timestamp: Date.now()
    };

    // 触发回调
    if (this.onReportCallback) {
      this.onReportCallback(report);
    }

    return report;
  }

  /**
   * 获取用于图表的数据结构
   */
  getChartData(): {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      category: string;
      level: number;
    }>;
  } {
    const allMetrics = Array.from(this.metrics.values());
    const labels = allMetrics.map(m => m.label);
    
    // 按层级和分类分组
    const categoryLevelMap = new Map<string, Map<number, PerformanceMetric[]>>();
    
    allMetrics.forEach(metric => {
      if (!categoryLevelMap.has(metric.category)) {
        categoryLevelMap.set(metric.category, new Map());
      }
      const levelMap = categoryLevelMap.get(metric.category)!;
      if (!levelMap.has(metric.level)) {
        levelMap.set(metric.level, []);
      }
      levelMap.get(metric.level)!.push(metric);
    });

    const datasets: Array<{
      label: string;
      data: number[];
      category: string;
      level: number;
    }> = [];

    categoryLevelMap.forEach((levelMap, category) => {
      levelMap.forEach((metrics, level) => {
        datasets.push({
          label: `${category}-L${level}`,
          data: labels.map(label => {
            const metric = metrics.find(m => m.label === label);
            return metric ? metric.duration : 0;
          }),
          category,
          level
        });
      });
    });

    return { labels, datasets };
  }

  /**
   * 清理数据
   */
  clear(): void {
    this.metrics.clear();
    this.startTimes.clear();
    this.taskStartTime = Date.now();
  }

  /**
   * 获取所有指标
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * 导出为JSON
   */
  exportToJSON(): string {
    const report = this.generateReport();
    return JSON.stringify(report, null, 2);
  }

  /**
   * 获取简化的上报数据
   */
  getUploadData(): {
    sessionId: string;
    taskId: string;
    totalDuration: number;
    metrics: Array<{
      label: string;
      duration: number;
      category: string;
      level: number;
    }>;
    keyMetrics: {
      firstTokenTime: number;
      apiRequestTime: number;
      preProcessingTime: number;
    };
  } {
    const report = this.generateReport();
    return {
      sessionId: report.sessionId,
      taskId: report.taskId,
      totalDuration: report.totalDuration,
      metrics: report.phases.flatMap(phase => 
        phase.metrics.map(metric => ({
          label: metric.label,
          duration: metric.duration,
          category: metric.category,
          level: metric.level
        }))
      ),
      keyMetrics: report.keyMetrics
    };
  }
}

// 全局性能收集器管理器
export class PerformanceCollectorManager {
  private static collectors: Map<string, PerformanceCollector> = new Map();

  static getCollector(sessionId: string, onReport?: (report: PerformanceReport) => void): PerformanceCollector {
    if (!this.collectors.has(sessionId)) {
      this.collectors.set(sessionId, new PerformanceCollector(sessionId, onReport));
    }
    return this.collectors.get(sessionId)!;
  }

  static removeCollector(sessionId: string): void {
    this.collectors.delete(sessionId);
  }

  static getAllReports(): PerformanceReport[] {
    return Array.from(this.collectors.values()).map(collector => 
      collector.generateReport()
    );
  }
}
