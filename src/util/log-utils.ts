import * as fs from 'node:fs';

import fastFolderSize from 'fast-folder-size';
import { promisify } from 'util';
const getFolderSize = promisify(fastFolderSize);

// 获取磁盘使用情况
const getDiskUsed = async (folderPath: string) => {
  try {
    if (!fs.existsSync(folderPath)) {
      return 0;
    }
    const size = await getFolderSize(folderPath);
    return size;
  } catch (error) {
    console.error('Error getting disk space:', folderPath, error);
    return null;
  }
};

// 获取文件大小
const getFileSize = async (filePath: string) => {
  try {
    if (!fs.existsSync(filePath)) {
      return 0;
    }
    const stats = await fs.promises.stat(filePath);
    return stats.size;
  } catch (error) {
    console.error('Error getting file size:', filePath, error);
    return null;
  }
};

export { getDiskUsed, getFileSize };
