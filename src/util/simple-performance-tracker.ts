/**
 * 简化的性能追踪器
 * 专门用于方便的埋点上传，提供简洁的API和结构化的数据输出
 */

export interface SimpleMetric {
  label: string;
  duration: number;
  category: string;
  level: number;
  timestamp: number;
}

export interface UploadData {
  sessionId: string;
  taskId: string;
  totalDuration: number;
  firstTokenDuration?: number;
  metrics: SimpleMetric[];
  summary: {
    [category: string]: {
      total: number;
      count: number;
      average: number;
      percentage: number;
    };
  };
}

export class SimplePerformanceTracker {
  private sessionId: string;
  private taskId: string;
  private startTime: number;
  private metrics: SimpleMetric[] = [];
  private timers: Map<string, number> = new Map();
  private firstTokenTime?: number;

  constructor(sessionId: string, taskId: string = 'default') {
    this.sessionId = sessionId;
    this.taskId = taskId;
    this.startTime = Date.now();
  }

  /**
   * 开始计时
   */
  start(label: string, category: string = 'default', level: number = 0): void {
    this.timers.set(label, Date.now());
  }

  /**
   * 结束计时并记录
   */
  end(label: string, category: string = 'default', level: number = 0): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      console.warn(`Timer "${label}" was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.metrics.push({
      label,
      duration,
      category,
      level,
      timestamp: Date.now()
    });

    this.timers.delete(label);
    return duration;
  }

  /**
   * 直接记录耗时
   */
  record(label: string, duration: number, category: string = 'default', level: number = 0): void {
    this.metrics.push({
      label,
      duration,
      category,
      level,
      timestamp: Date.now()
    });
  }

  /**
   * 记录第一个字符返回时间
   */
  markFirstToken(): void {
    if (!this.firstTokenTime) {
      this.firstTokenTime = Date.now() - this.startTime;
    }
  }

  /**
   * 获取总耗时
   */
  getTotalDuration(): number {
    return Date.now() - this.startTime;
  }

  /**
   * 获取用于上传的数据结构
   */
  getUploadData(): UploadData {
    const totalDuration = this.getTotalDuration();
    
    // 计算各分类的汇总信息
    const summary: UploadData['summary'] = {};
    
    this.metrics.forEach(metric => {
      if (!summary[metric.category]) {
        summary[metric.category] = {
          total: 0,
          count: 0,
          average: 0,
          percentage: 0
        };
      }
      
      summary[metric.category].total += metric.duration;
      summary[metric.category].count += 1;
    });

    // 计算平均值和百分比
    Object.keys(summary).forEach(category => {
      const categoryData = summary[category];
      categoryData.average = Math.round(categoryData.total / categoryData.count);
      categoryData.percentage = Math.round((categoryData.total / totalDuration) * 100 * 100) / 100; // 保留2位小数
    });

    return {
      sessionId: this.sessionId,
      taskId: this.taskId,
      totalDuration,
      firstTokenDuration: this.firstTokenTime,
      metrics: [...this.metrics],
      summary
    };
  }

  /**
   * 获取图表数据（多层柱状图）
   */
  getChartData() {
    const categories = [...new Set(this.metrics.map(m => m.category))];
    const labels = this.metrics.map(m => m.label);

    return {
      labels,
      datasets: categories.map(category => ({
        label: category,
        data: labels.map(label => {
          const metric = this.metrics.find(m => m.label === label && m.category === category);
          return metric ? metric.duration : 0;
        }),
        category
      }))
    };
  }

  /**
   * 获取瀑布图数据
   */
  getWaterfallData() {
    const sortedMetrics = [...this.metrics].sort((a, b) => a.timestamp - b.timestamp);
    let cumulative = 0;
    
    return {
      labels: ['开始', ...sortedMetrics.map(m => m.label)],
      data: [0, ...sortedMetrics.map(metric => {
        cumulative += metric.duration;
        return cumulative;
      })]
    };
  }

  /**
   * 导出为JSON字符串
   */
  exportJSON(): string {
    return JSON.stringify(this.getUploadData(), null, 2);
  }

  /**
   * 打印性能报告
   */
  printReport(): void {
    const data = this.getUploadData();
    
    console.log('\n📊 性能报告');
    console.log('='.repeat(40));
    console.log(`会话: ${data.sessionId}`);
    console.log(`任务: ${data.taskId}`);
    console.log(`总耗时: ${data.totalDuration}ms`);
    if (data.firstTokenDuration) {
      console.log(`第一个字符: ${data.firstTokenDuration}ms`);
    }
    
    console.log('\n📈 分类汇总:');
    Object.entries(data.summary)
      .sort(([,a], [,b]) => b.total - a.total)
      .forEach(([category, stats]) => {
        console.log(`  ${category}: ${stats.total}ms (${stats.percentage}%) - 平均: ${stats.average}ms`);
      });
    
    console.log('\n📋 详细指标:');
    data.metrics
      .sort((a, b) => b.duration - a.duration)
      .forEach(metric => {
        const percentage = ((metric.duration / data.totalDuration) * 100).toFixed(1);
        console.log(`  ${'  '.repeat(metric.level)}${metric.label}: ${metric.duration}ms (${percentage}%)`);
      });
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    this.metrics = [];
    this.timers.clear();
    this.firstTokenTime = undefined;
    this.startTime = Date.now();
  }

  /**
   * 获取当前指标数量
   */
  getMetricsCount(): number {
    return this.metrics.length;
  }

  /**
   * 获取指定分类的指标
   */
  getMetricsByCategory(category: string): SimpleMetric[] {
    return this.metrics.filter(m => m.category === category);
  }

  /**
   * 获取最慢的N个操作
   */
  getSlowestOperations(count: number = 5): SimpleMetric[] {
    return [...this.metrics]
      .sort((a, b) => b.duration - a.duration)
      .slice(0, count);
  }
}

/**
 * 全局简单性能追踪器管理器
 */
export class SimplePerformanceManager {
  private static trackers: Map<string, SimplePerformanceTracker> = new Map();

  /**
   * 获取或创建追踪器
   */
  static getTracker(sessionId: string, taskId?: string): SimplePerformanceTracker {
    const key = `${sessionId}-${taskId || 'default'}`;
    if (!this.trackers.has(key)) {
      this.trackers.set(key, new SimplePerformanceTracker(sessionId, taskId));
    }
    return this.trackers.get(key)!;
  }

  /**
   * 移除追踪器
   */
  static removeTracker(sessionId: string, taskId?: string): void {
    const key = `${sessionId}-${taskId || 'default'}`;
    this.trackers.delete(key);
  }

  /**
   * 获取所有追踪器的上传数据
   */
  static getAllUploadData(): UploadData[] {
    return Array.from(this.trackers.values()).map(tracker => tracker.getUploadData());
  }

  /**
   * 清空所有追踪器
   */
  static clearAll(): void {
    this.trackers.clear();
  }
}

/**
 * 便捷的装饰器函数，用于自动追踪函数执行时间
 */
export function trackPerformance(
  tracker: SimplePerformanceTracker,
  label: string,
  category: string = 'default',
  level: number = 0
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      tracker.start(label, category, level);
      try {
        const result = await originalMethod.apply(this, args);
        tracker.end(label, category, level);
        return result;
      } catch (error) {
        tracker.end(label, category, level);
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 便捷函数：创建并返回一个简单的性能追踪器
 */
export function createSimpleTracker(sessionId: string, taskId?: string): SimplePerformanceTracker {
  return new SimplePerformanceTracker(sessionId, taskId);
}
