/**
 * 根据文件名判断文件语言类型
 */

export interface FileTypeInfo {
  language: string;
  category: 'code' | 'markup' | 'data' | 'config' | 'document' | 'image' | 'other';
}

/**
 * 文件扩展名到语言类型的映射
 */
const FILE_TYPE_MAP: Record<string, FileTypeInfo> = {
  // JavaScript/TypeScript
  '.js': { language: 'javascript', category: 'code' },
  '.jsx': { language: 'javascript', category: 'code' },
  '.ts': { language: 'typescript', category: 'code' },
  '.tsx': { language: 'typescript', category: 'code' },
  '.mjs': { language: 'javascript', category: 'code' },
  '.cjs': { language: 'javascript', category: 'code' },

  // Web 技术
  '.html': { language: 'html', category: 'markup' },
  '.htm': { language: 'html', category: 'markup' },
  '.css': { language: 'css', category: 'code' },
  '.scss': { language: 'scss', category: 'code' },
  '.sass': { language: 'sass', category: 'code' },
  '.less': { language: 'less', category: 'code' },

  // Python
  '.py': { language: 'python', category: 'code' },
  '.pyw': { language: 'python', category: 'code' },
  '.pyi': { language: 'python', category: 'code' },

  // Java
  '.java': { language: 'java', category: 'code' },
  '.class': { language: 'java', category: 'code' },

  // C/C++
  '.c': { language: 'c', category: 'code' },
  '.h': { language: 'c', category: 'code' },
  '.cpp': { language: 'cpp', category: 'code' },
  '.cxx': { language: 'cpp', category: 'code' },
  '.cc': { language: 'cpp', category: 'code' },
  '.hpp': { language: 'cpp', category: 'code' },

  // C#
  '.cs': { language: 'csharp', category: 'code' },

  // Go
  '.go': { language: 'go', category: 'code' },

  // Rust
  '.rs': { language: 'rust', category: 'code' },

  // PHP
  '.php': { language: 'php', category: 'code' },
  '.phtml': { language: 'php', category: 'code' },

  // Ruby
  '.rb': { language: 'ruby', category: 'code' },

  // Swift
  '.swift': { language: 'swift', category: 'code' },

  // Kotlin
  '.kt': { language: 'kotlin', category: 'code' },
  '.kts': { language: 'kotlin', category: 'code' },

  // Shell 脚本
  '.sh': { language: 'shell', category: 'code' },
  '.bash': { language: 'bash', category: 'code' },
  '.zsh': { language: 'zsh', category: 'code' },
  '.fish': { language: 'fish', category: 'code' },

  // PowerShell
  '.ps1': { language: 'powershell', category: 'code' },

  // SQL
  '.sql': { language: 'sql', category: 'code' },

  // 标记语言
  '.md': { language: 'markdown', category: 'markup' },
  '.markdown': { language: 'markdown', category: 'markup' },
  '.xml': { language: 'xml', category: 'markup' },
  '.yml': { language: 'yaml', category: 'config' },
  '.yaml': { language: 'yaml', category: 'config' },

  // 数据格式
  '.json': { language: 'json', category: 'data' },
  '.toml': { language: 'toml', category: 'config' },
  '.ini': { language: 'ini', category: 'config' },
  '.cfg': { language: 'ini', category: 'config' },
  '.conf': { language: 'config', category: 'config' },

  // 其他代码文件
  '.r': { language: 'r', category: 'code' },
  '.R': { language: 'r', category: 'code' },
  '.m': { language: 'matlab', category: 'code' },
  '.lua': { language: 'lua', category: 'code' },
  '.pl': { language: 'perl', category: 'code' },
  '.scala': { language: 'scala', category: 'code' },
  '.dart': { language: 'dart', category: 'code' },

  // 配置文件
  '.gitignore': { language: 'gitignore', category: 'config' },
  '.dockerignore': { language: 'dockerignore', category: 'config' },
  '.env': { language: 'dotenv', category: 'config' },

  // 文档
  '.txt': { language: 'text', category: 'document' },
  '.log': { language: 'log', category: 'document' },
  '.pdf': { language: 'pdf', category: 'document' },
  '.doc': { language: 'word', category: 'document' },
  '.docx': { language: 'word', category: 'document' },

  // 图片
  '.png': { language: 'image', category: 'image' },
  '.jpg': { language: 'image', category: 'image' },
  '.jpeg': { language: 'image', category: 'image' },
  '.gif': { language: 'image', category: 'image' },
  '.svg': { language: 'svg', category: 'image' },
  '.webp': { language: 'image', category: 'image' },

  // Windows 特有的文件类型
  '.bat': { language: 'batch', category: 'code' },
  '.cmd': { language: 'batch', category: 'code' },
  '.exe': { language: 'executable', category: 'other' }
};

/**
 * 特殊文件名映射（不基于扩展名）
 */
const SPECIAL_FILES: Record<string, FileTypeInfo> = {
  // Docker files (支持不同大小写变体)
  dockerfile: { language: 'dockerfile', category: 'config' },

  // Makefiles (支持不同大小写变体)
  makefile: { language: 'makefile', category: 'config' },

  // Ruby files
  rakefile: { language: 'ruby', category: 'code' },
  gemfile: { language: 'ruby', category: 'config' },
  podfile: { language: 'ruby', category: 'config' },
  vagrantfile: { language: 'ruby', category: 'config' },

  // JavaScript 配置文件
  'gruntfile.js': { language: 'javascript', category: 'config' },
  'gulpfile.js': { language: 'javascript', category: 'config' },
  'webpack.config.js': { language: 'javascript', category: 'config' },

  // JSON 配置文件
  'package.json': { language: 'json', category: 'config' },
  'tsconfig.json': { language: 'json', category: 'config' },
  'composer.json': { language: 'json', category: 'config' },

  // TOML 配置文件
  'cargo.toml': { language: 'toml', category: 'config' },
  'pyproject.toml': { language: 'toml', category: 'config' },

  // Go 配置文件
  'go.mod': { language: 'gomod', category: 'config' },
  'go.sum': { language: 'gosum', category: 'config' },

  // Python 配置文件
  'requirements.txt': { language: 'text', category: 'config' },

  // 文档文件 (支持不同大小写变体)
  'readme.md': { language: 'markdown', category: 'document' },
  readme: { language: 'text', category: 'document' },
  license: { language: 'text', category: 'document' },
  'changelog.md': { language: 'markdown', category: 'document' },
  changelog: { language: 'text', category: 'document' }
};

/**
 * 根据文件路径获取文件类型信息
 * @param filePath 文件路径
 * @returns 文件类型信息
 */
export function getFileType(filePath: string): FileTypeInfo {
  try {
    if (!filePath) {
      return { language: 'unknown', category: 'other' };
    }

    // 提取文件名和扩展名（处理不同操作系统的路径分隔符）
    const fileName = filePath.toLowerCase().split(/[/\\]/).pop() || '';
    const lastDotIndex = fileName.lastIndexOf('.');
    const extension = lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : '';

    // 首先检查特殊文件名（支持常见的大小写变体）
    if (SPECIAL_FILES[fileName]) {
      return SPECIAL_FILES[fileName];
    }

    // 检查常见的大小写变体
    const commonVariants = [fileName.toUpperCase(), fileName.charAt(0).toUpperCase() + fileName.slice(1)];

    for (const variant of commonVariants) {
      if (SPECIAL_FILES[variant.toLowerCase()]) {
        return SPECIAL_FILES[variant.toLowerCase()];
      }
    }

    // 然后检查扩展名
    if (extension && FILE_TYPE_MAP[extension]) {
      return FILE_TYPE_MAP[extension];
    }

    // 处理没有扩展名的特殊情况
    if (!extension) {
      // 检查是否是可执行文件或脚本
      if (fileName.startsWith('.')) {
        return { language: 'config', category: 'config' };
      }
      return { language: 'text', category: 'document' };
    }

    // 默认返回未知类型
    return { language: 'unknown', category: 'other' };
  } catch (error) {
    // 发生任何错误时返回默认值，确保不影响后续执行
    return { language: 'unknown', category: 'other' };
  }
}

/**
 * 根据文件路径获取语言类型字符串（简化版本）
 * @param filePath 文件路径
 * @returns 语言类型字符串
 */
export function getFileLanguage(filePath: string): string {
  try {
    return getFileType(filePath).language;
  } catch (error) {
    return 'unknown';
  }
}

/**
 * 根据文件路径获取文件分类
 * @param filePath 文件路径
 * @returns 文件分类
 */
export function getFileCategory(filePath: string): string {
  try {
    return getFileType(filePath).category;
  } catch (error) {
    return 'other';
  }
}

/**
 * 检查文件是否为代码文件
 * @param filePath 文件路径
 * @returns 是否为代码文件
 */
export function isCodeFile(filePath: string): boolean {
  try {
    return getFileType(filePath).category === 'code';
  } catch (error) {
    return false;
  }
}

/**
 * 检查文件是否为配置文件
 * @param filePath 文件路径
 * @returns 是否为配置文件
 */
export function isConfigFile(filePath: string): boolean {
  try {
    return getFileType(filePath).category === 'config';
  } catch (error) {
    return false;
  }
}
