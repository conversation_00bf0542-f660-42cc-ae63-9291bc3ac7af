import { PerfInfo } from './log.d';
import * as os from 'os';
import * as process from 'process';
import { getDiskUsed, getFileSize } from './log-utils';
import { getIndexSqlitePath, getKwaipilotGlobalPath, getLanceDbPath } from './paths';
import * as si from 'systeminformation';
import { CUSTOM_USER_ACTION, WebLogger } from './weblogger';
import { GlobalConfig } from './global';
import * as fs from 'fs';
import * as path from 'path';
import { createLogger } from './base-log';
import pidusage from 'pidusage';
import { Api } from '@/http';

// 为助理 Agent 模式特殊提供的日志通道
export const AgentLogger = createLogger('agent', {
  maxSize: '200M'
});
export const AgentDataSetLogger = createLogger('agent-dataset', {
  maxSize: '200M'
});

export const MCPLogger = createLogger('mcp', {
  maxSize: '200M'
});

export const UIPreviewLogger = createLogger('uipreview', {
  maxSize: '200M'
})

const BaseLogger = createLogger('core');

export class Logger extends BaseLogger {

  private _httpClient: Api | null = null;
  
  // 延迟初始化 httpClient，避免循环依赖
  private get httpClient(): Api {
    if (!this._httpClient) {
      this._httpClient = new Api();
    }
    return this._httpClient;
  }

  public perf(log: PerfInfo) {
    // 确保必填字段存在
    if (!log.namespace || !log.subtag) {
      console.error('Missing required fields in performance log');
      return;
    }

    // 截断字符串至最大长度
    const truncateString = (str?: string, maxLength: number = 300): string => {
      if (!str) return '';
      return str.length > maxLength ? str.substring(0, maxLength) : str;
    };

    this.httpClient.reportPerf({
      namespace: log.namespace,
      stage: log.subtag,
      duration: log.millis,
      platform: GlobalConfig?.getConfig()?.getPlatform(),
      version: GlobalConfig?.getConfig()?.getPluginVersion(),
      modelType: truncateString(log.extra3),
      extra1: log.extra4 ? truncateString(log.extra4) : GlobalConfig?.getConfig()?.getRepoPath(),
      extra2: log.extra5 ? truncateString(log.extra5) : GlobalConfig?.getConfig()?.getUsername(),
      extra3: log.extra6 ? truncateString(log.extra6, 500) : ''
    }).catch((error) => {
      console.error('Failed to send performance log:', error);
    });
  }

  public async logProcess(name: string, stage: string) {
    try {
      await Promise.all([
        this.logCpuUsage(name, stage), // 并行获取系统内存和 CPU 负载信息
        this.logMemoryUsage(name, stage),
        this.logCpuInfo(name, stage),
        this.logDotKwaipilotSize(name),
        this.logLanceDbSubdirectorySizes(name, stage) // Add logging of LanceDB subdirectory sizes
      ]);
    } catch (error) {
      this.error('Failed to log process:', error);
    }
  }
  public async getSystemInfo() {
    try {
      const cpu = await si.currentLoad();
      const memory = await si.mem();
      const disk = await si.fsSize();

      return {
        cpu: cpu.currentLoad.toFixed(2),
        memory: ((memory.active / memory.total) * 100).toFixed(2),
        availableMemory: (memory.available / 1024 / 1024).toFixed(2),
        swapUsage: ((memory.swapused / memory.swaptotal) * 100).toFixed(2),
        diskUsage: disk[0].use.toFixed(2)
      };
    } catch (err) {
      console.error('Error fetching system info:', err);
    }
  }

  public async getProcessInfo() {
    try {
      const stats = await pidusage(process.pid);
      const uptime = process.uptime();

      return {
        cpu: stats.cpu.toFixed(2),
        memory: (stats.memory / 1024 / 1024).toFixed(2),
        uptime: uptime.toFixed(2)
      };
    } catch (err) {
      console.error('Error fetching process info:', err);
    }
  }
  public async logCPUInfo(name: string, stage: string) {
    const [systemInfo, processInfo] = await Promise.all([this.getSystemInfo(), this.getProcessInfo()]);
    this.perf({
      namespace: name,
      subtag: stage,
      millis: Number(systemInfo?.cpu),
      extra3: 'cpuUsagePercent',
      extra4: systemInfo?.cpu + '',
      extra6: JSON.stringify({
        systemInfo,
        processInfo
      })
    });
    this.debug(
      `logCPUInfo, ${JSON.stringify({
        systemInfo,
        processInfo
      })}`
    );
    this.reportUserAction({
      key: 'kwapilot-binary-' + name,
      subType: stage,
      content: JSON.stringify({
        systemInfo,
        processInfo
      })
    });
  }
  private async logDotKwaipilotSize(name: string) {
    const [diskSpace, lanceDbSpace, indexSqliteSpace] = await Promise.all([
      getDiskUsed(getKwaipilotGlobalPath()),
      getDiskUsed(getLanceDbPath()),
      getFileSize(getIndexSqlitePath())
    ]);
    this.perf({
      namespace: name,
      subtag: 'DotKwaipilotSize',
      millis: diskSpace || 0,
      extra3: lanceDbSpace?.toString() || '0',
      extra4: diskSpace?.toString() || '0',
      extra6: indexSqliteSpace?.toString() || '0'
    });
    this.reportUserAction({
      key: 'kwapilot-binary-' + name,
      subType: 'DotKwaipilotSize',
      content: JSON.stringify({
        diskSpace,
        lanceDbSpace,
        indexSqliteSpace
      })
    });
  }

  /**
   * Log the size of each subdirectory in the LanceDB directory
   * @param name Namespace for the log
   * @param stage Stage identifier for the log
   */
  public async logLanceDbSubdirectorySizes(name: string, stage: string) {
    try {
      const lanceDbPath = getLanceDbPath();
      const entries = await fs.promises.readdir(lanceDbPath, { withFileTypes: true });

      // Filter only directories
      const directories = entries.filter((entry) => entry.isDirectory());

      // Get size of each directory
      const dirSizes = await Promise.all(
        directories.map(async (dir) => {
          const dirPath = path.join(lanceDbPath, dir.name);
          const size = await getDiskUsed(dirPath);
          return {
            name: dir.name,
            size: size || 0
          };
        })
      );

      // Sort directories by size (largest first)
      dirSizes.sort((a, b) => b.size - a.size);

      // Log each directory size
      this.info(`LanceDB subdirectory sizes (${stage}):`);
      dirSizes.forEach((dir) => {
        this.info(`  - ${dir.name}: ${dir.size}`);
      });

      // Also log as performance data
      dirSizes.forEach((dir) => {
        // Limit to first 10 directories to avoid too many logs
        this.perf({
          namespace: name,
          subtag: 'log_lancedb_size',
          millis: dir.size,
          extra3: dir.name,
          extra6: dir.size.toString()
        });
      });
    } catch (error) {
      this.error('Failed to log LanceDB subdirectory sizes:', error);
    }
  }

  private async logCpuInfo(name: string, stage: string) {
    const cpus = os.cpus();
    const [memStats] = await Promise.all([si.mem()]);

    // 获取 CPU 型号信息
    const cpuModel = cpus[0].model;

    // 将内存大小转换为 GB 并四舍五入
    const memoryGB = Math.round(memStats.total / (1024 * 1024 * 1024));
    // 硬件信息：CPU型号-内存大小，例如：'Apple M2 Pro-32G'
    const hardwareInfo = `${cpuModel}-${memoryGB}G`;
    this.perf({
      namespace: name,
      subtag: stage,
      millis: 0,
      extra3: 'hardwareInfo',
      extra6: hardwareInfo
    });
    this.reportUserAction({
      key: 'hardwareInfo',
      content: hardwareInfo
    });
  }
  private async logCpuUsage(name: string, stage: string) {
    const currentLoad = await si.currentLoad();
    const cpuUsage = currentLoad.currentLoad;
    this.info(`cpuUsage: ${cpuUsage}`);
    this.perf({
      namespace: name,
      subtag: stage,
      millis: Number(cpuUsage),
      extra3: 'cpuUsagePercent',
      extra4: cpuUsage + ''
    });
  }
  // RSS 占可用内存的百分比，反映实际内存压力
  private async logMemoryUsage(name: string, stage: string) {
    // RSS 占可用内存的百分比，反映实际内存压力
    const memStats = await si.mem();
    const memUsage = process.memoryUsage();
    const rssToFreePercent = ((memUsage.rss / memStats.available) * 100).toFixed(2);
    this.info(`rssToFreePercent: ${rssToFreePercent}`);
    this.perf({
      namespace: name,
      subtag: stage,
      millis: Number(rssToFreePercent),
      extra3: 'rssToFreePercent',
      extra4: ((memStats.active / memStats.total) * 100).toFixed(2),
      extra6: memUsage.rss.toString()
    });
  }

  public collectPV(page: string, params?: any) {
    try {
      this.info(`collectPV, ${page}, ${JSON.stringify(params)}`);
      const weblogger = WebLogger.getWebLogger();
      weblogger.collectPV(page, params);
    } catch (error) {
      this.error('Failed to collectPV:', error);
    }
  }

  public collectClick(action: string, params?: any) {
    try {
      this.info(`collectClick, ${action}, ${JSON.stringify(params)}`);
      const weblogger = WebLogger.getWebLogger();
      weblogger.collectClick(action, params);
    } catch (error) {
      this.error('Failed to collectClick:', error);
    }
  }

  public reportUserAction(opt: CUSTOM_USER_ACTION) {
    try {
      this.info(`reportUserAction, ${JSON.stringify(opt)}`);
      const weblogger = WebLogger.getWebLogger();
      weblogger.reportUserAction(opt);
    } catch (error) {
      this.error('Failed to reportUserAction:', error);
    }
  }
}
