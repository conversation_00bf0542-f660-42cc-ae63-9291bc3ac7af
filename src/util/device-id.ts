import * as si from 'systeminformation';
import * as fs from 'fs';
import * as os from 'os';

export interface DeviceIdentifier {
  type: 'mac' | 'serial' | 'docker' | 'uuid' | 'fallback';
  value: string;
  source: string;
}

/**
 * 获取设备唯一标识
 * 优先级：Docker ID > 系统序列号 > 网络接口MAC地址 > 系统UUID > 回退方案
 */
export async function getDeviceIdentifier(): Promise<DeviceIdentifier> {
  try {
    // 1. 检查是否在 Docker 容器中
    const dockerId = await getDockerContainerId();
    if (dockerId) {
      return {
        type: 'docker',
        value: dockerId,
        source: 'Docker Container ID'
      };
    }

    // 2. 尝试获取系统序列号
    const serialNumber = await getSystemSerialNumber();
    if (serialNumber) {
      return {
        type: 'serial',
        value: serialNumber,
        source: 'System Serial Number'
      };
    }

    // 3. 获取主网络接口的 MAC 地址
    const macAddress = await getPrimaryMacAddress();
    if (macAddress) {
      return {
        type: 'mac',
        value: macAddress,
        source: 'Primary Network Interface MAC'
      };
    }

    // 4. 尝试获取系统 UUID
    const systemUuid = await getSystemUuid();
    if (systemUuid) {
      return {
        type: 'uuid',
        value: systemUuid,
        source: 'System UUID'
      };
    }

    // 5. 回退方案：使用主机名 + 平台
    const hostname = os.hostname();
    const platform = os.platform();
    const fallbackId = `${hostname}-${platform}`;

    return {
      type: 'fallback',
      value: fallbackId,
      source: 'Hostname + Platform'
    };
  } catch (error) {
    console.error('Failed to get device identifier:', error);
    // 最终回退方案
    const fallbackId = `unknown-${Date.now()}`;
    return {
      type: 'fallback',
      value: fallbackId,
      source: 'Emergency Fallback'
    };
  }
}

/**
 * 检查并获取 Docker 容器 ID
 */
async function getDockerContainerId(): Promise<string | null> {
  try {
    // 检查 /.dockerenv 文件是否存在（Docker 容器标识）
    if (fs.existsSync('/.dockerenv')) {
      // 尝试从 /proc/self/cgroup 读取容器 ID
      if (fs.existsSync('/proc/self/cgroup')) {
        const cgroupContent = fs.readFileSync('/proc/self/cgroup', 'utf8');
        const dockerMatch = cgroupContent.match(/docker[/-]([a-f0-9]{12,64})/);
        if (dockerMatch) {
          return dockerMatch[1].substring(0, 12); // 返回前12位
        }
      }

      // 尝试从环境变量获取
      if (process.env.CONTAINER_ID) {
        return process.env.CONTAINER_ID;
      }

      // 尝试从 hostname 获取（通常容器 hostname 是容器 ID）
      const hostname = os.hostname();
      if (hostname && hostname.length === 12 && /^[a-f0-9]+$/.test(hostname)) {
        return hostname;
      }
    }

    return null;
  } catch (error) {
    console.warn('Failed to get Docker container ID:', error);
    return null;
  }
}

/**
 * 获取系统序列号
 */
async function getSystemSerialNumber(): Promise<string | null> {
  try {
    const system = await si.system();
    if (system.serial && system.serial !== 'Not Specified' && system.serial !== '') {
      return system.serial;
    }

    // 在 macOS 上尝试获取硬件 UUID
    if (os.platform() === 'darwin') {
      const uuid = await si.uuid();
      if (uuid.hardware && uuid.hardware !== '') {
        return uuid.hardware;
      }
    }

    return null;
  } catch (error) {
    console.warn('Failed to get system serial number:', error);
    return null;
  }
}

/**
 * 获取主网络接口的 MAC 地址
 */
async function getPrimaryMacAddress(): Promise<string | null> {
  try {
    const networkInterfaces = await si.networkInterfaces();

    // 查找活跃的非环回接口
    const activeInterface = networkInterfaces.find(
      (iface) => !iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00' && iface.operstate === 'up'
    );

    if (activeInterface) {
      return activeInterface.mac;
    }

    // 如果没有找到活跃接口，返回第一个有效的 MAC 地址
    const validInterface = networkInterfaces.find(
      (iface) => !iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00'
    );

    return validInterface?.mac || null;
  } catch (error) {
    console.warn('Failed to get MAC address:', error);
    return null;
  }
}

/**
 * 获取系统 UUID
 */
async function getSystemUuid(): Promise<string | null> {
  try {
    const uuid = await si.uuid();
    if (uuid.os && uuid.os !== '') {
      return uuid.os;
    }
    return null;
  } catch (error) {
    console.warn('Failed to get system UUID:', error);
    return null;
  }
}

/**
 * 获取设备标识的详细信息（包含类型和来源）
 */
export async function getDeviceInfo(): Promise<DeviceIdentifier> {
  return await getDeviceIdentifier();
}
