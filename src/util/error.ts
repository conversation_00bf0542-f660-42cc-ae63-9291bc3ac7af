import { serializeError } from 'serialize-error';

/**
 * 错误处理工具类
 * 提供统一的错误序列化和处理功能
 */
export class ErrorUtils {
  /**
   * 序列化错误对象，获取完整的错误信息
   * 包括 message、stack、name 等所有属性
   * 
   * @param error 要序列化的错误对象
   * @returns 序列化后的错误对象
   */
  static serializeError(error: any): any {
    return serializeError(error);
  }

  /**
   * 将错误对象转换为 JSON 字符串
   * 使用 serializeError 确保包含所有错误信息
   * 
   * @param error 要序列化的错误对象
   * @param space 缩进空格数，默认为 2
   * @returns JSON 字符串
   */
  static errorToJson(error: any, space: number = 2): string {
    const serialized = this.serializeError(error);
    return JSON.stringify(serialized, null, space);
  }

  /**
   * 获取错误的详细信息
   * 包括错误消息、堆栈跟踪、错误类型等
   * 
   * @param error 错误对象
   * @returns 错误详细信息对象
   */
  static getErrorDetails(error: any): {
    message: string;
    stack?: string;
    name?: string;
    code?: string | number;
    [key: string]: any;
  } {
    const serialized = this.serializeError(error);
    return {
      message: serialized.message || String(error),
      stack: serialized.stack,
      name: serialized.name,
      code: serialized.code,
      ...serialized
    };
  }

  /**
   * 创建格式化的错误消息
   * 
   * @param error 错误对象
   * @param prefix 错误消息前缀
   * @returns 格式化的错误消息
   */
  static formatErrorMessage(error: any, prefix: string = '错误'): string {
    const details = this.getErrorDetails(error);
    return `${prefix}: ${details.message}`;
  }

  /**
   * 检查是否为 Error 对象
   * 
   * @param error 要检查的对象
   * @returns 是否为 Error 对象
   */
  static isError(error: any): error is Error {
    return error instanceof Error;
  }
} 