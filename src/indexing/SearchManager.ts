import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import { Api } from '@/http';
import { GlobalConfig } from '@/util/global';
import { IndexingSearchBody, SearchParams } from '@/http/types';
import { convertToGitSshFormat } from '@/util/git-utils';
import { RepoStatusTable } from '@/db/sqlite/tables/repo-status';
export class SearchManager {
  private logger = new Logger('SearchManager');
  private httpClient = new Api();

  async searchCloud(params: SearchParams) {
    const { query, topK, targetDirectory, chatHistory, enable_rewrite = false, gitRepo, username } = params;

    const startTime = Date.now();

    const repoStatus = await RepoStatusTable.findByDirPath(params.dirPath);
    // 检查必需参数（使用原有参数名）
    if (!username || !gitRepo) {
      this.logger.error('Missing required parameters: username, gitRepo, or commit or repoStatus');
      throw new Error('Missing required parameters: username, gitRepo, or commit or repoStatus');
    }

    // 处理 gitRepo：如果是绝对路径直接使用，否则转换为SSH格式
    let processedRepoId: string;
    if (gitRepo.startsWith('/')) {
      // 绝对路径直接使用
      processedRepoId = gitRepo;
      this.logger.debug(`Using absolute path as repo_id: ${processedRepoId}`);
    } else {
      // 认为是git url，转换为SSH格式
      const gitSshUrl = convertToGitSshFormat(gitRepo);
      if (gitSshUrl) {
        processedRepoId = gitSshUrl;
        this.logger.debug(`Converted git URL to SSH format: ${gitRepo} -> ${processedRepoId}`);
      } else {
        // 转换失败时使用原始值
        processedRepoId = gitRepo;
        this.logger.warn(`Failed to convert git URL to SSH format, using original: ${gitRepo}`);
      }
    }

    // 调用新的indexing search API，使用参数映射
    const body: IndexingSearchBody = {
      targetDirectory,
      query,
      user_id: username, // username 映射到 user_id
      repo_id: processedRepoId, // 处理后的gitRepo映射到 repo_id
      commit_id: repoStatus?.base_commit_id || '', // 最近的一个 base 基线 commit_id
      enable_rewrite,
      topK,
      chatHistory: Array.isArray(chatHistory) ? chatHistory : []
    };

    this.logger.debug(`indexingSearch body, ${JSON.stringify(body)}`);

    try {
      const data = await this.httpClient.indexingSearch(body);
      this.logger.info(`indexingSearch result, ${JSON.stringify(data)}`);

      // 直接返回rerank_result中的code_context_list
      const result = {
        code_context_list: data.rerank_result.code_context_list
      };

      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'search/indexingSearchSize',
        millis: result.code_context_list.length,
        extra3: String(result.code_context_list.length),
        extra4: targetDirectory.join(','),
        extra6: query
      });

      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'search/search',
        millis: Date.now() - startTime,
        extra3: GlobalConfig.getConfig().getPlatform(),
        extra6: 'success'
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to perform indexing search:', error);
      throw error;
    }
  }

  async search(params: SearchParams) {
    return this.searchCloud(params);
  }
}
