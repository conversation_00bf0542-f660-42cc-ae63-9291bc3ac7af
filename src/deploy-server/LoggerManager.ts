import { Logger } from '@/util/log';

/**
 * 部署服务日志管理器
 */
export class LoggerManager {
  private namespace: string;
  private logger?: Logger;

  constructor(namespace: string) {
    this.namespace = namespace;
  }

  /**
   * 获取日志实例
   */
  public getLogger(): Logger {
    if (!this.logger) {
      this.logger = new Logger(this.namespace);
    }
    return this.logger;
  }

  /**
   * 上报用户行为
   */
  public reportUserAction(data: { key: string; type: string; content: string }): void {
    this.getLogger().reportUserAction(data);
  }
}