import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/protocol/messenger';
import { ServerManager } from './ServerManager';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import { LoggerManager } from './LoggerManager';
import { HttpServerInfo, ApiResponse } from './types';
import getPort, { portNumbers } from 'get-port';

export class DeployServerManager {
  private static instance: DeployServerManager;
  /** 消息通信实例 */
  private messenger?: IMessenger<ToCoreProtocol, FromCoreProtocol>;

  private logger = new LoggerManager(`DeployServer-manager-instance`);

  private readonly PORT_RANGE_START = 18000; // 端口范围开始
  private readonly PORT_RANGE_END = 19000; // 端口范围结束

  private constructor() {
    // Private constructor to prevent instantiation
  }

  /**
   * 设置消息通信实例
   */
  public setMessenger(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    this.messenger = messenger;
  }

  public static getInstance(): DeployServerManager {
    if (!DeployServerManager.instance) {
      DeployServerManager.instance = new DeployServerManager();
    }
    return DeployServerManager.instance;
  }

  /**
   * 启动HTTP服务
   */
  public async createServer(_port?: number): Promise<HttpServerInfo> {
    try {

      let port = _port;
      if (!port) {
        port = await getPort({
          port: portNumbers(this.PORT_RANGE_START, this.PORT_RANGE_END)
        });
      }
      this.logger.getLogger().info('[DeployServerManager] Creating HTTP server', { port });
      const manager = ServerManager.getInstance();
      const serverInfo = await manager.createServer(port);
      const server = manager.getServer(serverInfo.port);
      if (server && this.messenger) {
        server.setMessenger(this.messenger);
      }
      
      this.logger.getLogger().info('[DeployServerManager] HTTP server created successfully:', {
        url: serverInfo.url,
        port: serverInfo.port
      });
      
      return {
        url: serverInfo.url,
        port: serverInfo.port
      };
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Failed to create HTTP server:', error);
      throw new Error(error?.message || 'Create HTTP server error');
    }
  }

  /**
   * 停止HTTP服务
   */
  public async stopServer(port: number): Promise<void> {
    try {
      this.logger.getLogger().info('[DeployServerManager] Stopping server on port:', port);
      
      const manager = ServerManager.getInstance();
      await manager.stopServer(port);
      
      this.logger.getLogger().info('[DeployServerManager] Server stopped successfully on port:', port);
      
    //   // 通知IDE服务已停止
    //   this.notifyServerStopped(port);
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Failed to stop server:', error);
      throw new Error(error?.message || 'Stop server error');
    }
  }

  /**
   * 获取服务状态
   */
  public async getServerStatus(port: number): Promise<HttpServerInfo | null> {
    try {
      const manager = ServerManager.getInstance();
      return await manager.getServerInfo(port);
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Failed to get server status:', error);
      throw new Error(error?.message || 'Get server status error');
    }
  }

  /**
   * 获取所有服务列表
   */
  public async getAllServers(): Promise<HttpServerInfo[]> {
    try {
      const manager = ServerManager.getInstance();
      return await manager.getAllServers();
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Failed to get all servers:', error);
      throw new Error(error?.message || 'Get all servers error');
    }
  }

  /**
   * 处理启动服务请求 - Core调用的主要入口
   */
  public async startServer1(): Promise<ApiResponse> {
    try {
      const serverInfo = await this.createServer();
      
      return {
        success: true,
        message: 'HTTP server started successfully',
        data: serverInfo,
        timestamp: Date.now()
      };
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Handle start server request failed:', error);
      
      return {
        success: false,
        message: error?.message || 'Failed to start HTTP server',
        timestamp: Date.now()
      };
    }
  }

  /**
   * 清理所有资源
   */
  public async dispose(): Promise<void> {
    try {
      this.logger.getLogger().info('[DeployServerManager] Disposing all HTTP servers...');
      
      const manager = ServerManager.getInstance();
      await manager.dispose();
      
      this.logger.getLogger().info('[DeployServerManager] All HTTP servers disposed successfully');
    } catch (error: any) {
      this.logger.getLogger().error('[DeployServerManager] Error during dispose:', error);
      throw error;
    }
  }

  /**
   * 上报用户行为埋点
   */
  public reportUserAction(action: {
    key: string;
    type: string;
    content?: string;
  }): void {
    try {
      this.logger.reportUserAction({
        key: action.key,
        type: action.type,
        content: action.content || ''
      });
    } catch (error) {
      this.logger.getLogger().error('[DeployServerManager] reportUserAction error:', error);
    }
  }
}

// 导出实例获取函数和类型
export { ServerManager };
export {
  DeployServerStatus,
  type HttpServerInfo,
  type ApiResponse
} from './types';

/**
 * 获取DeployServerManager实例
 */
export const getDeployServerManager = () => DeployServerManager.getInstance();

/**
 * 获取ServerManager实例
 */
export const getServerManager = () => ServerManager.getInstance();