/**
 * 部署服务状态枚举
 */
export enum DeployServerStatus {
  STOPPED = 'stopped',
  STARTING = 'starting', 
  RUNNING = 'running',
  ERROR = 'error'
}

/**
 * 简化的HTTP服务信息接口
 */
export interface HttpServerInfo {
  // id: string;
  port: number;
  url: string;
  // status: DeployServerStatus;
  // createdAt: Date;
  // lastActiveAt: Date;
  // isHealthy?: boolean;
}

/**
 * API响应接口
 */
export interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
  timestamp: number;
}

export const VERCEL_CLIENT_ID = 'oac_fQivH60DlcBayzLHLQTocLt6';