/**
 * 代理实例状态枚举
 */
export enum PreviewProxyStatus {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  ERROR = 'error'
}

/**
 * 预览代理消息接口
 */
export interface PreviewProxyMessage {
  data?: any[];
}

/**
 * 预览代理响应接口
 */
export interface PreviewProxyResponse {
  success: boolean;
  message: string;
  data?: any;
  timestamp: number;
}

/**
 * 代理信息接口 - 对外提供的代理状态信息
 */
export interface ProxyInfo {
  port: number;
  targetUrl: string;
  proxyUrl: string;
  status: PreviewProxyStatus;
  createdAt: Date;
  lastActiveAt: Date;
  isHealthy?: boolean;
} 