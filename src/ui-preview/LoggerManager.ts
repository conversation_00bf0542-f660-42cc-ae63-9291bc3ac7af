import { Logger, UIPreviewLogger } from "@/util/log";
import { CUSTOM_USER_ACTION } from "@/util/weblogger";

export class LoggerManager {
    private uiPreviewLogger;
    private performanceLogger: Logger;

    constructor(scope: string) {
        this.uiPreviewLogger = new UIPreviewLogger(scope);
        this.performanceLogger = new Logger(scope || 'uipreview');
    }

    // 日志
    getUIPreviewLogger() {
        return this.uiPreviewLogger;
    }

    // 埋点
    reportUserAction(action: CUSTOM_USER_ACTION) {
        this.performanceLogger.reportUserAction(action)
    }
}