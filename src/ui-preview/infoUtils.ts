import { ISendInfo, ISendLog } from './types';
import { genElementInfo, getElementName } from './elementUtils';
import { IMessenger } from '@/protocol/messenger';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { wrapWithTag, wrapWithTagWithStack, wrapUiConsole, DISPLAYNAME_CONSOLE_ERROR } from './prompt';

interface IFocusInfo {
  filePath?: string;
  start?: {
    line?: number;
    column?: number;
  };
}
/**
 * 格式化元素信息
 */
export function fmtSendInfo(data: ISendInfo): {
  type: 'element';
  displayName: string;
  data: string;
  desc?: string;
  focusInfo?: IFocusInfo;
} | null {
  if (data.type !== 'element') {
    return null;
  }
  const elementInfoStr = genElementInfo(data);
  if (!elementInfoStr) {
    return null;
  }
  const displayName = getElementName(data);
  let focusInfo: IFocusInfo | undefined = undefined;
  if (data.codeInfo) {
    focusInfo = {
      filePath: data.codeInfo?.path,
      start: data.codeInfo.start
    }
  }
  return {
    type: 'element',
    data: elementInfoStr,
    displayName: `dom-element: ${displayName}`,
    desc: displayName,
    focusInfo: focusInfo
  };
}

/**
 * 发送日志信息（error/warn/log）
 */
export function sendLog(
  messenger: IMessenger<ToCoreProtocol, FromCoreProtocol> | undefined,
  data: ISendLog[]
): void {
  if (!data || !data.length) return;

  // 检查是否有 tabId 信息
  const tabIds = data
    .filter(item => item.type === 'error' && 'tabId' in item && item.tabId)
    .map(item => (item as any).tabId);
  const uniqueTabId = tabIds.length > 0 ? tabIds[0] : null;

  const messages = data.map(
    (item) => {
      const tag = `${item.type || 'log'}`;
      if (item.type === 'error') {
        return wrapWithTagWithStack(tag, item.message, item.stack);
      }
      return wrapWithTag(tag, item.message);
    }
  ).join('\n');

  // 如果有 tabId，添加到消息中
  let finalMessages = messages;
  if (uniqueTabId) {
    finalMessages = `<tab_id description="browser tab id">${uniqueTabId}</tab_id>\n${messages}`;
  }

  const desc = data.map(item => item.message).filter(Boolean);
  const dataMessage = wrapUiConsole(finalMessages);
  messenger?.send('uiPreview/info', {
    type: 'error',
    displayName: DISPLAYNAME_CONSOLE_ERROR(desc.length),
    data: dataMessage,
    desc,
  });
}