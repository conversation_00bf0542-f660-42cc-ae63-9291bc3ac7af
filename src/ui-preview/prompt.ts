// 统一维护 UI Preview 相关的提示词和描述

// 元素信息相关
export const UI_ELEMENT_DESC = 'Interactive element chosen by the user on this webpage. After code modifications, you can use browser_action tool to retest and validate the changes';
export const FILE_LIST_DESC = "File dependency chain: List of files in the component's external node hierarchy";

// 控制台日志相关
export const UI_CONSOLE_DESC = "This section contains error and warning messages (with optional stack traces) captured from the browser's console during UI preview.";

// 其他常用 displayName
export const DISPLAYNAME_SCREENSHOT = 'screenshot';
export const DISPLAYNAME_CONSOLE_ERROR = (count: number) => `Console Error ${count > 0 ? `(${count})` : ''}`;

// XML 标签模板
export const wrapWithTag = (tag: string, content: string) => `<${tag}>${content}</${tag}>`;
export const wrapWithTagWithStack = (tag: string, message: string, stack?: string) =>
    `<${tag}>${message}${stack ? `\n${stack}` : ''}</${tag}>`;

export const wrapUiElement = (content: string) => `<ui_element desc="${UI_ELEMENT_DESC}">\n${content}</ui_element>`;
export const wrapFileList = (content: string) => `<file_list desc="${FILE_LIST_DESC}">${content}</file_list>`;
export const wrapUiConsole = (content: string) => `<ui_console desc="${UI_CONSOLE_DESC}">${content}</ui_console>`;