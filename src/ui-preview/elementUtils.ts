// 元素相关工具函数
import { ISendElement } from './types';
import { wrapUiElement, wrapFileList } from './prompt';

/**
 * 生成元素信息
 * @param data 
 * @returns 
 */
export function genElementInfo(data: ISendElement): string | null {
    if (!hasValue(data.element)) return null;
    const element = data.element!;
    let result = '';
    result += `<id>${data.id || 'unknown'}</id>\n`;
    result += `<tag>${element.tag || 'unknown'}</tag>\n`;
    result += `<class>${element.class || 'unknown'}</class>\n`;

    if (hasValue(data.tabId)) {
        result += `<tab_id desciption="browser tab id">${data.tabId}</tab_id>\n`
    }

    if (hasValue(data.selector)) {
        result += `<selector>${data.selector}</selector>\n`;
    }
    const attributesStr = safeStringify(element.attributes);
    if (attributesStr) {
        result += `<attributes>${attributesStr}</attributes>\n`;
    }
    const computedStyleStr = safeStringify(element.computedStyle);
    if (computedStyleStr) {
        result += `<computed_style>${computedStyleStr}</computed_style>\n`;
    }

    if (hasValue(data.ariaSnapshot)) {
        result += `<aria_snapshot>${data.ariaSnapshot}</aria_snapshot>\n`;
    } else if (hasValue(data.textContent)) {
        result += `<text_content>${data.textContent}</text_content>\n`;
    }

    if (hasValue(data.parentElement)) {
        const parentElementStr = safeStringify(data.parentElement);
        if (parentElementStr) {
            result += `<parent_element>${parentElementStr}</parent_element>\n`;
        }
    }

    if (hasValue(data.codeInfo)) {
        const codeInfoStr = safeStringify(data.codeInfo);
        if (codeInfoStr) {
            result += `<code_info>${codeInfoStr}</code_info>\n`;
        }
    }

    if (hasValue(data.fileList)) {
        const fileListStr = (data.fileList?.join?.("\n") || '').trim();
        if (fileListStr) {
            result += wrapFileList(fileListStr) + '\n';
        }
    }
    if (result) {
        return wrapUiElement(result);
    }
    return null;
}

/**
 * 获取元素名称
 * @param data 
 * @returns 
 */
export function getElementName(data: ISendElement): string {
    let tag = (data.element?.tag || '').toLowerCase();

    // 如果元素有id，则返回 tag#id 格式
    if (data.element?.id) {
        tag += `#${data.element.id}`;
    }

    // 如果元素有class，则返回 tag.class 格式
    if (data.element?.class) {
        tag += `.${data.element.class.split(' ').join('.')}`;
    }

    // 只有标签时，直接返回标签
    return tag || 'unknown element';
}

// 工具函数：判断值是否有效（非空、非undefined、非null）
export function hasValue(value: any): boolean {
    if (value === null || value === undefined || value === '') return false;
    if (typeof value === 'object') {
        if (Array.isArray(value)) return value.length > 0;
        return Object.keys(value).length > 0;
    }
    if (typeof value === 'string') {
        return value.trim() !== '';
    }
    return true;
}

// 工具函数：安全的JSON序列化，去除空对象
export function safeStringify(obj: any): string {
    if (!hasValue(obj)) return '';
    if (typeof obj === 'object' && !Array.isArray(obj)) {
        const filtered = Object.fromEntries(Object.entries(obj).filter(([_, v]) => hasValue(v)));
        return Object.keys(filtered).length > 0 ? JSON.stringify(filtered) : '';
    }
    return JSON.stringify(obj);
} 