import { z, ZodSchema, ZodTypeAny } from 'zod';
import { BrowserToolRegistry } from './tool-registry';

interface JSONSchema {
    type?: string;
    properties?: Record<string, any>;
    required?: string[];
    description?: string;
    enum?: string[];
    items?: JSONSchema;
    anyOf?: JSONSchema[];
    oneOf?: JSONSchema[];
    allOf?: JSONSchema[];
    additionalProperties?: boolean | JSONSchema;
    not?: JSONSchema;
    [key: string]: any;
}

/**
 * Dynamic Schema Generator
 * Dynamically generates unified parameter schemas based on registered tools
 */
export class DynamicSchemaGenerator {
    constructor(private registry: BrowserToolRegistry) { }

    /**
     * 生成统一的工具Schema (Zod格式)
     */
    generateUnifiedSchema(): ZodSchema {
        const availableActions = this.registry.getAvailableActions();

        if (availableActions.length === 0) {
            return z.object({});
        }

        // 动态收集所有工具的参数
        const allParameters = this.collectAllParameters();

        return z.object({
            // tabId 作为必填参数
            tabId: z.string().describe('Unique identifier for the browser tab instance. Required for all operations to ensure correct tab targeting.').optional(),

            // 动态生成的action枚举
            action: z.enum(availableActions as [string, ...string[]])
                .optional()
                .describe(this.generateActionDescription()),

            // 动态生成的参数
            ...allParameters,

            // 批处理参数
            batch: z.array(z.object({
                action: z.string().describe('Action type'),
                params: z.record(z.any()).describe('Action parameters'),
                continueOnError: z.boolean().optional().describe('Whether to continue execution on error')
            })).optional().describe('Batch operation list, mutually exclusive with action parameter')
        }).refine(
            (data) => data.action || data.batch,
            {
                message: "Must specify either action or batch parameter",
                path: ["action"]
            }
        );
    }

    /**
     * 生成统一的工具Schema (JSON Schema格式)
     */
    generateUnifiedJSONSchema(): JSONSchema {
        const availableActions = this.registry.getAvailableActions();

        if (availableActions.length === 0) {
            return {
                type: "object",
                properties: {},
                additionalProperties: false
            };
        }

        // 动态收集所有工具的参数
        const allParameters = this.collectAllParametersAsJSONSchema();

        const properties: Record<string, any> = {
            tabId: {
                type: "string",
                description: "Unique identifier for the browser tab instance. Required for all operations to ensure correct tab targeting."
            },
            action: {
                type: "string",
                enum: availableActions,
                description: this.generateActionDescription()
            },
            batch: {
                type: "array",
                description: "Batch operation list, mutually exclusive with action parameter",
                items: {
                    type: "object",
                    properties: {
                        action: {
                            type: "string",
                            description: "Action type"
                        },
                        params: {
                            type: "object",
                            description: "Action parameters",
                            additionalProperties: true
                        },
                        continueOnError: {
                            type: "boolean",
                            description: "Whether to continue execution on error"
                        }
                    },
                    required: ["action", "params"],
                    additionalProperties: false
                }
            },
            ...allParameters
        };

        return {
            type: "object",
            properties,
            anyOf: [
                {
                    type: "object",
                    required: ["action"],
                    not: {
                        type: "object",
                        required: ["batch"]
                    }
                },
                {
                    type: "object",
                    required: ["batch"],
                    not: {
                        type: "object",
                        required: ["action"]
                    }
                }
            ],
            required: ['tabId'],
            additionalProperties: false
        };
    }

    /**
     * 收集所有注册工具的参数
     */
    private collectAllParameters(): Record<string, ZodTypeAny> {
        const allTools = this.registry.getAllTools();
        const parameterMap = new Map<string, {
            schema: ZodTypeAny;
            descriptions: Map<string, string>;
            tools: string[];
            baseSchema: ZodTypeAny;
        }>();

        // 第一步：收集所有参数信息
        for (const tool of allTools) {
            if (tool.parameters && (tool.parameters as any)._def) {
                const def = (tool.parameters as any)._def;
                let shape: Record<string, any> = {};

                // 处理不同类型的 Zod schema
                if (def.typeName === 'ZodObject' && def.shape) {
                    if (typeof def.shape === 'function') {
                        shape = def.shape();
                    } else {
                        shape = def.shape;
                    }
                }

                for (const [paramName, paramSchema] of Object.entries(shape)) {
                    const schema = paramSchema as ZodTypeAny;
                    const description = (schema as any)._def?.description || `Parameter ${paramName}`;

                    if (!parameterMap.has(paramName)) {
                        parameterMap.set(paramName, {
                            schema: schema.optional(),
                            descriptions: new Map([[description, tool.name]]),
                            tools: [tool.name],
                            baseSchema: schema
                        });
                    } else {
                        const existing = parameterMap.get(paramName)!;
                        existing.tools.push(tool.name);

                        // 记录不同的描述
                        if (!existing.descriptions.has(description)) {
                            existing.descriptions.set(description, tool.name);
                        } else {
                            // 如果描述相同，添加到工具列表
                            const existingTool = existing.descriptions.get(description)!;
                            existing.descriptions.set(description, `${existingTool}, ${tool.name}`);
                        }
                    }
                }
            }
        }

        // 第二步：生成最终的参数描述
        const result: Record<string, ZodTypeAny> = {};
        const entries = Array.from(parameterMap.entries());

        for (const [paramName, paramInfo] of entries) {
            let finalDescription: string;

            if (paramInfo.descriptions.size === 1) {
                // 只有一种描述，直接使用
                const [description] = paramInfo.descriptions.keys();
                const toolList = paramInfo.tools.join(', ');
                finalDescription = `${description} (used by: ${toolList})`;
            } else {
                // 多种描述，按action分组
                const descriptionParts: string[] = [];
                const entries = Array.from(paramInfo.descriptions.entries());

                for (const [description, tools] of entries) {
                    descriptionParts.push(`${description} (for ${tools})`);
                }

                finalDescription = `Parameter with different uses:\n${descriptionParts.join('\n')}`;
            }

            // 创建新的schema并设置描述
            result[paramName] = paramInfo.baseSchema.optional().describe(finalDescription);
        }

        return result;
    }

    /**
     * 收集所有注册工具的参数 (JSON Schema格式)
     */
    private collectAllParametersAsJSONSchema(): Record<string, JSONSchema> {
        const allTools = this.registry.getAllTools();
        const parameterMap = new Map<string, {
            schema: JSONSchema;
            descriptions: Map<string, string>;
            tools: string[];
            baseSchema: ZodTypeAny;
        }>();

        // 第一步：收集所有参数信息
        for (const tool of allTools) {
            if (tool.parameters && (tool.parameters as any)._def) {
                const def = (tool.parameters as any)._def;
                let shape: Record<string, any> = {};

                // 处理不同类型的 Zod schema
                if (def.typeName === 'ZodObject' && def.shape) {
                    if (typeof def.shape === 'function') {
                        shape = def.shape();
                    } else {
                        shape = def.shape;
                    }
                }

                for (const [paramName, paramSchema] of Object.entries(shape)) {
                    const schema = paramSchema as ZodTypeAny;
                    const description = (schema as any)._def?.description || `Parameter ${paramName}`;

                    // 使用新的类型解析逻辑
                    const resolvedSchema = this.resolveZodTypeChain(schema);
                    const jsonSchema = this.zodToJSONSchema(resolvedSchema);

                    if (!parameterMap.has(paramName)) {
                        parameterMap.set(paramName, {
                            schema: jsonSchema,
                            descriptions: new Map([[description, tool.name]]),
                            tools: [tool.name],
                            baseSchema: schema
                        });
                    } else {
                        const existing = parameterMap.get(paramName)!;
                        existing.tools.push(tool.name);

                        // 记录不同的描述
                        if (!existing.descriptions.has(description)) {
                            existing.descriptions.set(description, tool.name);
                        } else {
                            // 如果描述相同，添加到工具列表
                            const existingTool = existing.descriptions.get(description)!;
                            existing.descriptions.set(description, `${existingTool}, ${tool.name}`);
                        }
                    }
                }
            }
        }

        // 第二步：生成最终的参数描述
        const result: Record<string, JSONSchema> = {};
        const entries = Array.from(parameterMap.entries());

        for (const [paramName, paramInfo] of entries) {
            let finalDescription: string;

            if (paramInfo.descriptions.size === 1) {
                // 只有一种描述，直接使用
                const [description] = paramInfo.descriptions.keys();
                const toolList = paramInfo.tools.join(', ');
                finalDescription = `${description} (used by: ${toolList})`;
            } else {
                // 多种描述，按action分组
                const descriptionParts: string[] = [];
                const entries = Array.from(paramInfo.descriptions.entries());

                for (const [description, tools] of entries) {
                    descriptionParts.push(`${description} (for ${tools})`);
                }

                finalDescription = `Parameter with different uses:\n${descriptionParts.join('\n')}`;
            }

            // 设置描述，确保类型信息正确
            result[paramName] = {
                ...paramInfo.schema,
                description: finalDescription
            };
        }

        return result;
    }

    /**
     * 将 Zod Schema 转换为 JSON Schema
     */
    private zodToJSONSchema(zodSchema: ZodTypeAny): JSONSchema {
        const def = (zodSchema as any)._def;

        switch (def.typeName) {
            case 'ZodString':
                return { type: "string" };
            case 'ZodNumber':
                return { type: "number" };
            case 'ZodBoolean':
                return { type: "boolean" };
            case 'ZodArray':
                return {
                    type: "array",
                    items: this.zodToJSONSchema(def.type)
                };
            case 'ZodObject':
                const properties: Record<string, JSONSchema> = {};
                const required: string[] = [];
                let shape: Record<string, any> = {};

                if (typeof def.shape === 'function') {
                    shape = def.shape();
                } else {
                    shape = def.shape;
                }

                for (const [key, value] of Object.entries(shape)) {
                    properties[key] = this.zodToJSONSchema(value as ZodTypeAny);
                    // 检查是否为必填字段
                    const valueDef = (value as any)._def;
                    if (valueDef.typeName !== 'ZodOptional') {
                        required.push(key);
                    }
                }

                return {
                    type: "object",
                    properties,
                    ...(required.length > 0 ? { required } : {}),
                    additionalProperties: false
                };
            case 'ZodOptional':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodEnum':
                return {
                    type: "string",
                    enum: def.values
                };
            case 'ZodUnion':
                return {
                    anyOf: def.options.map((option: ZodTypeAny) => this.zodToJSONSchema(option))
                };
            case 'ZodRecord':
                return {
                    type: "object",
                    additionalProperties: def.valueType ? this.zodToJSONSchema(def.valueType) : true
                };
            case 'ZodAny':
                return {};
            case 'ZodDefault':
                // 处理带有默认值的类型，递归处理内部类型
                // 确保正确处理嵌套的 ZodDefault 类型
                let innerType = def.innerType;
                while (innerType && (innerType as any)._def && (innerType as any)._def.typeName === 'ZodDefault') {
                    innerType = (innerType as any)._def.innerType;
                }
                return this.zodToJSONSchema(innerType);
            case 'ZodNullable':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodUndefined':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodNull':
                return { type: "null" };
            case 'ZodEffects':
                return this.zodToJSONSchema(def.schema);
            case 'ZodPipeline':
                return this.zodToJSONSchema(def.out);
            case 'ZodBranded':
                return this.zodToJSONSchema(def.type);
            case 'ZodCatch':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodTransform':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodLazy':
                return this.zodToJSONSchema(def.getter());
            case 'ZodPromise':
                return this.zodToJSONSchema(def.type);
            case 'ZodReadonly':
                return this.zodToJSONSchema(def.innerType);
            case 'ZodDate':
                return { type: "string", format: "date-time" };
            case 'ZodBigInt':
                return { type: "string" };
            case 'ZodSymbol':
                return { type: "string" };
            case 'ZodFunction':
                return { type: "string" };
            case 'ZodVoid':
                return { type: "null" };
            case 'ZodNever':
                return { type: "null" };
            case 'ZodUnknown':
                return {};
            case 'ZodLiteral':
                return { type: typeof def.value, const: def.value };
            case 'ZodNativeEnum':
                return {
                    type: "string",
                    enum: Object.values(def.values)
                };
            case 'ZodIntersection':
                return {
                    allOf: def.options.map((option: ZodTypeAny) => this.zodToJSONSchema(option))
                };
            case 'ZodDiscriminatedUnion':
                return {
                    oneOf: def.options.map((option: ZodTypeAny) => this.zodToJSONSchema(option))
                };
            case 'ZodTuple':
                return {
                    type: "array",
                    items: def.items.map((item: ZodTypeAny) => this.zodToJSONSchema(item)),
                    minItems: def.items.length,
                    maxItems: def.items.length
                };
            case 'ZodMap':
                return {
                    type: "object",
                    additionalProperties: this.zodToJSONSchema(def.valueType)
                };
            case 'ZodSet':
                return {
                    type: "array",
                    items: this.zodToJSONSchema(def.valueType),
                    uniqueItems: true
                };
            default:
                // 默认情况，返回一个通用的schema
                console.warn(`Unknown Zod type: ${def.typeName}, falling back to string`);
                return { type: "string" };
        }
    }

    /**
     * 递归解析 Zod 类型链，获取最终的基础类型
     */
    private resolveZodTypeChain(zodSchema: ZodTypeAny): ZodTypeAny {
        const def = (zodSchema as any)._def;

        switch (def.typeName) {
            case 'ZodOptional':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodDefault':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodNullable':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodUndefined':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodNull':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodEffects':
                return this.resolveZodTypeChain(def.schema);
            case 'ZodPipeline':
                return this.resolveZodTypeChain(def.out);
            case 'ZodBranded':
                return this.resolveZodTypeChain(def.type);
            case 'ZodCatch':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodTransform':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodLazy':
                return this.resolveZodTypeChain(def.getter());
            case 'ZodPromise':
                return this.resolveZodTypeChain(def.type);
            case 'ZodReadonly':
                return this.resolveZodTypeChain(def.innerType);
            case 'ZodDate':
            case 'ZodBigInt':
            case 'ZodSymbol':
            case 'ZodFunction':
            case 'ZodVoid':
            case 'ZodNever':
            case 'ZodUnknown':
            case 'ZodLiteral':
            case 'ZodNativeEnum':
            case 'ZodIntersection':
            case 'ZodDiscriminatedUnion':
            case 'ZodTuple':
            case 'ZodMap':
            case 'ZodSet':
            case 'ZodString':
            case 'ZodNumber':
            case 'ZodBoolean':
            case 'ZodArray':
            case 'ZodObject':
            case 'ZodEnum':
            case 'ZodUnion':
            case 'ZodRecord':
            case 'ZodAny':
                // 这些是基础类型，直接返回
                return zodSchema;
            default:
                // 对于未知类型，尝试递归解析 innerType（如果存在）
                if (def.innerType) {
                    return this.resolveZodTypeChain(def.innerType);
                }
                // 如果无法解析，返回原始 schema
                return zodSchema;
        }
    }

    /**
     * 生成操作描述
     */
    private generateActionDescription(): string {
        const categories = this.registry.getToolsByCategory();
        const descriptions = [];

        const entries = Array.from(categories.entries());
        for (const [category, tools] of entries) {
            descriptions.push(`${category}: ${tools.map(t => t.name).join(', ')}`);
        }

        return `Available action types:\n${descriptions.join('\n')}`;
    }
}