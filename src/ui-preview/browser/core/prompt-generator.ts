import { BrowserToolRegistry } from './tool-registry';

/**
 * Browser tool prompt generator
 * Dynamically generates complete usage instructions based on registered tools
 */
export class BrowserToolPromptGenerator {
    constructor(private registry: BrowserToolRegistry) { }

    /**
     * Generate complete tool description
     */
    generateToolDescription(): string {
        return `# Browser Automation Tool

## PRIORITY RECOMMENDATION
**For ANY web-related tasks, frontend development, or browser automation - USE THIS TOOL FIRST!**

### Smart Testing Workflow
- **Complex Operations**: Use "test_case" tool first to generate test scenarios and execution plans
- **Simple Operations**: Use "test_case" tool first to generate execution plans
- **Always inform users** about key steps and execution plans and expected outcomes before execution

## Core Advantages
- **Real-time visual feedback** - See results instantly, catch errors immediately
- **Actual browser environment** - No simulation, real rendering and behavior
- **Cross-browser compatibility** - Chrome, Firefox, Safari, Edge support
- **Comprehensive logging** - Detailed execution logs and error reporting

## Primary Use Cases

### Testing & Validation
- **Automated Testing**: Form filling, button clicks, user journey validation
- **Cross-Browser Testing**: Ensure compatibility across different browsers
- **Visual Testing**: Screenshot capture and visual comparison
- **Performance Testing**: Network monitoring and response time analysis

### Development Support
- **Frontend Development**: Instant feedback on UI changes and functionality
- **Element Operations**: Locate, interact with, and manipulate DOM elements
- **Debug Support**: Console logs, error tracking, state inspection and network monitoring

### Data & Analysis
- **Web Scraping**: Extract content, attributes, and structured data
- **Content Analysis**: Monitor page changes and extract information
- **User Experience**: Simulate real user workflows and interactions

### Design & UI
- **Design Validation**: Compare mockups with actual implementation
- **Responsive Testing**: Test across different screen sizes and devices
- **Accessibility Testing**: Validate ARIA labels and keyboard navigation

## Available Operations

${this.generateCategoryDescriptions()}

## Usage Guide

### Single Operation
\`\`\`json
{
  "action": "operation_name",
  "param1": "value1",
  "param2": "value2"
}
\`\`\`

### Batch Operations
\`\`\`json
{
  "batch": [
    {"action": "navigate", "url": "https://example.com"},
    {"action": "click", "selector": "button.submit"},
    {"action": "screenshot", "fullPage": true}
  ]
}
\`\`\`

### Key Parameters
- **tabId**: Required for tab operations - identifies which browser tab to operate on
- **ref**: Required for element operations - unique reference to DOM elements
- **selector**: CSS selector or XPath to locate elements
- **url**: Target URL for navigation operations

## Parameter Reference
${this.generateParameterDescriptions()}

## Batch Processing
- **Sequential execution**: Operations run in order
- **Last result returned**: Only the final operation's result is returned
- **Fail-fast behavior**: Stops on first error (unless continueOnError is enabled)
- **Complete logging**: Detailed execution logs for all operations

## Pagination & Filtering
- **page**: Page number (starts from 1)
- **pageSize**: Items per page (max 100)
- **sortOrder**: Sort direction (desc for newest first)
- **filters**: Apply specific filters for console logs, network requests, etc.

## State Management
- **Auto-cleanup**: Historical data cleared on page navigation
- **Manual cleanup**: Explicit cleanup operations available
- **Lifecycle tracking**: Complete page state management

## Usage Examples
${this.generateExamples()}`;
    }

    /**
     * Generate category descriptions
     */
    private generateCategoryDescriptions(): string {
        const categories = this.registry.getToolsByCategory();
        const sections = [];

        const categoryEntries = Array.from(categories.entries());
        for (const [category, tools] of categoryEntries) {
            const toolList = tools.map(t => `\`${t.name}\``).join(', ');
            sections.push(`### ${category}`);
            sections.push(`${toolList}`);
            sections.push('');
        }

        return sections.join('\n');
    }

    /**
     * Generate parameter descriptions
     */
    private generateParameterDescriptions(): string {
        const paramGroups = this.extractParameterGroups();

        const sections = [];
        for (const [group, params] of Object.entries(paramGroups)) {
            if (params.length > 0) {
                sections.push(`### ${group}`);
                sections.push(params.map(p => `- ${p}`).join('\n'));
                sections.push('');
            }
        }

        return sections.join('\n');
    }

    /**
     * Extract parameter groups from registered tools
     */
    private extractParameterGroups(): Record<string, string[]> {
        const paramGroups: Record<string, Set<string>> = {
            'Basic Parameters': new Set(),
            'Pagination Parameters': new Set(),
            'Filter Parameters': new Set(),
            'Option Parameters': new Set(),
            'Other Parameters': new Set()
        };

        // 遍历所有注册的工具
        const allTools = this.registry.getAllTools();
        const parameterUsage = new Map<string, { description: string; tools: string[] }>();

        for (const tool of allTools) {
            if (tool.parameters && (tool.parameters as any)._def && (tool.parameters as any)._def.shape) {
                const shape = (tool.parameters as any)._def.shape();

                for (const [paramName, paramSchema] of Object.entries(shape)) {
                    const schema = paramSchema as any;
                    const description = schema._def?.description || `Parameter ${paramName}`;

                    if (!parameterUsage.has(paramName)) {
                        parameterUsage.set(paramName, {
                            description,
                            tools: [tool.name]
                        });
                    } else {
                        const existing = parameterUsage.get(paramName)!;
                        if (!existing.tools.includes(tool.name)) {
                            existing.tools.push(tool.name);
                        }
                    }
                }
            }
        }

        // Generate grouping information based on parameter usage
        const paramEntries = Array.from(parameterUsage.entries());
        for (const [paramName, paramInfo] of paramEntries) {
            const toolList = paramInfo.tools.join(', ');
            const paramInfoText = `${paramName}: ${paramInfo.description} (used by: ${toolList})`;

            // Classify based on parameter name and description
            if (this.isBasicParam(paramName)) {
                paramGroups['Basic Parameters'].add(paramInfoText);
            } else if (this.isPaginationParam(paramName)) {
                paramGroups['Pagination Parameters'].add(paramInfoText);
            } else if (this.isFilterParam(paramName)) {
                paramGroups['Filter Parameters'].add(paramInfoText);
            } else if (this.isOptionParam(paramName)) {
                paramGroups['Option Parameters'].add(paramInfoText);
            } else {
                paramGroups['Other Parameters'].add(paramInfoText);
            }
        }

        // Convert to arrays and sort
        const result: Record<string, string[]> = {};

        for (const [group, paramSet] of Object.entries(paramGroups)) {
            result[group] = Array.from(paramSet).sort();
        }

        return result;
    }

    /**
     * Check if parameter is a basic parameter
     */
    private isBasicParam(paramName: string): boolean {
        const basicParams = ['action', 'url', 'element', 'ref', 'text', 'selector', 'xpath'];
        return basicParams.includes(paramName);
    }

    /**
     * Check if parameter is a pagination parameter
     */
    private isPaginationParam(paramName: string): boolean {
        const paginationParams = ['page', 'pageSize', 'sortOrder', 'limit', 'offset'];
        return paginationParams.includes(paramName);
    }

    /**
     * Check if parameter is a filter parameter
     */
    private isFilterParam(paramName: string): boolean {
        const filterParams = ['messageTypes', 'statusFilter', 'urlPattern', 'textPattern', 'filter', 'search'];
        return filterParams.includes(paramName) || paramName.includes('Filter') || paramName.includes('Pattern');
    }

    /**
     * Check if parameter is an option parameter
     */
    private isOptionParam(paramName: string): boolean {
        const optionParams = ['includeHistory', 'sinceNavigation', 'fullPage', 'doubleClick', 'submit', 'waitForLoad', 'timeout'];
        return optionParams.includes(paramName) || paramName.startsWith('include') || paramName.startsWith('enable');
    }

    /**
     * Generate usage examples
     */
    private generateExamples(): string {
        const examples = [];
        const allTools = this.registry.getAllTools();

        // 为每个类别生成示例
        const categories = this.registry.getToolsByCategory();

        const categoryEntries = Array.from(categories.entries());
        for (const [category, tools] of categoryEntries) {
            if (tools.length > 0) {
                examples.push(`### ${category}`);

                // Select the first tool in this category as an example
                const exampleTool = tools[0];
                const exampleParams = this.generateExampleParams(exampleTool);

                examples.push('```json');
                examples.push(JSON.stringify({
                    action: exampleTool.name,
                    ...exampleParams
                }, null, 2));
                examples.push('```');
                examples.push('');
            }
        }

        // Common workflow examples
        examples.push('### Common Workflows');
        examples.push('');

        examples.push('**Form Testing Workflow:**');
        examples.push('```json');
        examples.push(JSON.stringify({
            batch: [
                { action: "navigate", url: "https://example.com/form" },
                { action: "type", selector: "input[name='email']", text: "<EMAIL>" },
                { action: "type", selector: "input[name='password']", text: "password123" },
                { action: "click", selector: "button[type='submit']" },
                { action: "waitForElement", selector: ".success-message" }
            ]
        }, null, 2));
        examples.push('```');
        examples.push('');

        examples.push('**Page Analysis Workflow:**');
        examples.push('```json');
        examples.push(JSON.stringify({
            batch: [
                { action: "navigate", url: "https://example.com" },
                { action: "screenshot", fullPage: true },
                { action: "getConsole", page: 1, pageSize: 50 },
                { action: "getNetwork", page: 1, pageSize: 20 }
            ]
        }, null, 2));
        examples.push('```');

        return examples.join('\n');
    }

    /**
     * Generate example parameters for a tool
     */
    private generateExampleParams(tool: any): Record<string, any> {
        const params: Record<string, any> = {};

        if (tool.parameters && (tool.parameters as any)._def && (tool.parameters as any)._def.shape) {
            const shape = (tool.parameters as any)._def.shape();

            for (const [paramName, paramSchema] of Object.entries(shape)) {
                const schema = paramSchema as any;

                // 跳过可选参数，只包含必需的或常用的参数
                if (schema._def?.typeName === 'ZodOptional' && !this.isCommonParam(paramName)) {
                    continue;
                }

                // 根据参数类型和名称生成合适的示例值
                if (paramName === 'url') {
                    params[paramName] = 'https://example.com';
                } else if (paramName === 'element' || paramName === 'selector') {
                    params[paramName] = 'button.submit';
                } else if (paramName === 'text') {
                    params[paramName] = 'example text';
                } else if (paramName === 'page') {
                    params[paramName] = 1;
                } else if (paramName === 'pageSize') {
                    params[paramName] = 20;
                } else if (paramName === 'messageTypes') {
                    params[paramName] = ['error', 'warn'];
                } else if (paramName === 'time') {
                    params[paramName] = 2000;
                } else if (paramName === 'fullPage') {
                    params[paramName] = true;
                } else {
                    // Generate example values based on Zod schema type
                    const exampleValue = this.generateExampleFromSchema(schema, paramName);
                    if (exampleValue !== undefined) {
                        params[paramName] = exampleValue;
                    }
                }
            }
        }

        return params;
    }

    /**
     * Generate example values based on Zod schema
     */
    private generateExampleFromSchema(schema: any, paramName: string): any {
        // Handle optional types
        if (schema._def?.typeName === 'ZodOptional') {
            return this.generateExampleFromSchema(schema._def.innerType, paramName);
        }

        // Generate example values based on type
        switch (schema._def?.typeName) {
            case 'ZodString':
                return schema._def.description?.includes('example') ? 'example value' : `example ${paramName}`;
            case 'ZodNumber':
                return schema._def.checks?.find((c: any) => c.kind === 'min')?.value || 1;
            case 'ZodBoolean':
                return true;
            case 'ZodArray':
                const itemExample = this.generateExampleFromSchema(schema._def.type, 'item');
                return itemExample !== undefined ? [itemExample] : ['example item'];
            case 'ZodEnum':
                return schema._def.values?.[0] || 'example enum value';
            case 'ZodUnion':
                return this.generateExampleFromSchema(schema._def.options[0], paramName);
            default:
                return undefined;
        }
    }

    /**
     * Check if it's a common parameter (should be included in examples)
     */
    private isCommonParam(paramName: string): boolean {
        const commonParams = ['url', 'element', 'text', 'page', 'pageSize', 'time', 'fullPage'];
        return commonParams.includes(paramName);
    }
}