import { BatchAction, ToolResponse, ToolContext } from '../types/tool-definition';
import { BrowserToolRegistry } from './tool-registry';

/**
 * 批处理执行器
 * 负责执行批量操作序列
 */
export class BatchExecutor {
    constructor(private registry: BrowserToolRegistry) { }

    /**
     * 执行批处理操作
     * 只返回最后一个操作的结果
     */
    async executeBatch(actions: BatchAction[], context: ToolContext): Promise<ToolResponse> {
        if (actions.length === 0) {
            return {
                content: [{ type: 'text' as const, text: '### Batch Execution\nNo actions to execute.' }],
                isError: false
            };
        }

        let lastResult: ToolResponse | null = null;
        const executionLog: string[] = [];

        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];

            try {
                // 记录执行日志
                executionLog.push(`[${i + 1}/${actions.length}] Executing: ${action.action}`);

                // 获取工具定义
                const toolDef = this.registry.getToolDefinition(action.action);
                if (!toolDef) {
                    throw new Error(`Unknown action: ${action.action}`);
                }

                // 验证参数
                // try {
                //     toolDef.parameters.parse(action.params);
                // } catch (error: any) {
                //     throw new Error(`Parameter validation failed for ${action.action}: ${error.message}`);
                // }

                // 执行动作
                const result = await toolDef.handler(action.params, context);
                lastResult = result;

                // 如果操作失败且不允许继续，立即返回错误
                if (result.isError && !action.continueOnError) {
                    return {
                        content: [
                            {
                                type: 'text' as const,
                                text: `### Batch Execution Failed
**Failed at step ${i + 1}**: ${action.action}

**Execution Log**:
${executionLog.join('\n')}

**Error Details**:
${this.extractErrorMessage(result)}`
                            }
                        ],
                        isError: true
                    };
                }

                // 记录成功完成
                const status = result.isError ? '⚠️ Completed with error' : '✅ Completed';
                executionLog.push(`[${i + 1}/${actions.length}] ${status}: ${action.action}`);

            } catch (error: any) {
                // 捕获执行异常
                if (!action.continueOnError) {
                    return {
                        content: [
                            {
                                type: 'text' as const,
                                text: `### Batch Execution Error
**Exception at step ${i + 1}**: ${action.action}

**Execution Log**:
${executionLog.join('\n')}

**Exception**: ${error.message}`
                            }
                        ],
                        isError: true
                    };
                } else {
                    // 记录错误但继续执行
                    executionLog.push(`[${i + 1}/${actions.length}] ❌ Failed (continuing): ${action.action} - ${error.message}`);
                }
            }
        }

        // 所有操作完成，返回最后一个操作的结果
        if (lastResult) {
            // 在最后结果前添加批处理执行摘要
            const batchSummary = `### Batch Execution Completed
**Executed ${actions.length} actions successfully**

**Execution Log**:
${executionLog.join('\n')}

---

### Final Result
`;

            const enhancedContent = [
                { type: 'text' as const, text: batchSummary },
                ...lastResult.content
            ];

            return {
                content: enhancedContent,
                isError: lastResult.isError || false
            };
        }

        // 没有成功的操作
        return {
            content: [
                {
                    type: 'text' as const,
                    text: `### Batch Execution Completed
**Executed ${actions.length} actions**

**Execution Log**:
${executionLog.join('\n')}

**Result**: No successful operations to return.`
                }
            ],
            isError: false
        };
    }

    /**
     * 从结果中提取错误信息
     */
    private extractErrorMessage(result: ToolResponse): string {
        const errorContent = result.content.find(c => c.type === 'text');
        return errorContent ? errorContent.text : 'Unknown error occurred';
    }
}