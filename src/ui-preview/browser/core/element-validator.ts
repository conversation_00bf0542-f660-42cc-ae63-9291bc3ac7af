import { Page, Locator } from 'playwright-core';

/**
 * Element validator - validates various states of elements
 * Validates element states based on CSS selectors
 */
export class ElementValidator {
    /**
     * Validate complete element state
     * Including existence, visibility, clickability, etc.
     * @param page Playwright page object
     * @param selector CSS selector string
     */
    static async validateElement(page: Page, selector: string): Promise<ValidationResult> {
        const result: ValidationResult = {
            exists: false,
            visible: false,
            clickable: false,
            editable: false,
            enabled: false,
            inViewport: false,
            errors: []
        };

        try {
            // 1. Check if element exists
            const element = page.locator(selector);
            const count = await element.count();

            if (count === 0) {
                result.errors.push(`Element not found with CSS selector: ${selector}`);
                return result;
            }

            if (count > 1) {
                result.errors.push(`Multiple elements found (${count}) with CSS selector: ${selector}`);
            }

            result.exists = true;

            // 2. Check if element is visible
            try {
                result.visible = await element.isVisible();
                if (!result.visible) {
                    result.errors.push(`Element is not visible with CSS selector: ${selector}`);
                }
            } catch (error: any) {
                result.errors.push(`Failed to check visibility: ${error?.message || error}`);
            }

            // 3. Check if element is in viewport
            try {
                // Use boundingBox to check if element is in viewport
                const boundingBox = await element.boundingBox();
                if (boundingBox) {
                    const viewport = page.viewportSize();
                    result.inViewport = boundingBox.x >= 0 &&
                        boundingBox.y >= 0 &&
                        boundingBox.x + boundingBox.width <= (viewport?.width || 0) &&
                        boundingBox.y + boundingBox.height <= (viewport?.height || 0);
                } else {
                    result.inViewport = false;
                }
                if (!result.inViewport) {
                    result.errors.push(`Element is not in viewport with CSS selector: ${selector}`);
                }
            } catch (error: any) {
                result.errors.push(`Failed to check viewport visibility: ${error?.message || error}`);
                result.inViewport = false;
            }

            // 4. Check if element is enabled
            try {
                result.enabled = await element.isEnabled();
                if (!result.enabled) {
                    result.errors.push(`Element is disabled with CSS selector: ${selector}`);
                }
            } catch (error: any) {
                result.errors.push(`Failed to check enabled state: ${error?.message || error}`);
            }

            // 5. Check if element is clickable
            result.clickable = await this.isClickable(page, element);
            if (!result.clickable) {
                result.errors.push(`Element is not clickable with CSS selector: ${selector}`);
            }

            // 6. Check if element is editable
            result.editable = await this.isEditable(element);
            if (!result.editable) {
                result.errors.push(`Element is not editable with CSS selector: ${selector}`);
            }

        } catch (error: any) {
            result.errors.push(`Validation failed: ${error?.message || error}`);
        }

        return result;
    }

    /**
     * Check if element is editable
     * Check if element is an input box, text area, or other editable element
     */
    private static async isEditable(element: Locator): Promise<boolean> {
        try {
            // Check basic state
            const isVisible = await element.isVisible();
            const isEnabled = await element.isEnabled();


            if (!isVisible || !isEnabled) {
                return false;
            }

            // Check element type
            const tagName = await element.evaluate(el => el.tagName.toLowerCase()).catch(() => '');
            const type = await element.evaluate(el => (el as HTMLInputElement).type || '').catch(() => '');
            const contentEditable = await element.evaluate(el => (el as HTMLElement).contentEditable).catch(() => 'false');

            // Editable element types
            const editableTags = ['input', 'textarea'];
            const editableTypes = ['text', 'email', 'password', 'search', 'tel', 'url', 'number'];

            const isEditableTag = editableTags.includes(tagName);
            const isEditableType = tagName === 'input' ? editableTypes.includes(type) : false;
            const isContentEditable = contentEditable === 'true';

            return (tagName === 'textarea') ||
                (tagName === 'input' && isEditableType) ||
                isContentEditable;
        } catch (error) {
            return false;
        }
    }

    /**
     * Check if element is clickable
     * Considers visibility, enabled state, interactivity, etc.
     */
    private static async isClickable(page: Page, element: Locator): Promise<boolean> {
        try {
            // 检查基本状态
            const isVisible = await element.isVisible();
            const isEnabled = await element.isEnabled();

            if (!isVisible || !isEnabled) {
                return false;
            }

            // Check if element is in viewport
            let isInViewport = false;
            try {
                const boundingBox = await element.boundingBox();
                if (boundingBox) {
                    const viewport = page.viewportSize();
                    isInViewport = boundingBox.x >= 0 &&
                        boundingBox.y >= 0 &&
                        boundingBox.x + boundingBox.width <= (viewport?.width || 0) &&
                        boundingBox.y + boundingBox.height <= (viewport?.height || 0);
                }
            } catch (error) {
                isInViewport = false;
            }

            if (!isInViewport) {
                return false;
            }

            // Check if element has interactivity
            const tagName = await element.evaluate(el => el.tagName.toLowerCase()).catch(() => '');
            const role = await element.getAttribute('role').catch(() => null);

            // Clickable element types
            const clickableTags = ['button', 'a', 'input', 'select', 'textarea'];
            const clickableRoles = ['button', 'link', 'menuitem', 'tab', 'checkbox', 'radio'];

            const isClickableTag = clickableTags.includes(tagName);
            const isClickableRole = role && clickableRoles.includes(role);

            // Check if has click event listeners
            const hasClickHandlers = await element.evaluate(el => {
                const events = (el as any)._eventListeners || [];
                return events.some((event: any) =>
                    event.type === 'click' ||
                    event.type === 'mousedown' ||
                    event.type === 'mouseup'
                );
            }).catch(() => false);

            return isClickableTag || isClickableRole || hasClickHandlers;

        } catch (error: any) {
            return false;
        }
    }

    /**
     * Wait for element to become clickable
     * @param page Playwright page object
     * @param selector CSS selector string
     * @param timeout Timeout in milliseconds
     */
    static async waitForClickable(
        page: Page,
        selector: string,
        timeout: number = 5000
    ): Promise<ValidationResult> {

        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const result = await this.validateElement(page, selector);

            if (result.clickable) {
                return result;
            }

            // Wait a short time before retrying
            await page.waitForTimeout(100);
        }

        // Return last validation result after timeout
        return await this.validateElement(page, selector);
    }

    /**
     * Check if element is covered/obscured
     * @param page Playwright page object
     * @param selector CSS selector string
     */
    static async isElementCovered(page: Page, selector: string): Promise<boolean> {
        try {
            const element = page.locator(selector);

            // Check if element is covered by other elements
            const isCovered = await element.evaluate(el => {
                const rect = el.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;

                const elementAtPoint = document.elementFromPoint(centerX, centerY);
                return elementAtPoint !== el && !(el as Element).contains(elementAtPoint);
            });
            return isCovered;
        } catch (error: any) {
            return false;
        }
    }
}

/**
 * Validation result interface
 */
export interface ValidationResult {
    exists: boolean;
    visible: boolean;
    clickable: boolean;
    editable: boolean;
    enabled: boolean;
    inViewport: boolean;
    errors: string[];
}

/**
 * Element information interface
 */
export interface ElementInfo {
    selector: string;
    exists: boolean;
    tagName?: string;
    textContent?: string;
    innerText?: string;
    className?: string;
    id?: string;
    name?: string;
    type?: string;
    role?: string;
    ariaLabel?: string;
    title?: string;
    disabled?: boolean;
    hidden?: boolean;
    style?: string;
    boundingBox?: DOMRect;
    error?: string;
}