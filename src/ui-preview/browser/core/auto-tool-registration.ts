import { BrowserToolRegistry } from './tool-registry';
import { NavigationTools } from '../tools/navigation-tools';
import { InteractionTools } from '../tools/interaction-tools';
import { InputTools } from '../tools/input-tools';
import { EvaluateTools } from '../tools/evaluate-tools';
import { ConsoleTools } from '../tools/console-tools';
import { ScreenshotTools } from '../tools/screenshot-tools';
import { WaitTools } from '../tools/wait-tools';
import { NetworkTools } from '../tools/network-tools';
import { TabTools } from '../tools/tab-tools';
import { TabInfoTools } from '../tools/tab-info-tools';
import { DiagnosticTools } from '../tools/diagnostic-tools';
import { SnapshotTools } from '../tools/snapshot-tools';
import { MouseTools } from '../tools/mouse-tools';
import { BrowserToolDefinition } from '../types/tool-definition';

/**
 * Automatic tool registration system
 * Responsible for automatically discovering and registering all tools
 */
export class AutoToolRegistration {
    /**
     * 注册所有工具
     */
    static registerAllTools(registry: BrowserToolRegistry): void {
        // 自动发现并注册所有工具
        const toolModules = this.discoverToolModules();

        for (const module of toolModules) {
            const tools = this.extractToolsFromModule(module);
            for (const tool of tools) {
                registry.register(tool);
            }
        }
    }

    /**
     * 发现工具模块
     */
    private static discoverToolModules(): any[] {
        // 静态导入所有工具模块
        const modules = [
            NavigationTools,
            InteractionTools,
            InputTools,
            EvaluateTools,
            ConsoleTools,
            // 暂时禁用截图工具
            ScreenshotTools,
            WaitTools,
            NetworkTools,
            TabTools,
            TabInfoTools,
            DiagnosticTools,
            SnapshotTools,
            MouseTools
        ];

        return modules;
    }

    /**
     * 从模块中提取工具
     */
    private static extractToolsFromModule(module: any): BrowserToolDefinition[] {
        const tools: BrowserToolDefinition[] = [];

        // 检查模块本身是否是工具组
        if (this.isToolGroup(module)) {
            // 直接从工具组中提取工具
            for (const tool of Object.values(module)) {
                if (this.isValidToolDefinition(tool)) {
                    tools.push(tool as BrowserToolDefinition);
                }
            }
        } else {
            // 遍历模块导出的所有项
            for (const exportedItem of Object.values(module)) {
                if (this.isToolGroup(exportedItem)) {
                    // 从工具组中提取单个工具
                    for (const tool of Object.values(exportedItem as any)) {
                        if (this.isValidToolDefinition(tool)) {
                            tools.push(tool as BrowserToolDefinition);
                        }
                    }
                } else if (this.isValidToolDefinition(exportedItem)) {
                    // 直接是工具定义
                    tools.push(exportedItem as BrowserToolDefinition);
                }
            }
        }

        return tools;
    }

    /**
     * 检查是否为工具组
     */
    private static isToolGroup(obj: any): boolean {
        // 工具组应该是一个对象，包含多个工具定义，但本身不是工具定义
        if (!obj || typeof obj !== 'object' || obj.name || obj.handler) {
            return false;
        }

        // 检查对象的值是否包含有效的工具定义
        const values = Object.values(obj);
        return values.length > 0 && values.some(value => this.isValidToolDefinition(value));
    }

    /**
     * 验证工具定义
     */
    private static isValidToolDefinition(obj: any): boolean {
        return obj &&
            typeof obj.name === 'string' &&
            typeof obj.description === 'string' &&
            typeof obj.category === 'string' &&
            typeof obj.capability === 'string' &&
            typeof obj.handler === 'function' &&
            obj.parameters &&
            Array.isArray(obj.examples);
    }
}