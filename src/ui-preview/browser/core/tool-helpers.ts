import { Page, BrowserContext } from 'playwright-core';
import { ToolContext } from '../types/tool-definition';
import { TabManager } from './tab-manager';

/**
 * Tool helper functions
 * For handling optional page and context
 */
export class ToolHelpers {
    /**
     * Get page, throw error if not available
     */
    static async requirePage(context: ToolContext, tabId?: string): Promise<Page> {
        if (context.page) {
            return context.page;
        }

        if (context.getPage) {
            const page = await context.getPage(tabId);
            if (page) {
                return page;
            }
        }

        const finalTabId = tabId || context.tabId;
        if (finalTabId) {
            const tabManager = TabManager.getInstance();
            const page = tabManager.getPageByTabId(finalTabId);
            if (page) {
                return page;
            }
        }

        // Provide more detailed error information
        const diagnosticInfo = {
            hasContextPage: !!context.page,
            hasGetPageMethod: !!context.getPage,
            providedTabId: tabId,
            contextTabId: context.tabId,
            finalTabId,
            availableTabs: TabManager.getInstance().getAllTabs().map(tab => ({
                tabId: tab.tabId,
                url: tab.url,
                isClosed: tab.isClosed
            }))
        };

        throw new Error(`Page is required for this operation but not available. Diagnostic info: ${JSON.stringify(diagnosticInfo, null, 2)}`);
    }

    /**
     * Get context, throw error if not available
     */
    static async requireContext(context: ToolContext, tabId?: string): Promise<BrowserContext> {
        if (context.context) {
            return context.context;
        }

        if (context.getContext) {
            const browserContext = await context.getContext(tabId);
            if (browserContext) {
                return browserContext;
            }
        }

        const finalTabId = tabId || context.tabId;
        if (finalTabId) {
            const tabManager = TabManager.getInstance();
            const page = tabManager.getPageByTabId(finalTabId);
            if (page) {
                return page.context();
            }
        }

        // Provide more detailed error information
        const diagnosticInfo = {
            hasContextContext: !!context.context,
            hasGetContextMethod: !!context.getContext,
            providedTabId: tabId,
            contextTabId: context.tabId,
            finalTabId,
            availableTabs: TabManager.getInstance().getAllTabs().map(tab => ({
                tabId: tab.tabId,
                url: tab.url,
                isClosed: tab.isClosed
            }))
        };

        throw new Error(`Browser context is required for this operation but not available. Diagnostic info: ${JSON.stringify(diagnosticInfo, null, 2)}`);
    }

    /**
     * Get page (optional)
     */
    static async getPage(context: ToolContext, tabId?: string): Promise<Page | null> {
        if (context.page) {
            return context.page;
        }

        if (context.getPage) {
            return await context.getPage(tabId);
        }

        const finalTabId = tabId || context.tabId;
        if (finalTabId) {
            const tabManager = TabManager.getInstance();
            return tabManager.getPageByTabId(finalTabId);
        }

        return null;
    }

    /**
     * Get context (optional)
     */
    static async getContext(context: ToolContext, tabId?: string): Promise<BrowserContext | null> {
        if (context.context) {
            return context.context;
        }

        if (context.getContext) {
            return await context.getContext(tabId);
        }

        const finalTabId = tabId || context.tabId;
        if (finalTabId) {
            const tabManager = TabManager.getInstance();
            const page = tabManager.getPageByTabId(finalTabId);
            return page ? page.context() : null;
        }

        return null;
    }

    /**
     * Get tabId
     */
    static getTabId(context: ToolContext, tabId?: string): string | null {
        return tabId || context.tabId || (context.getTabId ? context.getTabId() : null);
    }

    /**
     * Validate if operation requires page
     */
    static requiresPage(action: string): boolean {
        const pageRequiredActions = [
            'navigate',
            'click',
            'type',
            'screenshot',
            'wait_for',
            'get_console_messages',
            'get_network_requests',
            'switch_tab',
            'close_tab'
        ];
        return pageRequiredActions.some(requiredAction => action.includes(requiredAction));
    }

    /**
     * Validate if operation requires context
     */
    static requiresContext(action: string): boolean {
        const contextRequiredActions = [
            'create_tab',
            'new_page'
        ];
        return contextRequiredActions.some(requiredAction => action.includes(requiredAction));
    }
}