import { Page } from 'playwright-core';
import { TabState } from '../types/tool-definition';
import { CacheManager } from './cache-manager';

/**
 * Tab lifecycle manager
 * Responsible for managing tab lifecycle and state
 */
export class PageLifecycleManager {
    private static tabStates = new Map<string, TabState>();

    /**
     * 初始化标签页
     */
    static initializePage(page: Page, tabId: string): void {
        const state: TabState = {
            tabId,
            url: page.url(),
            navigationCount: 0,
            lastNavigationTime: Date.now(),
            isLoading: false
        };

        this.tabStates.set(tabId, state);
        this.setupPageListeners(page, tabId);
    }

    /**
     * 设置标签页事件监听
     */
    private static setupPageListeners(page: Page, tabId: string): void {
        // 导航开始
        page.on('framenavigated', (frame) => {
            if (frame === page.mainFrame()) {
                this.handleTabNavigation(tabId, frame.url());
            }
        });

        // 页面加载开始
        page.on('request', (request) => {
            if (request.isNavigationRequest()) {
                this.setTabLoading(tabId, true);
            }
        });

        // 页面加载完成
        page.on('load', () => {
            this.setTabLoading(tabId, false);
            this.handleTabLoaded(tabId);
        });

        // DOM 内容加载完成
        page.on('domcontentloaded', () => {
            this.handleDOMLoaded(tabId);
        });

        // 页面关闭时清理
        page.on('close', () => {
            this.cleanupTabState(tabId);
        });
    }

    /**
     * 处理标签页导航
     */
    private static handleTabNavigation(tabId: string, url: string): void {
        const state = this.tabStates.get(tabId);
        if (state) {
            state.navigationCount++;
            state.lastNavigationTime = Date.now();
            state.url = url;

            // 清理相关状态
            this.clearTabRelatedData(tabId);
        }
    }

    /**
     * 处理标签页加载完成
     */
    private static handleTabLoaded(tabId: string): void {
        this.finalizeTabState(tabId);
    }

    /**
     * 处理DOM加载完成
     */
    private static handleDOMLoaded(tabId: string): void {
        // console.log(`[TabLifecycle] DOM loaded for ${tabId}`);
    }

    /**
     * 设置标签页加载状态
     */
    private static setTabLoading(tabId: string, isLoading: boolean): void {
        const state = this.tabStates.get(tabId);
        if (state) {
            state.isLoading = isLoading;
        }
    }

    /**
     * 清理标签页相关数据
     */
    private static clearTabRelatedData(tabId: string): void {
        // 清理控制台消息
        CacheManager.clearConsoleHistory(tabId);
        CacheManager.markPageNavigation(tabId);

        // 清理标签页快照缓存
        CacheManager.clearSnapshotCache(tabId);
    }

    /**
     * 最终化标签页状态
     */
    private static finalizeTabState(tabId: string): void {
        const state = this.tabStates.get(tabId);
        if (state) {
            state.isLoading = false;
        }
    }

    /**
     * 清理标签页状态
     */
    private static cleanupTabState(tabId: string): void {
        this.tabStates.delete(tabId);
        CacheManager.clearAllTabData(tabId);
    }

    /**
     * 获取标签页状态
     */
    static getPageState(tabId: string): TabState | undefined {
        return this.tabStates.get(tabId);
    }

    /**
     * 检查标签页是否正在加载
     */
    static isPageLoading(tabId: string): boolean {
        const state = this.tabStates.get(tabId);
        return state?.isLoading || false;
    }

    /**
     * 获取导航次数
     */
    static getNavigationCount(tabId: string): number {
        const state = this.tabStates.get(tabId);
        return state?.navigationCount || 0;
    }
}