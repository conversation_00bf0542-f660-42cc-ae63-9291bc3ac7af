import { Page, Locator } from 'playwright-core';
import { ElementParams } from '../types/tool-definition';
import { CacheManager } from './cache-manager';
import { ToolHelpers } from './tool-helpers';

/**
 * Extended Page interface to access internal snapshot method
 */
interface PageEx extends Page {
    _snapshotForAI(): Promise<string>;
}

/**
 * Element resolver - based on CSS selectors and precise references
 * Responsible for handling CSS selectors and precise element reference mechanisms
 */
export class ElementResolver {

    /**
     * Resolve element to Locator object
     * Enhanced version that returns Playwright Locator with comprehensive validation
     */
    static async resolveElementToLocator(page: Page, params: ElementParams, context?: any): Promise<Locator> {
        if (!page) {
            throw new Error('Page is required for element resolution');
        }

        // Priority 1: Use ref if provided (Playwright MCP standard)
        if (params.ref) {
            try {
                // Use ref locator method with enhanced validation
                return await this.refLocator(page, {
                    element: params.element || 'element',
                    ref: params.ref
                });
            } catch (error) {
                throw new Error(`Ref ${params.ref} not found in the current page snapshot. Try capturing new snapshot.`);
            }
        }

        // Priority 2: Use CSS selector if provided
        if (params.element) {
            try {
                // Enhanced CSS selector validation and locator creation
                const locator = await this.createCSSLocator(page, params.element, context);
                return locator;
            } catch (error) {
                throw new Error(`No element found matching CSS selector: "${params.element}"`);
            }
        }

        throw new Error('Either element or ref parameter is required');
    }

    /**
     * Get a single locator by ref
     * Reference implementation for ref resolution
     */
    static async refLocator(page: Page, params: { element: string, ref: string }): Promise<Locator> {
        return (await this.refLocators(page, [params]))[0];
    }

    /**
     * Get multiple locators by refs
     * Reference implementation for batch ref resolution
     */
    static async refLocators(page: Page, params: { element: string, ref: string }[]): Promise<Locator[]> {
        const snapshot = await (page as PageEx)._snapshotForAI();

        return params.map(param => {
            if (!snapshot.includes(`[ref=${param.ref}]`)) {
                throw new Error(`Ref ${param.ref} not found in the current page snapshot. Try capturing new snapshot.`);
            }
            return page.locator(`aria-ref=${param.ref}`).describe(param.element);
        });
    }


    /**
     * Create enhanced CSS Locator with validation and context
     * Returns Playwright Locator object with additional features
     */
    private static async createCSSLocator(page: Page, selector: string, context?: any): Promise<Locator> {
        // Step 1: Validate CSS selector syntax
        if (!this.isValidCSSSelector(selector)) {
            throw new Error(`Invalid CSS selector syntax: "${selector}"`);
        }

        // Step 2: Create base locator
        let locator = page.locator(selector);

        // Step 3: Validate element exists
        const count = await locator.count();
        if (count === 0) {
            throw new Error(`No element found matching CSS selector: "${selector}"`);
        }

        // Step 4: Apply context-based filtering if provided
        if (context) {
            locator = this.applyContextToLocator(locator, context);
        }

        // Step 5: Add timeout and retry options
        locator = locator.first(); // Use first element if multiple matches

        return locator;
    }

    /**
     * Apply context-based filtering to locator
     */
    private static applyContextToLocator(locator: Locator, context: any): Locator {
        // Apply visible filter if specified
        if (context.visible !== undefined) {
            locator = context.visible ? locator.locator(':visible') : locator;
        }

        // Apply enabled filter if specified
        if (context.enabled !== undefined) {
            locator = context.enabled ? locator.locator(':enabled') : locator;
        }

        // Apply text filter if specified
        if (context.hasText) {
            locator = locator.filter({ hasText: context.hasText });
        }

        // Apply nth element if specified
        if (typeof context.nth === 'number') {
            locator = locator.nth(context.nth);
        }

        return locator;
    }

    /**
     * Validate CSS selector syntax
     */
    private static isValidCSSSelector(selector: string): boolean {
        try {
            // Try to create a dummy element and test the selector
            if (typeof document !== 'undefined') {
                document.querySelector(selector);
            }
            return true;
        } catch (error) {
            // Invalid CSS selector syntax
            return false;
        }
    }






}