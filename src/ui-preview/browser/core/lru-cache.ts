/**
 * LRU (Least Recently Used) Cache implementation
 * 
 * A generic LRU cache that maintains a fixed capacity and automatically
 * evicts the least recently used items when the capacity is exceeded.
 * 
 * @template T The type of values stored in the cache
 */
export class LRUCache<T> {
    private capacity: number;
    private cache = new Map<string, T>();
    private accessOrder: string[] = [];

    /**
     * Creates a new LRU cache with the specified capacity
     * 
     * @param capacity Maximum number of items the cache can hold
     */
    constructor(capacity: number) {
        if (capacity <= 0) {
            throw new Error('LRU cache capacity must be greater than 0');
        }
        this.capacity = capacity;
    }

    /**
     * Retrieves a value from the cache and marks it as recently used
     * 
     * @param key The key to retrieve
     * @returns The value associated with the key, or undefined if not found
     */
    get(key: string): T | undefined {
        const value = this.cache.get(key);
        if (value !== undefined) {
            // Move to end (most recently used)
            this.moveToEnd(key);
        }
        return value;
    }

    /**
     * Stores a value in the cache and marks it as recently used
     * If the cache is at capacity, the least recently used item is evicted
     * 
     * @param key The key to store
     * @param value The value to associate with the key
     */
    set(key: string, value: T): void {
        if (this.cache.has(key)) {
            // Update existing key
            this.cache.set(key, value);
            this.moveToEnd(key);
        } else {
            // Add new key
            if (this.cache.size >= this.capacity) {
                // Remove least recently used
                const lruKey = this.accessOrder.shift();
                if (lruKey) {
                    this.cache.delete(lruKey);
                }
            }
            this.cache.set(key, value);
            this.accessOrder.push(key);
        }
    }

    /**
     * Removes a key-value pair from the cache
     * 
     * @param key The key to remove
     * @returns true if the key was found and removed, false otherwise
     */
    delete(key: string): boolean {
        const deleted = this.cache.delete(key);
        if (deleted) {
            const index = this.accessOrder.indexOf(key);
            if (index > -1) {
                this.accessOrder.splice(index, 1);
            }
        }
        return deleted;
    }

    /**
     * Checks if a key exists in the cache
     * 
     * @param key The key to check
     * @returns true if the key exists, false otherwise
     */
    has(key: string): boolean {
        return this.cache.has(key);
    }

    /**
     * Clears all items from the cache
     */
    clear(): void {
        this.cache.clear();
        this.accessOrder = [];
    }

    /**
     * Returns an iterator for all keys in the cache
     * 
     * @returns Iterator for cache keys
     */
    keys(): IterableIterator<string> {
        return this.cache.keys();
    }

    /**
     * Returns an iterator for all values in the cache
     * 
     * @returns Iterator for cache values
     */
    values(): IterableIterator<T> {
        return this.cache.values();
    }

    /**
     * Returns an iterator for all key-value pairs in the cache
     * 
     * @returns Iterator for cache entries
     */
    entries(): IterableIterator<[string, T]> {
        return this.cache.entries();
    }

    /**
     * Returns the current number of items in the cache
     * 
     * @returns The cache size
     */
    size(): number {
        return this.cache.size;
    }

    /**
     * Returns the maximum capacity of the cache
     * 
     * @returns The cache capacity
     */
    getCapacity(): number {
        return this.capacity;
    }

    /**
     * Returns the current access order (least to most recently used)
     * 
     * @returns Array of keys in access order
     */
    getAccessOrder(): string[] {
        return [...this.accessOrder];
    }

    /**
     * Returns the least recently used keys
     * 
     * @param count Number of LRU keys to return
     * @returns Array of least recently used keys
     */
    getLRUKeys(count: number): string[] {
        return this.accessOrder.slice(0, Math.min(count, this.accessOrder.length));
    }

    /**
     * Returns the most recently used keys
     * 
     * @param count Number of MRU keys to return
     * @returns Array of most recently used keys
     */
    getMRUKeys(count: number): string[] {
        const startIndex = Math.max(0, this.accessOrder.length - count);
        return this.accessOrder.slice(startIndex).reverse();
    }

    /**
     * Checks if the cache is at full capacity
     * 
     * @returns true if the cache is full, false otherwise
     */
    isFull(): boolean {
        return this.cache.size >= this.capacity;
    }

    /**
     * Checks if the cache is empty
     * 
     * @returns true if the cache is empty, false otherwise
     */
    isEmpty(): boolean {
        return this.cache.size === 0;
    }

    /**
     * Moves a key to the end of the access order (most recently used)
     * 
     * @param key The key to move
     */
    private moveToEnd(key: string): void {
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
            this.accessOrder.splice(index, 1);
            this.accessOrder.push(key);
        }
    }

    /**
     * Returns cache statistics for debugging and monitoring
     * 
     * @returns Object containing cache statistics
     */
    getStats(): {
        size: number;
        capacity: number;
        utilizationRate: number;
        oldestKey?: string;
        newestKey?: string;
    } {
        return {
            size: this.cache.size,
            capacity: this.capacity,
            utilizationRate: this.cache.size / this.capacity,
            oldestKey: this.accessOrder[0],
            newestKey: this.accessOrder[this.accessOrder.length - 1]
        };
    }

    /**
     * Validates the internal consistency of the cache
     * Used for debugging and testing
     * 
     * @returns true if the cache is consistent, false otherwise
     */
    validateConsistency(): boolean {
        // Check if cache size matches access order length
        if (this.cache.size !== this.accessOrder.length) {
            return false;
        }

        // Check if all keys in cache are in access order
        const cacheKeys = Array.from(this.cache.keys());
        for (const key of cacheKeys) {
            if (!this.accessOrder.includes(key)) {
                return false;
            }
        }

        // Check if all keys in access order are in cache
        for (const key of this.accessOrder) {
            if (!this.cache.has(key)) {
                return false;
            }
        }

        // Check for duplicate keys in access order
        const uniqueKeys = new Set(this.accessOrder);
        if (uniqueKeys.size !== this.accessOrder.length) {
            return false;
        }

        return true;
    }
}