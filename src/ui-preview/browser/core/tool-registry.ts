import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';

/**
 * Browser tool registry
 * Responsible for managing all available browser tools
 */
export class BrowserToolRegistry {
    private tools = new Map<string, BrowserToolDefinition>();
    private categories = new Map<ToolCategory, BrowserToolDefinition[]>();

    /**
     * 注册工具
     */
    register(toolDef: BrowserToolDefinition): void {
        this.tools.set(toolDef.name, toolDef);

        // 按类别分组
        if (!this.categories.has(toolDef.category)) {
            this.categories.set(toolDef.category, []);
        }
        this.categories.get(toolDef.category)!.push(toolDef);
    }

    /**
     * 获取工具定义
     */
    getToolDefinition(name: string): BrowserToolDefinition | undefined {
        return this.tools.get(name);
    }

    /**
     * 获取所有可用操作名称
     */
    getAvailableActions(): string[] {
        return Array.from(this.tools.keys());
    }

    /**
     * 按类别获取工具
     */
    getToolsByCategory(): Map<ToolCategory, BrowserToolDefinition[]> {
        return new Map(this.categories);
    }

    /**
     * 获取所有工具
     */
    getAllTools(): BrowserToolDefinition[] {
        return Array.from(this.tools.values());
    }

    /**
     * 按能力获取工具
     */
    getToolsByCapability(capability: ToolCapability): BrowserToolDefinition[] {
        return this.getAllTools().filter(tool => tool.capability === capability);
    }

    /**
     * 检查工具是否存在
     */
    hasToolDefinition(name: string): boolean {
        return this.tools.has(name);
    }

    /**
     * 获取工具数量
     */
    getToolCount(): number {
        return this.tools.size;
    }

    /**
     * 获取类别数量
     */
    getCategoryCount(): number {
        return this.categories.size;
    }

    /**
     * 清空注册表
     */
    clear(): void {
        this.tools.clear();
        this.categories.clear();
    }
}