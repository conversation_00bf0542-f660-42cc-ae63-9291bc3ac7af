import { Page } from 'playwright-core';

/**
 * Tab information interface
 */
export interface TabInfo {
    tabId: string;
    page: Page;
    url: string;
    title: string;
    createdAt: number;
    lastActiveAt: number;
    isClosed: boolean;
    metadata?: Record<string, any>; // Additional metadata for the tab
}

/**
 * Page-Tab relationship event types
 */
export type PageTabEventType = 'registered' | 'unregistered' | 'updated' | 'activated';

/**
 * Page-Tab relationship event
 */
export interface PageTabEvent {
    type: PageTabEventType;
    tabId: string;
    page: Page;
    timestamp: number;
    metadata?: Record<string, any>;
}

/**
 * Page-Tab relationship listener
 */
export type PageTabEventListener = (event: PageTabEvent) => void;

/**
 * Tab manager
 * Responsible for managing tabId mapping relationships for all browser tabs
 */
export class TabManager {
    private static instance: TabManager | null = null;
    private tabs = new Map<string, TabInfo>();
    private pageToTabId = new WeakMap<Page, string>();
    private tabIdToPage = new Map<string, Page>(); // Additional mapping for faster lookup
    private eventListeners = new Set<PageTabEventListener>();
    private readonly cleanupInterval: NodeJS.Timeout;

    private constructor() {
        // Periodically clean up closed tabs
        this.cleanupInterval = setInterval(() => {
            this.cleanupClosedTabs();
        }, 60000); // Clean up every minute
    }

    static getInstance(): TabManager {
        if (!TabManager.instance) {
            TabManager.instance = new TabManager();
        }
        return TabManager.instance;
    }

    /**
     * Add event listener for page-tab relationship changes
     */
    addEventListener(listener: PageTabEventListener): void {
        this.eventListeners.add(listener);
    }

    /**
     * Remove event listener for page-tab relationship changes
     */
    removeEventListener(listener: PageTabEventListener): void {
        this.eventListeners.delete(listener);
    }

    /**
     * Emit page-tab relationship event
     */
    private emitEvent(event: PageTabEvent): void {
        this.eventListeners.forEach(listener => {
            try {
                listener(event);
            } catch (error) {
            }
        });
    }

    /**
     * Register a new tab
     */
    registerTab(page: Page, customTabId?: string, metadata?: Record<string, any>): string {
        if (!page) {
            throw new Error('Page is required for tab registration');
        }

        const existingTabId = this.pageToTabId.get(page);
        if (existingTabId) {
            // Update last active time and metadata
            const tabInfo = this.tabs.get(existingTabId);
            if (tabInfo && !tabInfo.isClosed) {
                tabInfo.lastActiveAt = Date.now();
                if (metadata) {
                    tabInfo.metadata = { ...tabInfo.metadata, ...metadata };
                }
                this.emitEvent({
                    type: 'updated',
                    tabId: existingTabId,
                    page,
                    timestamp: Date.now(),
                    metadata
                });
                return existingTabId;
            }
        }

        let tabId = customTabId || this.generateTabId();

        // If custom tabId is provided, check if it's already used by another page
        if (customTabId) {
            const existingTabInfo = this.tabs.get(customTabId);
            if (existingTabInfo && !existingTabInfo.isClosed && existingTabInfo.page !== page) {
                throw new Error(`TabId ${customTabId} is already in use by another page`);
            }
            // If tabId exists but page is closed, it can be reused
            if (existingTabInfo && existingTabInfo.isClosed) {
                // Clean up old tabInfo
                this.tabs.delete(customTabId);
                this.tabIdToPage.delete(customTabId);
            }
        } else {
            // Generate unique tabId
            while (this.tabs.has(tabId)) {
                tabId = this.generateTabId();
            }
        }

        const now = Date.now();

        const tabInfo: TabInfo = {
            tabId,
            page,
            url: page.url() || 'about:blank',
            title: '',
            createdAt: now,
            lastActiveAt: now,
            isClosed: false,
            metadata: metadata || {}
        };

        this.tabs.set(tabId, tabInfo);
        this.pageToTabId.set(page, tabId);
        this.tabIdToPage.set(tabId, page);

        // Asynchronously update title
        this.updateTabTitle(tabInfo);

        // Listen for page close event
        page.on('close', () => {
            this.unregisterTab(tabId);
        });

        // Listen for page navigation events to update URL
        page.on('framenavigated', (frame) => {
            if (frame === page.mainFrame()) {
                tabInfo.url = page.url();
                this.updateTabTitle(tabInfo);
                this.emitEvent({
                    type: 'updated',
                    tabId,
                    page,
                    timestamp: Date.now(),
                    metadata: { url: tabInfo.url }
                });
            }
        });

        // Emit registration event
        this.emitEvent({
            type: 'registered',
            tabId,
            page,
            timestamp: now,
            metadata
        });

        return tabId;
    }

    /**
     * Asynchronously update tab title
     */
    private async updateTabTitle(tabInfo: TabInfo): Promise<void> {
        try {
            const title = await tabInfo.page.title();
            const oldTitle = tabInfo.title;
            tabInfo.title = title || 'Unknown';

            // Emit update event if title changed
            if (oldTitle !== tabInfo.title) {
                this.emitEvent({
                    type: 'updated',
                    tabId: tabInfo.tabId,
                    page: tabInfo.page,
                    timestamp: Date.now(),
                    metadata: { title: tabInfo.title, oldTitle }
                });
            }
        } catch (error) {
            tabInfo.title = 'Unknown';
            console.warn(`Failed to get title for tab ${tabInfo.tabId}:`, error);
        }
    }

    /**
     * Unregister a tab
     */
    unregisterTab(tabId: string): void {
        const tabInfo = this.tabs.get(tabId);
        if (tabInfo) {
            tabInfo.isClosed = true;
            this.pageToTabId.delete(tabInfo.page);
            this.tabIdToPage.delete(tabId);

            // Emit unregistration event
            this.emitEvent({
                type: 'unregistered',
                tabId,
                page: tabInfo.page,
                timestamp: Date.now()
            });

            // Don't delete immediately, let cleanup mechanism handle it
        }
    }

    /**
     * Clean up closed tabs
     */
    private cleanupClosedTabs(): void {
        const closedTabs = Array.from(this.tabs.entries())
            .filter(([_, tabInfo]) => tabInfo.isClosed);

        closedTabs.forEach(([tabId, _]) => {
            this.tabs.delete(tabId);
            this.tabIdToPage.delete(tabId);
        });
    }

    /**
     * Get page by tabId
     */
    getPageByTabId(tabId: string): Page | null {
        const page = this.tabIdToPage.get(tabId);
        if (page) {
            const tabInfo = this.tabs.get(tabId);
            if (tabInfo && !tabInfo.isClosed) {
                return page;
            }
        }
        return null;
    }

    /**
     * Get tabId by page
     */
    getTabIdByPage(page: Page): string | null {
        return this.pageToTabId.get(page) || null;
    }

    /**
     * Check if page-tab relationship exists
     */
    hasPageTabRelationship(page: Page, tabId: string): boolean {
        const existingTabId = this.pageToTabId.get(page);
        const existingPage = this.tabIdToPage.get(tabId);
        return existingTabId === tabId && existingPage === page;
    }

    /**
     * Get all page-tab relationships
     */
    getAllPageTabRelationships(): Array<{ page: Page; tabId: string; tabInfo: TabInfo }> {
        const relationships: Array<{ page: Page; tabId: string; tabInfo: TabInfo }> = [];

        for (const [tabId, tabInfo] of this.tabs.entries()) {
            if (!tabInfo.isClosed) {
                relationships.push({
                    page: tabInfo.page,
                    tabId,
                    tabInfo
                });
            }
        }

        return relationships;
    }

    /**
     * Validate page-tab relationship integrity
     */
    validateRelationshipIntegrity(): { isValid: boolean; issues: string[] } {
        const issues: string[] = [];
        let isValid = true;

        // Check if all tabs have valid page mappings
        for (const [tabId, tabInfo] of this.tabs.entries()) {
            if (!tabInfo.isClosed) {
                const pageFromMapping = this.tabIdToPage.get(tabId);
                const tabIdFromPage = this.pageToTabId.get(tabInfo.page);

                if (pageFromMapping !== tabInfo.page) {
                    issues.push(`Tab ${tabId}: tabIdToPage mapping mismatch`);
                    isValid = false;
                }

                if (tabIdFromPage !== tabId) {
                    issues.push(`Tab ${tabId}: pageToTabId mapping mismatch`);
                    isValid = false;
                }
            }
        }

        // Check for orphaned mappings in tabIdToPage
        for (const [tabId, page] of this.tabIdToPage.entries()) {
            const tabInfo = this.tabs.get(tabId);
            if (!tabInfo || tabInfo.isClosed) {
                issues.push(`Orphaned tabIdToPage mapping for tab ${tabId}`);
                isValid = false;
            }
        }

        return { isValid, issues };
    }

    /**
     * Repair page-tab relationship integrity
     */
    repairRelationshipIntegrity(): void {
        const { issues } = this.validateRelationshipIntegrity();

        if (issues.length === 0) {
            return;
        }

        console.warn('[TabManager] Repairing relationship integrity issues:', issues);

        // Clear all mappings and rebuild
        this.tabIdToPage.clear();
        // Note: pageToTabId is a WeakMap and will be rebuilt

        for (const [tabId, tabInfo] of this.tabs.entries()) {
            if (!tabInfo.isClosed) {
                this.tabIdToPage.set(tabId, tabInfo.page);
                this.pageToTabId.set(tabInfo.page, tabId);
            }
        }
    }

    /**
     * 获取所有活跃标签页信息
     */
    getAllTabs(): TabInfo[] {
        return Array.from(this.tabs.values()).filter(tab => !tab.isClosed);
    }

    /**
     * 获取标签页信息
     */
    getTabInfo(tabId: string): TabInfo | null {
        const tabInfo = this.tabs.get(tabId);
        return tabInfo && !tabInfo.isClosed ? tabInfo : null;
    }

    /**
     * Update tab activity time
     */
    updateTabActivity(tabId: string, metadata?: Record<string, any>): void {
        const tabInfo = this.tabs.get(tabId);
        if (tabInfo && !tabInfo.isClosed) {
            tabInfo.lastActiveAt = Date.now();

            if (metadata) {
                tabInfo.metadata = { ...tabInfo.metadata, ...metadata };
            }

            // Emit activation event
            this.emitEvent({
                type: 'activated',
                tabId,
                page: tabInfo.page,
                timestamp: tabInfo.lastActiveAt,
                metadata
            });
        }
    }

    /**
     * Update tab metadata
     */
    updateTabMetadata(tabId: string, metadata: Record<string, any>): void {
        const tabInfo = this.tabs.get(tabId);
        if (tabInfo && !tabInfo.isClosed) {
            tabInfo.metadata = { ...tabInfo.metadata, ...metadata };

            this.emitEvent({
                type: 'updated',
                tabId,
                page: tabInfo.page,
                timestamp: Date.now(),
                metadata
            });
        }
    }

    /**
     * Get tab metadata
     */
    getTabMetadata(tabId: string): Record<string, any> | null {
        const tabInfo = this.tabs.get(tabId);
        return tabInfo && !tabInfo.isClosed ? (tabInfo.metadata || {}) : null;
    }

    /**
     * 生成唯一的 tabId
     */
    private generateTabId(): string {
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substr(2, 9);
        return `tab_${timestamp}_${randomId}`;
    }

    /**
     * Clear all tabs
     */
    clear(): void {
        // Emit unregistration events for all active tabs
        for (const [tabId, tabInfo] of this.tabs.entries()) {
            if (!tabInfo.isClosed) {
                this.emitEvent({
                    type: 'unregistered',
                    tabId,
                    page: tabInfo.page,
                    timestamp: Date.now()
                });
            }
        }

        this.tabs.clear();
        this.tabIdToPage.clear();
        // WeakMap will be automatically cleaned up
    }

    /**
     * Get active tab count
     */
    getTabCount(): number {
        return Array.from(this.tabs.values()).filter(tab => !tab.isClosed).length;
    }

    /**
     * Get relationship statistics
     */
    getRelationshipStats(): {
        totalTabs: number;
        activeTabs: number;
        closedTabs: number;
        mappingIntegrity: { isValid: boolean; issues: string[] };
    } {
        const allTabs = Array.from(this.tabs.values());
        const activeTabs = allTabs.filter(tab => !tab.isClosed);
        const closedTabs = allTabs.filter(tab => tab.isClosed);
        const mappingIntegrity = this.validateRelationshipIntegrity();

        return {
            totalTabs: allTabs.length,
            activeTabs: activeTabs.length,
            closedTabs: closedTabs.length,
            mappingIntegrity
        };
    }

    /**
     * Destroy manager instance
     */
    destroy(): void {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
        }
        this.clear();
        this.eventListeners.clear();
        TabManager.instance = null;
    }
}