import { ConsoleMessage, NetworkRequest, PageDataSummary, TabDataSummary } from '../types/tool-definition';
import { ToolSystemConfigManager } from '../config/tool-system-config';
import { LRUCache } from './lru-cache';

/**
 * Cache manager with LRU support
 * Responsible for managing page-related cache data with a maximum of 10 pages
 */
export class CacheManager {
    private static readonly MAX_PAGES = 10;
    private static snapshots = new Map<string, { data: string; timestamp: number }>();
    private static consoleMessages = new LRUCache<ConsoleMessage[]>(CacheManager.MAX_PAGES);
    private static networkRequests = new LRUCache<NetworkRequest[]>(CacheManager.MAX_PAGES);
    private static pageNavigationTimestamps = new LRUCache<number>(CacheManager.MAX_PAGES);
    private static pageAccessOrder = new LRUCache<boolean>(CacheManager.MAX_PAGES);
    /**
     * Cache page snapshot
     */
    static cacheSnapshot(pageId: string, snapshot: string): void {
        if (!pageId || !snapshot) {
            console.warn('[CacheManager] Invalid snapshot data provided');
            return;
        }

        // Update page access order
        this.pageAccessOrder.set(pageId, true);

        this.snapshots.set(pageId, {
            data: snapshot,
            timestamp: Date.now()
        });

        // Clean up snapshots for pages that are no longer in LRU cache
        this.cleanupSnapshotsForRemovedPages();
    }

    /**
     * Get cached snapshot
     */
    static getCachedSnapshot(pageId: string, maxAge?: number): string | null {
        if (!pageId) {
            return null;
        }

        // Update page access order when accessing snapshot
        this.pageAccessOrder.set(pageId, true);

        const defaultMaxAge = maxAge || ToolSystemConfigManager.getCacheConfig().snapshotMaxAge;
        const cached = this.snapshots.get(pageId);

        if (cached && Date.now() - cached.timestamp < defaultMaxAge) {
            return cached.data;
        }

        // Clean up expired snapshots
        if (cached) {
            this.snapshots.delete(pageId);
        }

        return null;
    }

    /**
     * Add console message (supports tabId)
     */
    static addConsoleMessage(tabId: string, message: Partial<ConsoleMessage>): void {
        if (!tabId || !message) {
            console.warn('[CacheManager] Invalid console message data provided');
            return;
        }

        // Update page access order
        this.pageAccessOrder.set(tabId, true);

        const messages = this.consoleMessages.get(tabId) || [];
        const enhancedMessage: ConsoleMessage = {
            id: this.generateMessageId(),
            type: message.type || 'log',
            text: message.text || '',
            timestamp: message.timestamp || Date.now(),
            timeString: new Date(message.timestamp || Date.now()).toLocaleString(),
            navigationId: this.getCurrentNavigationId(tabId),
            level: this.getMessageLevel(message.type || 'log'),
            source: message.source || 'console'
        };

        messages.push(enhancedMessage);

        // Only keep the configured maximum number of messages
        const maxMessages = ToolSystemConfigManager.getCacheConfig().maxConsoleMessages;
        if (messages.length > maxMessages) {
            messages.splice(0, messages.length - maxMessages);
        }

        this.consoleMessages.set(tabId, messages);
    }

    /**
     * Get console messages (supports tabId)
     */
    static getConsoleMessages(tabId: string, sinceNavigation = true): ConsoleMessage[] {
        if (!tabId) {
            return [];
        }

        // Update page access order when accessing data
        this.pageAccessOrder.set(tabId, true);

        const messages = this.consoleMessages.get(tabId) || [];

        if (sinceNavigation) {
            // Only return messages after current page navigation
            const currentNavId = this.getCurrentNavigationId(tabId);
            return messages.filter(msg => msg.navigationId === currentNavId);
        }

        return messages;
    }

    /**
     * Clear console message history (supports tabId)
     */
    static clearConsoleHistory(tabId: string): void {
        if (tabId) {
            this.consoleMessages.delete(tabId);
        }
    }

    /**
     * Add network request (supports tabId)
     */
    static addNetworkRequest(tabId: string, request: Partial<NetworkRequest>): void {
        if (!tabId || !request) {
            console.warn('[CacheManager] Invalid network request data provided');
            return;
        }

        // Update page access order
        this.pageAccessOrder.set(tabId, true);

        const requests = this.networkRequests.get(tabId) || [];
        const enhancedRequest: NetworkRequest = {
            id: request.id || this.generateRequestId(),
            url: request.url || '',
            method: request.method || 'GET',
            timestamp: request.timestamp || Date.now(),
            startTime: request.startTime || new Date().toLocaleString(),
            endTime: request.endTime,
            status: request.status || 0,
            statusText: request.statusText,
            responseTime: request.responseTime,
            navigationId: this.getCurrentNavigationId(tabId),
            headers: request.headers || {},
            responseHeaders: request.responseHeaders,
            resourceType: request.resourceType || 'unknown',
            contentType: request.contentType
        };

        requests.push(enhancedRequest);

        // Only keep the configured maximum number of requests
        const maxRequests = ToolSystemConfigManager.getCacheConfig().maxNetworkRequests || 100;
        if (requests.length > maxRequests) {
            requests.splice(0, requests.length - maxRequests);
        }

        this.networkRequests.set(tabId, requests);
    }

    /**
     * Get network requests (supports tabId)
     */
    static getNetworkRequests(tabId: string, sinceNavigation = true): NetworkRequest[] {
        if (!tabId) {
            return [];
        }

        // Update page access order when accessing data
        this.pageAccessOrder.set(tabId, true);

        const requests = this.networkRequests.get(tabId) || [];

        if (sinceNavigation) {
            // Only return requests after current page navigation
            const currentNavId = this.getCurrentNavigationId(tabId);
            return requests.filter(req => req.navigationId === currentNavId);
        }

        return requests;
    }

    /**
     * Clear network request history (supports tabId)
     */
    static clearNetworkHistory(tabId: string): void {
        if (tabId) {
            this.networkRequests.delete(tabId);
        }
    }

    /**
     * Mark page navigation (supports tabId)
     */
    static markPageNavigation(tabId: string): void {
        if (tabId) {
            // Update page access order
            this.pageAccessOrder.set(tabId, true);

            this.pageNavigationTimestamps.set(tabId, Date.now());
            // Clean up old console messages and network requests
            this.clearConsoleHistory(tabId);
            this.clearNetworkHistory(tabId);
        }
    }

    /**
     * Clear snapshot cache
     */
    static clearSnapshotCache(pageId: string): void {
        if (!pageId) {
            return;
        }

        const keysToDelete = [];
        const snapshotKeys = Array.from(this.snapshots.keys());
        for (const key of snapshotKeys) {
            if (key.startsWith(pageId)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => this.snapshots.delete(key));
    }

    /**
     * Clear all page-related cache data
     */
    static clearAllPageData(pageId: string): void {
        if (pageId) {
            this.clearConsoleHistory(pageId);
            this.clearNetworkHistory(pageId);
            this.clearSnapshotCache(pageId);
            this.pageNavigationTimestamps.delete(pageId);
            this.pageAccessOrder.delete(pageId);
        }
    }

    /**
     * Clear all tab-related cache data
     */
    static clearAllTabData(tabId: string): void {
        if (tabId) {
            this.clearConsoleHistory(tabId);
            this.clearNetworkHistory(tabId);
            this.clearSnapshotCache(tabId);
            this.pageNavigationTimestamps.delete(tabId);
            this.pageAccessOrder.delete(tabId);
        }
    }

    /**
     * Get page data summary
     */
    static getPageDataSummary(pageId: string): PageDataSummary {
        if (!pageId) {
            return {
                pageId: '',
                totalConsoleMessages: 0,
                currentNavigationMessages: 0,
                hasSnapshotCache: false,
                lastActivity: 0
            };
        }

        const consoleMessages = this.getConsoleMessages(pageId, false);
        const currentNavMessages = this.getConsoleMessages(pageId, true);

        return {
            pageId,
            totalConsoleMessages: consoleMessages.length,
            currentNavigationMessages: currentNavMessages.length,
            hasSnapshotCache: this.hasSnapshotCache(pageId),
            lastActivity: this.getLastActivityTime(pageId)
        };
    }

    /**
     * Get tab data summary
     */
    static getTabDataSummary(tabId: string): TabDataSummary {
        if (!tabId) {
            return {
                tabId: '',
                totalConsoleMessages: 0,
                currentNavigationMessages: 0,
                hasSnapshotCache: false,
                lastActivity: 0
            };
        }

        const consoleMessages = this.getConsoleMessages(tabId, false);
        const currentNavMessages = this.getConsoleMessages(tabId, true);

        return {
            tabId,
            totalConsoleMessages: consoleMessages.length,
            currentNavigationMessages: currentNavMessages.length,
            hasSnapshotCache: this.hasSnapshotCache(tabId),
            lastActivity: this.getLastActivityTime(tabId)
        };
    }

    /**
     * Clean up expired cache
     */
    private static cleanupExpiredCache(): void {
        const maxAge = ToolSystemConfigManager.getCacheConfig().snapshotMaxAge;
        const now = Date.now();

        // Clean up expired snapshots
        const expiredSnapshots = Array.from(this.snapshots.entries())
            .filter(([_, snapshot]) => now - snapshot.timestamp > maxAge);

        expiredSnapshots.forEach(([key, _]) => {
            this.snapshots.delete(key);
        });
    }

    /**
     * Generate message ID
     */
    private static generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate request ID
     */
    private static generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Get message level
     */
    private static getMessageLevel(type: string): number {
        const levels = { debug: 0, log: 1, info: 2, warn: 3, error: 4, trace: 0 };
        return levels[type as keyof typeof levels] || 1;
    }

    /**
     * Get current navigation ID
     */
    private static getCurrentNavigationId(tabId: string): string {
        const timestamp = this.pageNavigationTimestamps.get(tabId) || Date.now();
        return `nav_${timestamp}`;
    }

    /**
     * Clean up snapshots for pages that are no longer in LRU cache
     */
    private static cleanupSnapshotsForRemovedPages(): void {
        const activePageIds = new Set<string>();

        // Collect all active page IDs from LRU caches
        const pageKeys = Array.from(this.pageAccessOrder.keys());
        for (const pageId of pageKeys) {
            activePageIds.add(pageId);
        }

        // Remove snapshots for pages not in active set
        const snapshotKeysToDelete: string[] = [];
        const snapshotKeys = Array.from(this.snapshots.keys());
        for (const snapshotKey of snapshotKeys) {
            const pageId = snapshotKey.split('_')[0]; // Assuming snapshot keys start with pageId
            if (!activePageIds.has(pageId)) {
                snapshotKeysToDelete.push(snapshotKey);
            }
        }

        snapshotKeysToDelete.forEach(key => this.snapshots.delete(key));
    }

    /**
     * Get cache statistics
     */
    static getCacheStats(): {
        totalPages: number;
        maxPages: number;
        consoleMessagesPages: number;
        networkRequestsPages: number;
        snapshotCount: number;
    } {
        return {
            totalPages: this.pageAccessOrder.size(),
            maxPages: this.MAX_PAGES,
            consoleMessagesPages: this.consoleMessages.size(),
            networkRequestsPages: this.networkRequests.size(),
            snapshotCount: this.snapshots.size
        };
    }

    /**
     * Check if snapshot cache exists
     */
    private static hasSnapshotCache(tabId: string): boolean {
        const snapshotKeys = Array.from(this.snapshots.keys());
        for (const key of snapshotKeys) {
            if (key.startsWith(tabId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get last activity time
     */
    private static getLastActivityTime(tabId: string): number {
        const messages = this.getConsoleMessages(tabId, false);
        if (messages.length > 0) {
            return Math.max(...messages.map(m => m.timestamp));
        }
        return 0;
    }

    /**
     * Destroy cache manager
     */
    static destroy(): void {
        this.snapshots.clear();
        this.consoleMessages.clear();
        this.networkRequests.clear();
        this.pageNavigationTimestamps.clear();
        this.pageAccessOrder.clear();
    }
}