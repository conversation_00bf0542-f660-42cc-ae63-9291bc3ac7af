import { Page, BrowserContext } from 'playwright-core';
import { ToolContext } from '../types/tool-definition';
import { TabManager } from './tab-manager';

/**
 * Tool context manager
 * Responsible for on-demand page and context retrieval, supports page-less operations
 */
export class ToolContextManager {
    private static instance: ToolContextManager | null = null;
    private tabManager: TabManager;

    private constructor() {
        this.tabManager = TabManager.getInstance();
    }

    static getInstance(): ToolContextManager {
        if (!ToolContextManager.instance) {
            ToolContextManager.instance = new ToolContextManager();
        }
        return ToolContextManager.instance;
    }

    /**
     * Create tool context
     * @param tabId Optional tab ID
     * @returns Tool context
     */
    createContext(tabId?: string): ToolContext {
        return {
            tabId,
            pageId: tabId, // For backward compatibility
            getPage: async (targetTabId?: string) => {
                const finalTabId = targetTabId || tabId;
                if (!finalTabId) {
                    return null;
                }
                return this.tabManager.getPageByTabId(finalTabId);
            },
            getContext: async (targetTabId?: string) => {
                const finalTabId = targetTabId || tabId;
                if (!finalTabId) {
                    return null;
                }
                const page = this.tabManager.getPageByTabId(finalTabId);
                return page ? page.context() : null;
            },
            getTabId: () => tabId || null
        };
    }

    /**
     * Get page (if exists)
     */
    async getPage(tabId?: string): Promise<Page | null> {
        if (!tabId) {
            return null;
        }
        return this.tabManager.getPageByTabId(tabId);
    }

    /**
     * Get context (if exists)
     */
    async getContext(tabId?: string): Promise<BrowserContext | null> {
        if (!tabId) {
            return null;
        }
        const page = this.tabManager.getPageByTabId(tabId);
        return page ? page.context() : null;
    }

    /**
     * Validate if tabId exists
     */
    validateTabId(tabId?: string): boolean {
        if (!tabId) {
            return false;
        }
        return this.tabManager.getPageByTabId(tabId) !== null;
    }

    /**
     * Get all available tabIds
     */
    getAvailableTabIds(): string[] {
        return this.tabManager.getAllTabs().map(tab => tab.tabId);
    }
}