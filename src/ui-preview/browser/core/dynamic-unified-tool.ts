import { BrowserToolRegistry } from './tool-registry';
import { DynamicSchemaGenerator } from './dynamic-schema-generator';
import { BrowserToolPromptGenerator } from './prompt-generator';
import { BatchExecutor } from './batch-executor';
import { ToolContext, ToolResponse } from '../types/tool-definition';
import { TabManager } from './tab-manager';

/**
 * Dynamic Unified Tool
 * Unifies all browser tools into a single tool interface
 */
export class DynamicUnifiedTool {
    private schemaGenerator: DynamicSchemaGenerator;
    private promptGenerator: BrowserToolPromptGenerator;

    constructor(private registry: BrowserToolRegistry) {
        this.schemaGenerator = new DynamicSchemaGenerator(registry);
        this.promptGenerator = new BrowserToolPromptGenerator(registry);
    }

    /**
     * 生成统一工具定义
     */
    generateTool() {
        return {
            name: 'browser_action',
            description: this.promptGenerator.generateToolDescription(),
            parameters: this.schemaGenerator.generateUnifiedJSONSchema(),
            handler: this.createHandler()
        };
    }

    /**
     * 创建统一处理器
     */
    private createHandler() {
        return async (params: any, context: ToolContext): Promise<ToolResponse> => {
            const { action, batch, tabId, ...actionParams } = params;

            try {
                // 验证 context 是否有效
                if (!context) {
                    throw new Error('Invalid context: context is required');
                }

                // 获取最终的 tabId
                const finalTabId = context.tabId || tabId;

                // 如果提供了 tabId，验证其有效性
                if (finalTabId) {
                    const tabManager = TabManager.getInstance();
                    const expectedPage = tabManager.getPageByTabId(finalTabId);
                    if (context.page && expectedPage && expectedPage !== context.page) {
                        throw new Error(`TabId ${finalTabId} is associated with a different page`);
                    }
                }

                // 更新上下文
                const updatedContext = { ...context, tabId: finalTabId };

                if (batch) {
                    const batchExecutor = new BatchExecutor(this.registry);
                    return await batchExecutor.executeBatch(batch, updatedContext);
                }

                if (!action) {
                    throw new Error('Must specify either action or batch parameter');
                }

                const toolDef = this.registry.getToolDefinition(action);
                if (!toolDef) {
                    const availableActions = this.registry.getAvailableActions();
                    throw new Error(`Unknown action: ${action}. Available actions: ${availableActions.join(', ')}`);
                }

                // 验证参数
                // try {
                //     toolDef.parameters.parse(actionParams);
                // } catch (error: any) {
                //     throw new Error(`Parameter validation failed for action '${action}': ${error.message}`);
                // }

                return await toolDef.handler(actionParams, updatedContext);
            } catch (error: any) {
                // 改进错误处理，提供更详细的错误信息
                const errorMessage = error instanceof Error ? error.message : String(error);
                const errorStack = error instanceof Error ? error.stack : undefined;

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
${errorMessage}${errorStack ? `\n\nStack trace:\n${errorStack}` : ''}`
                        }
                    ],
                    isError: true
                };
            }
        };
    }

    /**
     * 获取可用操作列表
     */
    getAvailableActions(): string[] {
        return this.registry.getAvailableActions();
    }

    /**
     * 获取工具数量
     */
    getToolCount(): number {
        return this.registry.getToolCount();
    }

    /**
     * 获取类别数量
     */
    getCategoryCount(): number {
        return this.registry.getCategoryCount();
    }
}