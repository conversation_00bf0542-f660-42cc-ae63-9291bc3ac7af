import { BrowserToolRegistry } from './tool-registry';
import { DynamicUnifiedTool } from './dynamic-unified-tool';
import { AutoToolRegistration } from './auto-tool-registration';
import { PageLifecycleManager } from './page-lifecycle-manager';
import { CacheManager } from './cache-manager';
import { TabManager } from './tab-manager';
import { ToolContextManager } from './tool-context-manager';
import { BrowserContext, Page } from 'playwright-core';
import { ToolContext } from '../types/tool-definition';

/**
 * Browser tool system
 * Main entry point of the system, responsible for initializing and managing all components
 */
export class BrowserToolSystem {
    private registry = new BrowserToolRegistry();
    private dynamicTool: DynamicUnifiedTool | null = null;
    private initialized = false;
    private readonly cleanupCallbacks: (() => void)[] = [];

    /**
     * Initialize system
     */
    async initialize(): Promise<void> {
        if (this.initialized) {
            return;
        }

        try {
            // 自动注册所有工具
            AutoToolRegistration.registerAllTools(this.registry);

            // 创建动态统一工具
            this.dynamicTool = new DynamicUnifiedTool(this.registry);

            this.initialized = true;
        } catch (error) {
            throw new Error(`Failed to initialize browser tool system: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 获取统一工具
     */
    getUnifiedTool() {
        if (!this.dynamicTool) {
            this.initialize();
        }

        return this.dynamicTool!.generateTool();
    }

    /**
     * 获取工具注册表
     */
    getToolRegistry(): BrowserToolRegistry {
        return this.registry;
    }

    /**
 * 创建工具执行上下文
 */
    createToolContext(tabId?: string): ToolContext {
        const contextManager = ToolContextManager.getInstance();
        const context = contextManager.createContext(tabId);

        // 如果提供了tabId，初始化页面监控
        if (tabId) {
            const tabManager = TabManager.getInstance();
            const page = tabManager.getPageByTabId(tabId);
            if (page) {
                // 初始化标签页生命周期管理
                if (!PageLifecycleManager.getPageState(tabId)) {
                    PageLifecycleManager.initializePage(page, tabId);
                    this.setupPageMonitoring(page, tabId);
                }

                // 更新标签页活跃时间
                tabManager.updateTabActivity(tabId);
            }
        }

        return context;
    }

    /**
     * 注册页面到标签页管理器
     */
    registerPage(page: Page, customTabId?: string): string {
        const tabManager = TabManager.getInstance();
        const tabId = tabManager.registerTab(page, customTabId);

        // 初始化页面生命周期管理
        if (!PageLifecycleManager.getPageState(tabId)) {
            PageLifecycleManager.initializePage(page, tabId);
            this.setupPageMonitoring(page, tabId);
        }

        return tabId;
    }

    /**
     * 获取页面诊断信息
     */
    getPageDiagnostics(tabId?: string): any {
        const tabManager = TabManager.getInstance();
        const allTabs = tabManager.getAllTabs();

        return {
            requestedTabId: tabId,
            availableTabs: allTabs.map(tab => ({
                tabId: tab.tabId,
                url: tab.url,
                title: tab.title,
                isClosed: tab.isClosed,
                createdAt: new Date(tab.createdAt).toISOString(),
                lastActiveAt: new Date(tab.lastActiveAt).toISOString()
            })),
            totalTabs: allTabs.length,
            activeTabs: allTabs.filter(tab => !tab.isClosed).length,
            requestedTabExists: tabId ? !!tabManager.getPageByTabId(tabId) : false,
            pageLifecycleState: tabId ? PageLifecycleManager.getPageState(tabId) : null
        };
    }

    /**
     * 设置标签页监控
     */
    private setupPageMonitoring(page: Page, tabId: string): void {
        // 监听控制台消息
        page.on('console', message => {
            // 只有在标签页不在加载状态时才记录消息，避免记录过渡状态的消息
            if (!PageLifecycleManager.isPageLoading(tabId)) {
                CacheManager.addConsoleMessage(tabId, {
                    type: message.type(),
                    text: message.text(),
                    timestamp: Date.now(),
                    source: 'browser-console'
                });
            }
        });

        // 监听页面错误
        page.on('pageerror', error => {
            CacheManager.addConsoleMessage(tabId, {
                type: 'error',
                text: `Page Error: ${error.message}`,
                timestamp: Date.now(),
                source: 'page-error'
            });
        });

        // 监听页面崩溃
        page.on('crash', () => {
            CacheManager.addConsoleMessage(tabId, {
                type: 'error',
                text: 'Page crashed',
                timestamp: Date.now(),
                source: 'page-crash'
            });
        });

        // 监听网络请求
        page.on('request', request => {
            const startTime = new Date().toISOString();
            CacheManager.addNetworkRequest(tabId, {
                id: request.url() + '_' + Date.now(), // 使用 URL + 时间戳作为唯一ID
                url: request.url(),
                method: request.method(),
                timestamp: Date.now(),
                startTime,
                headers: request.headers(),
                resourceType: request.resourceType()
            });
        });

        // 监听网络响应
        page.on('response', response => {
            const request = response.request();
            const endTime = new Date().toISOString();
            const requestId = request.url() + '_' + Date.now();
            const responseTime = Date.now() - (request.timing()?.startTime || Date.now());

            // 更新已存在的请求记录
            const requests = CacheManager.getNetworkRequests(tabId, false);
            const existingRequest = requests.find(req => req.url === request.url() && !req.endTime);

            if (existingRequest) {
                // 更新现有请求
                existingRequest.endTime = endTime;
                existingRequest.status = response.status();
                existingRequest.statusText = response.statusText();
                existingRequest.responseTime = responseTime;
                existingRequest.responseHeaders = response.headers();
                existingRequest.contentType = response.headers()['content-type'] || '';
            } else {
                // 创建新的请求记录
                CacheManager.addNetworkRequest(tabId, {
                    id: requestId,
                    url: request.url(),
                    method: request.method(),
                    timestamp: Date.now(),
                    startTime: endTime, // 如果没有开始时间，使用结束时间
                    endTime,
                    status: response.status(),
                    statusText: response.statusText(),
                    responseTime,
                    headers: request.headers(),
                    responseHeaders: response.headers(),
                    resourceType: request.resourceType(),
                    contentType: response.headers()['content-type'] || ''
                });
            }
        });

        // 监听请求失败
        page.on('requestfailed', request => {
            const endTime = new Date().toISOString();
            const responseTime = Date.now() - (request.timing()?.startTime || Date.now());

            // 更新已存在的请求记录
            const requests = CacheManager.getNetworkRequests(tabId, false);
            const existingRequest = requests.find(req => req.url === request.url() && !req.endTime);

            if (existingRequest) {
                existingRequest.endTime = endTime;
                existingRequest.status = 'failed';
                existingRequest.statusText = 'Request Failed';
                existingRequest.responseTime = responseTime;
            } else {
                // 创建新的失败请求记录
                CacheManager.addNetworkRequest(tabId, {
                    id: request.url() + '_' + Date.now(),
                    url: request.url(),
                    method: request.method(),
                    timestamp: Date.now(),
                    startTime: endTime,
                    endTime,
                    status: 'failed',
                    statusText: 'Request Failed',
                    responseTime,
                    headers: request.headers(),
                    resourceType: request.resourceType()
                });
            }
        });

        // 监听页面关闭
        page.on('close', () => {
            // 清理相关资源
            this.cleanupTabResources(tabId);
        });
    }

    /**
     * 清理标签页资源
     */
    private cleanupTabResources(tabId: string): void {
        try {
            // 清理缓存
            CacheManager.clearAllTabData(tabId);
        } catch (error) {
        }
    }

    /**
     * 检查系统是否已初始化
     */
    isInitialized(): boolean {
        return this.initialized;
    }

    /**
     * 获取系统统计信息
     */
    getSystemStats() {
        const tabManager = TabManager.getInstance();

        return {
            initialized: this.initialized,
            toolCount: this.registry.getToolCount(),
            categoryCount: this.registry.getCategoryCount(),
            availableActions: this.registry.getAvailableActions(),
            activeTabs: tabManager.getTabCount(),
            totalTabs: tabManager.getAllTabs().length
        };
    }

    /**
     * 清理系统资源
     */
    cleanup(): void {
        if (!this.initialized) {
            return;
        }

        try {
            // 执行清理回调
            this.cleanupCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                }
            });

            // 清理标签页管理器
            const tabManager = TabManager.getInstance();
            tabManager.destroy();

            // 清理缓存管理器
            CacheManager.destroy();

            // 重置状态
            this.initialized = false;
            this.dynamicTool = null;

        } catch (error) {
        }
    }

    /**
     * 销毁系统实例
     */
    destroy(): void {
        this.cleanup();
    }
}