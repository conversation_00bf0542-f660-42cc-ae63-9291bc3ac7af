import { BrowserManager } from './browserManager';
import { Page } from 'playwright-core';
import { ExceptionCode, getExceptionMessage } from './exception-codes';
import { LoggerManager } from '../LoggerManager';
import { BrowserTools } from './browser-tools';
import { ToolResponse } from './types/tool-definition';

export interface BrowserNavigationOptions {
  /** 导航超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 等待条件，默认为 domcontentloaded */
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
  /** 是否创建新标签页，默认复用现有页面 */
  newTab?: boolean;
}

export interface BrowserInfo {
  /** 浏览器是否正在运行 */
  isRunning: boolean;
  /** 当前页面数量 */
  pageCount: number;
  /** 当前活跃页面URL */
  currentUrl?: string;
}

/**
 * 浏览器预览管理器 - 提供统一的浏览器操作接口
 */
export class BrowserPreview {
  private static browserManager = BrowserManager.getInstance();
  private static logger = new LoggerManager('BrowserPreview');

  /**
   * 导航到指定URL
   * @param url 目标URL
   * @param options 导航选项
   * @returns 使用的页面实例和标签页ID
   * @throws 当URL无效或导航失败时抛出错误
   */
  static async navigateTo(url: string, options: BrowserNavigationOptions = {}): Promise<{ page: Page; tabId: string }> {
    if (!url || !this.isValidUrl(url)) {
      throw new Error(getExceptionMessage(ExceptionCode.VALIDATION));
    }

    return await this.browserManager.navigateTo(url);
  }

  /**
   * 创建新标签页并导航到指定URL
   * @param url 目标URL
   * @param options 导航选项
   * @returns 新创建的页面实例和标签页ID
   */
  static async openNewTab(url: string, options: BrowserNavigationOptions = {}): Promise<{ page: Page; tabId: string }> {
    return this.navigateTo(url, { ...options, newTab: true });
  }

  /**
   * 安装浏览器
   * @returns Promise，安装完成时resolve
   * @throws 当安装失败时抛出错误
   */
  static async installBrowser(): Promise<void> {
    try {
      await this.browserManager.installBrowser();
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Browser installation failed:', error);
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL));
    }
  }

  /**
   * 关闭浏览器及所有页面
   * @returns Promise，关闭完成时resolve
   */
  static async closeBrowser(): Promise<void> {
    try {
      await this.browserManager.close();
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Browser close failed:', error);
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_CONTEXT));
    }
  }


  /**
 * 获取浏览器工具实例
 * @returns 浏览器工具实例
 */
  static getBrowserTools(): BrowserTools {
    return this.browserManager.getBrowserTools();
  }


  /**
   * 执行浏览器工具操作
   * @param action 操作类型
   * @param params 操作参数
   * @param tabId 可选的标签页ID
   * @returns 操作结果
   */
  static async executeBrowserAction(action: string, params: any = {}, tabId?: string): Promise<ToolResponse> {
    try {
      return await this.browserManager.executeBrowserAction(action, params, tabId);
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Browser action failed:', error);
      throw error;
    }
  }

  /**
   * 验证URL格式
   * @param url 待验证的URL
   * @returns 是否为有效URL
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      // 支持本地开发服务器格式
      return (
        /^https?:\/\/localhost(:\d+)?/.test(url) ||
        /^https?:\/\/127\.0\.0\.1(:\d+)?/.test(url) ||
        /^https?:\/\/0\.0\.0\.0(:\d+)?/.test(url)
      );
    }
  }
}

// 导出类型和接口
export { BrowserTools, ToolResponse };
export * from './types/tool-definition';