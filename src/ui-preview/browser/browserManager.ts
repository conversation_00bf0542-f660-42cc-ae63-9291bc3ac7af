import { Browser<PERSON>ontext, chromium, <PERSON>, Browser } from 'playwright-core';
import { checkBrowserInstallation, installBrowser } from './install';
import { UIPreviewManager } from '..';
import { UIBridgeEvent, IBrowserManager, UIBridgeLog } from '../types';
import path from 'path';
import fs from 'fs/promises';
import { browserConfig, getPreviewScriptUrl, SCREENSHOT_DIR, MAX_CONCURRENT_SCREENSHOTS } from './config';
import { UIBridgeEventSchema } from '../types';
import { getBrowserDataDirPath } from '@/util/paths';
import { ExceptionCode, getExceptionMessage } from './exception-codes';
import { LoggerManager } from '../LoggerManager';
import { BrowserTools } from './browser-tools';
import { ToolResponse } from './types/tool-definition';
import { TabManager, TabInfo, PageTabEvent } from './core/tab-manager';
import { CacheManager } from './core/cache-manager';

export class BrowserManager implements IBrowserManager {
  private browserContext: BrowserContext | null = null;
  private browserInstance: Browser | null = null;
  private static instance: IBrowserManager | null = null;
  private launchPromise: Promise<void> | null = null; // Launch lock
  private logger: LoggerManager;
  private browserTools: BrowserTools | null = null;
  private tabManager: TabManager;

  private constructor() {
    this.logger = new LoggerManager('BrowserManager');
    this.tabManager = TabManager.getInstance();
    this.setupTabManagerEventListeners();
  }

  static getInstance(): IBrowserManager {
    if (!BrowserManager.instance) {
      BrowserManager.instance = new BrowserManager();
    }
    return BrowserManager.instance;
  }

  /**
   * Setup TabManager event listeners for enhanced integration
   */
  private setupTabManagerEventListeners(): void {
    this.tabManager.addEventListener((event: PageTabEvent) => {
      this.handleTabManagerEvent(event);
    });
  }

  /**
   * Handle TabManager events
   */
  private handleTabManagerEvent(event: PageTabEvent): void {
    const { type, tabId, page, timestamp, metadata } = event;

    this.logger.getUIPreviewLogger().info(
      `[TabManager Event] ${type.toUpperCase()}: Tab ${tabId}`,
      { url: page.url(), timestamp, metadata }
    );

    // Handle specific event types
    switch (type) {
      case 'registered':
        this.onTabRegistered(tabId, page, metadata);
        break;
      case 'unregistered':
        this.onTabUnregistered(tabId, page, metadata);
        break;
      case 'updated':
        this.onTabUpdated(tabId, page, metadata);
        break;
      case 'activated':
        this.onTabActivated(tabId, page, metadata);
        break;
    }
  }

  /**
   * Handle tab registration
   */
  private onTabRegistered(tabId: string, page: Page, metadata?: Record<string, any>): void {
    // Register page with browser tools if not already registered
    try {
      this.getBrowserTools().getToolSystem().registerPage(page);
      this.logger.getUIPreviewLogger().info(`Tab ${tabId} registered with browser tools`);
    } catch (error) {
      this.logger.getUIPreviewLogger().warn(`Failed to register tab ${tabId} with browser tools:`, error);
    }
  }

  /**
   * Handle tab unregistration
   */
  private onTabUnregistered(tabId: string, page: Page, metadata?: Record<string, any>): void {
    // Clean up any resources associated with this tab
    this.logger.getUIPreviewLogger().info(`Tab ${tabId} unregistered, cleaning up resources`);

    // Remove page from browser tools if needed
    try {
      // Note: BrowserTools might need a method to unregister pages
      // this.getBrowserTools().getToolSystem().unregisterPage(page);
    } catch (error) {
      this.logger.getUIPreviewLogger().warn(`Failed to unregister tab ${tabId} from browser tools:`, error);
    }
  }

  /**
   * Handle tab updates
   */
  private onTabUpdated(tabId: string, page: Page, metadata?: Record<string, any>): void {
    // Handle tab updates (URL changes, title changes, etc.)
    if (metadata?.url) {
      this.logger.getUIPreviewLogger().info(`Tab ${tabId} navigated to: ${metadata.url}`);
    }
    if (metadata?.title) {
      this.logger.getUIPreviewLogger().info(`Tab ${tabId} title changed to: ${metadata.title}`);
    }
  }

  /**
   * Handle tab activation
   */
  private onTabActivated(tabId: string, page: Page, metadata?: Record<string, any>): void {
    // Handle tab activation (user interaction, focus, etc.)
    this.logger.getUIPreviewLogger().info(`Tab ${tabId} activated`);
  }

  /**
   * Get TabManager instance
   */
  getTabManager(): TabManager {
    return this.tabManager;
  }

  /**
   * Get all active tabs
   */
  getAllTabs(): TabInfo[] {
    return this.tabManager.getAllTabs();
  }

  /**
   * Get tab information by tabId
   */
  getTabInfo(tabId: string): TabInfo | null {
    return this.tabManager.getTabInfo(tabId);
  }

  /**
   * Get page by tabId
   */
  getPageByTabId(tabId: string): Page | null {
    return this.tabManager.getPageByTabId(tabId);
  }

  /**
   * Get tabId by page
   */
  getTabIdByPage(page: Page): string | null {
    return this.tabManager.getTabIdByPage(page);
  }

  /**
   * Update tab activity
   */
  updateTabActivity(tabId: string, metadata?: Record<string, any>): void {
    this.tabManager.updateTabActivity(tabId, metadata);
  }

  /**
   * Update tab metadata
   */
  updateTabMetadata(tabId: string, metadata: Record<string, any>): void {
    this.tabManager.updateTabMetadata(tabId, metadata);
  }

  /**
   * Close specific tab
   */
  async closeTab(tabId: string): Promise<void> {
    const page = this.tabManager.getPageByTabId(tabId);
    if (page && !page.isClosed()) {
      try {
        await page.close();
        this.logger.getUIPreviewLogger().info(`Tab ${tabId} closed successfully`);
      } catch (error) {
        this.logger.getUIPreviewLogger().error(`Failed to close tab ${tabId}:`, error);
        throw new Error(`Failed to close tab ${tabId}`);
      }
    } else {
      this.logger.getUIPreviewLogger().warn(`Tab ${tabId} not found or already closed`);
    }
  }

  /**
   * Switch to specific tab (bring to front)
   */
  async switchToTab(tabId: string): Promise<void> {
    const page = this.tabManager.getPageByTabId(tabId);
    if (page && !page.isClosed()) {
      try {
        await page.bringToFront();
        this.tabManager.updateTabActivity(tabId, { action: 'switched_to' });
        this.logger.getUIPreviewLogger().info(`Switched to tab ${tabId}`);
      } catch (error) {
        this.logger.getUIPreviewLogger().error(`Failed to switch to tab ${tabId}:`, error);
        throw new Error(`Failed to switch to tab ${tabId}`);
      }
    } else {
      throw new Error(`Tab ${tabId} not found or already closed`);
    }
  }

  /**
   * Get tab relationship statistics
   */
  getTabStats(): {
    totalTabs: number;
    activeTabs: number;
    closedTabs: number;
    mappingIntegrity: { isValid: boolean; issues: string[] };
  } {
    return this.tabManager.getRelationshipStats();
  }

  /**
   * Validate and repair tab relationships if needed
   */
  validateAndRepairTabRelationships(): { isValid: boolean; issues: string[]; repaired: boolean } {
    const validation = this.tabManager.validateRelationshipIntegrity();
    let repaired = false;

    if (!validation.isValid) {
      this.logger.getUIPreviewLogger().warn('Tab relationship integrity issues detected:', validation.issues);
      this.tabManager.repairRelationshipIntegrity();
      repaired = true;
      this.logger.getUIPreviewLogger().info('Tab relationship integrity repaired');
    }

    return {
      isValid: validation.isValid,
      issues: validation.issues,
      repaired
    };
  }

  /**
   * 导航到指定URL，复用现有页面或创建新页面
   */
  async navigateTo(url: string): Promise<{ page: Page; tabId: string }> {
    return this.createNewTab(url);
  }

  /**
   * 创建新TAB并直接打开指定URL
   */
  async createNewTab(url: string): Promise<{ page: Page; tabId: string }> {
    let firstLaunchMode = false;
    if (!this.browserContext) {
      firstLaunchMode = true;
    }

    await this.ensureBrowser();

    if (!this.browserContext) {
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_CONTEXT));
    }

    let page: Page | undefined;
    if (firstLaunchMode) {
      [page] = this.browserContext!.pages();
      await this.registerPage(page);
    }

    if (!page || page.isClosed()) {
      page = await this.browserContext.newPage();
    }

    let tabId: string;
    try {
      // Register with TabManager with enhanced metadata
      tabId = this.tabManager.registerTab(page, undefined, {
        source: 'browser_manager',
        createdBy: 'createNewTab',
        initialUrl: url,
        timestamp: Date.now()
      });

      await this.registerPage(page);
      this.logger.getUIPreviewLogger().info(`Created new tab with ID: ${tabId}`);
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Failed to register page:', error);
      throw new Error('Failed to register page');
    }

    try {
      await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 30000 });
    } catch (err) {
      this.logger.getUIPreviewLogger().error('Failed to navigate to URL:', err);
      // throw new Error(getExceptionMessage(ExceptionCode.BROWSER_NAVIGATION));
    }

    return { page, tabId };
  }

  /**
   * 安装浏览器
   */
  async installBrowser(): Promise<void> {
    await installBrowser(browserConfig.channel);
  }

  /**
   * 获取浏览器工具实例
   */
  getBrowserTools(): BrowserTools {
    if (!this.browserTools) {
      this.browserTools = new BrowserTools();
      this.browserTools.initialize();
    }
    return this.browserTools;
  }

  /**
   * Execute browser tool action with enhanced tab tracking
   */
  async executeBrowserAction(action: string, params: any, tabId?: string): Promise<ToolResponse> {
    const tools = this.getBrowserTools();

    // Update tab activity if tabId is provided
    if (tabId) {
      this.tabManager.updateTabActivity(tabId, {
        action: 'browser_tool_execution',
        toolAction: action,
        timestamp: Date.now()
      });
    }

    try {
      const result = await tools.execute(action, params, tabId);

      // Update tab metadata with execution result if successful
      if (tabId && !result.isError) {
        this.tabManager.updateTabMetadata(tabId, {
          lastToolExecution: {
            action,
            timestamp: Date.now(),
            success: true
          }
        });
      }

      return result;
    } catch (error) {
      // Update tab metadata with execution error
      if (tabId) {
        this.tabManager.updateTabMetadata(tabId, {
          lastToolExecution: {
            action,
            timestamp: Date.now(),
            success: false,
            error: error instanceof Error ? error.message : String(error)
          }
        });
      }
      throw error;
    }
  }

  /**
   * 确保浏览器已启动
   */
  private async ensureBrowser(): Promise<void> {
    if (!this.browserContext) {
      // 如果已经有启动过程在进行中，等待它完成
      if (this.launchPromise) {
        await this.launchPromise;
        return;
      }

      // 创建新的启动Promise
      this.launchPromise = this.launch();
      try {
        await this.launchPromise;
      } finally {
        // 启动完成后清理Promise引用
        this.launchPromise = null;
      }
    }
  }

  /**
   * 启动浏览器
   */
  private async launch(): Promise<void> {
    try {
      let channel = browserConfig.channel;
      if ((await checkBrowserInstallation('chrome')).isInstalled) {
        channel = 'chrome';
      }

      if (!(await checkBrowserInstallation(channel)).isInstalled) {
        installBrowser(channel);
        throw new Error(getExceptionMessage(ExceptionCode.BROWSER_NOT_INSTALLED));
      }

      // 尝试使用配置的 userDataDir 启动
      try {
        await this.tryLaunchWithUserDataDir(getBrowserDataDirPath(browserConfig.storageName), channel);
      } catch (error) {
        // 如果启动失败，尝试使用空的 userDataDir
        this.logger.getUIPreviewLogger().warn('Failed to launch with userDataDir, trying with empty userDataDir:', error);
        await this.tryLaunchWithUserDataDir('', channel);
      }
    } catch (error) {
      // 启动失败时清理Promise引用，允许重试
      this.launchPromise = null;
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_LAUNCH));
    }
  }

  /**
   * 尝试使用指定的 userDataDir 启动浏览器
   */
  private async tryLaunchWithUserDataDir(userDataDir: string, channel: string): Promise<void> {
    this.browserContext = await chromium.launchPersistentContext(
      userDataDir, {
      channel,
      viewport: null,
      headless: false,
      devtools: false,
      handleSIGINT: false,
      handleSIGTERM: false,
      handleSIGHUP: false,
      args: [
        '--disable-backgrounding-occluded-windows',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-component-update',
        '--no-default-browser-check',
        '--disable-default-apps',
        '--disable-dev-shm-usage',
        '--allow-pre-commit-input',
        '--disable-hang-monitor',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--no-first-run',
        '--password-store=basic',
        '--disable-blink-features=AutomationControlled',
        // 禁用翻译功能
        '--disable-translate',
        '--disable-features=Translate',
        // 禁用恢复页面和会话功能
        '--disable-session-crashed-bubble',
        '--disable-infobars',
        '--no-crash-upload',
        '--disable-breakpad',
        '--disable-crash-reporter',
        '--disable-restore-session-state',
        '--disable-background-mode',
        // 禁用其他干扰功能
        '--disable-notifications',
        '--disable-extensions',
        '--disable-plugins',
        '--no-sandbox',
        '--disable-web-security'
      ]
    });

    // Set up page event listeners with TabManager integration
    this.browserContext.on('page', (page) => {
      // Register new page with TabManager
      try {
        const tabId = this.tabManager.registerTab(page, undefined, {
          source: 'browser_context',
          createdBy: 'context_page_event',
          timestamp: Date.now()
        });
        this.logger.getUIPreviewLogger().info(`Auto-registered new page with tab ID: ${tabId}`);
      } catch (error) {
        this.logger.getUIPreviewLogger().error('Failed to auto-register page with TabManager:', error);
      }

      // Register page with browser tools
      this.registerPage(page).catch(error =>
        this.logger.getUIPreviewLogger().error('Failed to register page:', error)
      );
    });

    this.browserContext.on('close', () => {
      // Clean up TabManager when browser context closes
      this.logger.getUIPreviewLogger().info('Browser context closing, cleaning up TabManager and cache');
      this.tabManager.clear();
      // Clear all cache data when browser closes
      CacheManager.destroy();
      this.close();
    });
  }

  /**
   * 确保截图目录存在
   */
  private async ensureScreenshotDirectory(): Promise<void> {
    try {
      await fs.mkdir(SCREENSHOT_DIR, { recursive: true });
    } catch (error) {
      const err = error as NodeJS.ErrnoException;
      // 忽略目录已存在的错误
      if (err.code !== 'EEXIST') {
        throw new Error(getExceptionMessage(ExceptionCode.BROWSER_SCREENSHOT));
      }
    }
  }

  /**
   * 注册页面事件和脚本注入
   */
  private async registerPage(page: Page): Promise<void> {
    try {
      // 检查是否为iframe，如果是iframe则不注册
      const isMainFrame = await page.evaluate(() => {
        return window.self === window.top;
      });

      if (!isMainFrame) {
        return;
      }


      this.getBrowserTools().getToolSystem().registerPage(page);

      // 暴露桥接函数
      await page.exposeFunction('__ui_bridge_send__', (event: UIBridgeEvent) => {
        this.handleUIBridgeEvent(page, event);
      });

      // 暴露桥接函数
      await page.exposeFunction('__ui_bridge_log__', (event: UIBridgeLog) => {
        this.handleUIBridgeLogEvent(event);
      });


      // 注入初始化脚本
      // await this.injectInitScript(page);

      // 设置页面事件监听
      this.setupPageEventListeners(page);

    } catch (error) {
      // throw new Error(getExceptionMessage(ExceptionCode.BROWSER_SCRIPT));
      this.logger.getUIPreviewLogger().error('Failed to register page:', error);
    }
  }
  /**
   * 处理 bridge 事件
   */
  private async handleUIBridgeEvent(page: Page, event: UIBridgeEvent): Promise<void> {
    // 参数校验
    const parseResult = UIBridgeEventSchema.safeParse(event);
    if (!parseResult.success) {
      this.logger.getUIPreviewLogger().warn('UIBridgeEvent 参数校验失败:', parseResult.error);
      this.logger.getUIPreviewLogger().info('Event data:', event);
      return;
    }

    this.handleUIBridgeErrors(page, event);
    this.handleUIBridgeElementAria(page, event);
    this.handleUIBridgeElementScreenshots(page, event);
  }

  private async handleUIBridgeLogEvent(event: UIBridgeLog) {
    UIPreviewManager.getInstance().reportUserAction(event);
  }

  /**
   * 处理 UI Bridge 错误信息
   */
  private handleUIBridgeErrors(page: Page, event: UIBridgeEvent): void {
    const errors = event.data.filter(item => item.type === 'error');
    if (errors.length === 0) return;

    // 获取当前页面的 tabId
    const tabId = this.tabManager.getTabIdByPage(page);

    // 为每个错误添加 tabId 信息
    const enhancedErrors = errors.map(error => {
      if (tabId) {
        error.tabId = tabId;
      }
      return error;
    });

    UIPreviewManager.getInstance().sendInfo(enhancedErrors);
  }

  /**
   * 处理 UI Bridge 元素的 ariaSnapshot 并发送
   */
  private async handleUIBridgeElementAria(page: Page, event: UIBridgeEvent): Promise<void> {
    const elements = event.data.filter(item => item.type === 'element');
    if (elements.length === 0) return;

    // 获取当前页面的 tabId
    const tabId = this.tabManager.getTabIdByPage(page);

    const ariaSnapshots = await Promise.all(elements.map(item => {
      if (!item.selector) {
        return Promise.resolve(null);
      }
      try {
        return page.locator(item.selector).ariaSnapshot({ timeout: 1000 });
      } catch (error) {
        return Promise.resolve(null);
      }
    }));
    const newElements = elements.map((item, index) => {
      item.ariaSnapshot = ariaSnapshots[index] || '';
      // 为每个元素添加 tabId 信息
      if (tabId) {
        item.tabId = tabId;
      }
      return item;
    });
    UIPreviewManager.getInstance().sendInfo(newElements);
  }

  /**
   * 处理 UI Bridge 元素截图并发
   */
  private async handleUIBridgeElementScreenshots(page: Page, event: UIBridgeEvent): Promise<void> {
    const elementScreenshots = event.data
      .filter(item => item.type === 'element')
      .map(item => this.handleElementScreenshot(page, item as any));
    await this.processConcurrentScreenshots(elementScreenshots);
  }

  /**
   * 处理并发截图
   */
  private async processConcurrentScreenshots(screenshots: Promise<void>[]): Promise<void> {
    // 分批处理截图，避免过多并发
    for (let i = 0; i < screenshots.length; i += MAX_CONCURRENT_SCREENSHOTS) {
      const batch = screenshots.slice(i, i + MAX_CONCURRENT_SCREENSHOTS);
      await Promise.allSettled(batch);
    }
  }

  /**
   * 处理单个元素截图
   */
  private async handleElementScreenshot(page: Page, item: any): Promise<void> {
    try {
      if (!item.selector) {
        return;
      }

      const screenshotBuffer = await page.locator(item.selector).screenshot({
        timeout: 1000
      });

      if (!screenshotBuffer || screenshotBuffer.length === 0) {
        return;
      }

      await this.saveScreenshot(screenshotBuffer);

    } catch (error) {
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_SCREENSHOT));
    }
  }

  /**
   * 保存截图
   */
  private async saveScreenshot(buffer: Buffer): Promise<void> {
    try {
      // 每次保存前判断截图目录是否存在，不存在则新建
      try {
        await fs.access(SCREENSHOT_DIR);
      } catch {
        await this.ensureScreenshotDirectory();
      }

      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 8);
      const filename = `screenshot-${timestamp}-${randomId}.png`;
      const tempPath = path.join(SCREENSHOT_DIR, filename);

      await fs.writeFile(tempPath, buffer);

      UIPreviewManager.getInstance().sendImage({
        type: 'image',
        screenshot: tempPath
      });

    } catch (error) {
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_SCREENSHOT));
    }
  }

  /**
   * 注入初始化脚本
   */
  private async injectInitScript(page: Page): Promise<void> {
    await page.addInitScript(
      ({ scriptUrl }) => {
        // 防止重复注入
        if ((window as any).__ui_bridge_loaded__) return;
        (window as any).__ui_bridge_loaded__ = true;

        function loadScript(src: string): Promise<boolean> {
          return new Promise((resolve, reject) => {
            // 用完整 src 匹配，防止重复加载
            if ([...document.scripts].some(s => s.src === src)) {
              resolve(true);
              return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.onload = () => resolve(true);
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
          });
        }

        const loadScriptSafely = () => {
          loadScript(scriptUrl).catch((error) => {
            // 在浏览器环境中无法直接访问 logger，使用 console.error 作为后备
            console.error('Failed to load script:', error);
          });
        };

        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', loadScriptSafely);
        } else {
          loadScriptSafely();
        }
      },
      { scriptUrl: getPreviewScriptUrl() }
    );
  }

  /**
   * 设置页面事件监听
   */
  private setupPageEventListeners(page: Page): void {
    // 页面加载后确保脚本存在
    page.on('load', () => {
      this.ensureScriptLoaded(page).catch(error =>
        this.logger.getUIPreviewLogger().warn('Failed to ensure script after page load:', error)
      );
    });

    // 页面导航后确保脚本存在
    page.on('framenavigated', () => {
      this.ensureScriptLoaded(page).catch(error =>
        this.logger.getUIPreviewLogger().warn('Failed to ensure script after navigation:', error)
      );
    });

    // Page close cleanup with TabManager integration
    page.on('close', () => {
      const tabId = this.tabManager.getTabIdByPage(page);
      if (tabId) {
        this.logger.getUIPreviewLogger().info(`Page closed, tab ${tabId} will be unregistered`);
        // TabManager will automatically handle unregistration via its own page close listener
      }
      page.removeAllListeners();
    });
  }

  /**
   * 确保脚本已加载
   */
  private async ensureScriptLoaded(page: Page): Promise<void> {
    try {
      await page.waitForLoadState('domcontentloaded', { timeout: 10000 });
      await page.evaluate((scriptUrl) => {
        setTimeout(() => {
          if (window.location.hostname.includes('google.com')) {
            return;
          }

          const existingScript = document.querySelector(`script[src*="${scriptUrl.split('?')[0]}"]`);
          if (!existingScript) {
            const script = document.createElement('script');
            script.src = scriptUrl;
            document.head.appendChild(script);
          }
        }, 0);
      }, getPreviewScriptUrl());
    } catch (error) {
      // 静默处理错误，避免阻塞正常流程
      // 可以选择记录日志
    }
  }

  /**
   * Close browser and all resources with enhanced TabManager cleanup
   */
  async close(): Promise<void> {
    try {
      // Clear launch promise
      this.launchPromise = null;

      // Get tab statistics before cleanup
      const stats = this.tabManager.getRelationshipStats();
      this.logger.getUIPreviewLogger().info('Closing browser with tab stats:', stats);

      if (this.browserContext) {
        // Close all pages
        const pages = this.browserContext.pages();
        await Promise.allSettled(pages.map(page => page.close()));

        // Close browser context
        await this.browserContext.close();
        this.browserContext = null;
      }

      if (this.browserInstance) {
        await this.browserInstance.close();
        this.browserInstance = null;
      }

      // Clean up TabManager
      this.tabManager.clear();
      this.logger.getUIPreviewLogger().info('TabManager cleared');

      // Clean up browser tools
      if (this.browserTools) {
        // Note: BrowserTools might need a cleanup method
        this.browserTools = null;
      }

      // Clear instance
      BrowserManager.instance = null;
      this.logger.getUIPreviewLogger().info('BrowserManager instance cleared');
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Error during browser close:', error);
    }
  }
}