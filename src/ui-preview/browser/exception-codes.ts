// Exception/Error code definitions for the project (numeric codes)

export enum ExceptionCode {
    UNKNOWN = 1000,
    NETWORK = 1001,
    VALIDATION = 1002,
    NOT_FOUND = 1003,
    PERMISSION_DENIED = 1004,
    TIMEOUT = 1005,
    CONFLICT = 1006,
    UNAUTHORIZED = 1007,
    INTERNAL = 1008,
    BROWSER_LAUNCH = 2001,
    BROWSER_CONTEXT = 2002,
    BROWSER_INSTALL = 2003,
    BROWSER_NAVIGATION = 2004,
    BROWSER_SCREENSHOT = 2005,
    BROWSER_SCRIPT = 2006,
    BROWSER_NOT_INSTALLED = 2007,
    // ... add more as needed
}

export const ExceptionMessages: Record<ExceptionCode, string> = {
    [ExceptionCode.UNKNOWN]: 'Unknown error',
    [ExceptionCode.NETWORK]: 'Network error',
    [ExceptionCode.VALIDATION]: 'Validation failed',
    [ExceptionCode.NOT_FOUND]: 'Resource not found',
    [ExceptionCode.PERMISSION_DENIED]: 'Permission denied',
    [ExceptionCode.TIMEOUT]: 'Operation timed out',
    [ExceptionCode.CONFLICT]: 'Resource conflict',
    [ExceptionCode.UNAUTHORIZED]: 'Unauthorized',
    [ExceptionCode.INTERNAL]: 'Internal error',
    [ExceptionCode.BROWSER_LAUNCH]: 'Browser launch failed',
    [ExceptionCode.BROWSER_CONTEXT]: 'Browser context error',
    [ExceptionCode.BROWSER_INSTALL]: 'Browser installation failed',
    [ExceptionCode.BROWSER_NAVIGATION]: 'Page navigation failed',
    [ExceptionCode.BROWSER_SCREENSHOT]: 'Screenshot failed',
    [ExceptionCode.BROWSER_SCRIPT]: 'Script injection failed',
    [ExceptionCode.BROWSER_NOT_INSTALLED]: 'Browser is not installed',
    // ... add more as needed
};

// Helper to get message by code
export function getExceptionMessage(code: ExceptionCode): string {
    return `[${code}] Failed to launch browser`;
} 