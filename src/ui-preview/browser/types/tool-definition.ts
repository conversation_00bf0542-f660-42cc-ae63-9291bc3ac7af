import { ZodSchema } from 'zod';
import { Page, BrowserContext } from 'playwright-core';

/**
 * Tool category enumeration
 */
export enum ToolCategory {
    NAVIGATION = 'Page Navigation',
    INTERACTION = 'Page Interaction',
    EVALUATION = 'Evaluation Tools',
    SCREENSHOT = 'Screenshot Tools',
    DEBUG = 'Debug Tools',
    WAIT = 'Wait Tools'
}

/**
 * Tool capability enumeration
 */
export enum ToolCapability {
    CORE = 'core',
    VISION = 'vision',
    CORE_TABS = 'core-tabs'
}

/**
 * Tool execution context
 */
export interface ToolContext {
    page?: Page;
    context?: BrowserContext;
    pageId?: string;
    tabId?: string;
    // 提供获取页面和上下文的方法
    getPage?: (tabId?: string) => Promise<Page | null>;
    getContext?: (tabId?: string) => Promise<BrowserContext | null>;
    getTabId?: () => string | null;
}

/**
 * Tool handler function
 */
export type ToolHandler = (params: any, context: ToolContext) => Promise<ToolResponse>;

/**
 * Tool response content
 */
export interface TextContent {
    type: 'text';
    text: string;
}

export interface ImageContent {
    type: 'image';
    source: {
        type: 'url',
        url: string
    }
}

export type Content = TextContent | ImageContent;

/**
 * Tool response format
 */
export interface ToolResponse {
    content: Content[];
    isError?: boolean;
}

/**
 * Tool usage example
 */
export interface ToolExample {
    description: string;
    params: Record<string, any>;
    expectedResult: string;
}

/**
 * Browser tool definition
 */
export interface BrowserToolDefinition {
    name: string;
    description: string;
    category: ToolCategory;
    capability: ToolCapability;
    parameters: ZodSchema;
    examples: ToolExample[];
    handler: ToolHandler;
}

/**
 * Batch action
 */
export interface BatchAction {
    action: string;
    params: Record<string, any>;
    continueOnError?: boolean;
}

/**
 * 控制台消息
 */
export interface ConsoleMessage {
    id: string;
    type: string;
    text: string;
    timestamp: number;
    timeString: string;
    navigationId: string;
    level: number;
    source: string;
}

/**
 * 网络请求
 */
export interface NetworkRequest {
    id: string;
    url: string;
    method: string;
    timestamp: number;
    startTime: string;
    endTime?: string;
    status: number | string;
    statusText?: string;
    responseTime?: number;
    navigationId: string;
    headers: Record<string, string>;
    responseHeaders?: Record<string, string>;
    resourceType: string;
    contentType?: string;
}

/**
 * 分页信息
 */
export interface PaginationInfo {
    currentPage: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
}

/**
 * 标签页状态
 */
export interface TabState {
    tabId: string;
    url: string;
    navigationCount: number;
    lastNavigationTime: number;
    isLoading: boolean;
}

/**
 * 模态状态
 */
export interface ModalState {
    type: 'dialog' | 'file-chooser';
    message?: string;
}

/**
 * 页面快照
 */
export interface PageSnapshot {
    id: string;
    url: string;
    title: string;
    timestamp: string;
    navigationId: string;
    elements: SnapshotElement[];
    metadata: {
        viewport: { width: number; height: number };
        userAgent: string;
        loadTime: number;
        elementCount: number;
    };
}

/**
 * 快照元素
 */
export interface SnapshotElement {
    id: string;
    tag: string;
    role?: string;
    name?: string;
    text?: string;
    value?: string;
    attributes: Record<string, string>;
    position: { x: number; y: number; width: number; height: number };
    children?: SnapshotElement[];
}

/**
 * 截图数据
 */
export interface ScreenshotData {
    id: string;
    data: string;
    format: 'png' | 'jpeg';
    dimensions: {
        width: number;
        height: number;
    };
    timestamp: string;
    navigationId: string;
    metadata: {
        quality: number;
        fullPage: boolean;
        element?: string;
        clip?: ClipOptions;
        fileSize: number;
        filename?: string;
    };
}

/**
 * 截图区域选项
 */
export interface ClipOptions {
    x: number;
    y: number;
    width: number;
    height: number;
}

/**
 * 元素参数
 */
export interface ElementParams {
    element?: string;
    ref?: string;
}

/**
 * 页面数据摘要
 */
export interface PageDataSummary {
    pageId: string;
    totalConsoleMessages: number;
    currentNavigationMessages: number;
    hasSnapshotCache: boolean;
    lastActivity: number;
}

/**
 * 标签页数据摘要
 */
export interface TabDataSummary {
    tabId: string;
    totalConsoleMessages: number;
    currentNavigationMessages: number;
    hasSnapshotCache: boolean;
    lastActivity: number;
}