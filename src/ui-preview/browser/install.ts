// @ts-expect-error: no type declaration for playwright-core/lib/server/registry/index
import { registry } from 'playwright-core/lib/server/registry/index';
import fs from 'fs/promises';
import { createInstaller } from './PWInstaller';
import browsersJson from './browsers.json';
import { ExceptionCode, getExceptionMessage } from './exception-codes';
import { LoggerManager } from '../LoggerManager';

// 类型定义
export type SupportedBrowser = 'chromium' | 'chrome' | 'msedge';

export interface BrowserInstallOptions {
  /** 是否强制重新安装，默认为 false */
  force?: boolean;
  /** 是否显示详细日志，默认为 true */
  verbose?: boolean;
  /** 安装超时时间（毫秒），默认为 5 分钟 */
  timeout?: number;
}

export interface BrowserInstallResult {
  /** 是否成功安装 */
  success: boolean;
  /** 浏览器名称 */
  browserName: SupportedBrowser;
  /** 可执行文件路径 */
  executablePath?: string;
  /** 安装目录 */
  installDirectory?: string;
  /** 是否是新安装（false表示已存在） */
  isNewInstall: boolean;
  /** 错误信息（如果安装失败） */
  error?: string;
  /** 安装耗时（毫秒） */
  duration: number;
}

export interface BrowserInfo {
  /** 浏览器名称 */
  name: SupportedBrowser;
  /** 是否已安装 */
  isInstalled: boolean;
  /** 可执行文件路径 */
  executablePath?: string;
  /** 安装目录 */
  directory?: string;
  /** 版本信息 */
  version?: string;
  /** 错误信息 */
  error?: string;
}

/**
 * 浏览器安装管理器
 */
export class BrowserInstaller {
  private static readonly DEFAULT_TIMEOUT = 5 * 60 * 1000; // 5分钟
  private static readonly SUPPORTED_BROWSERS: SupportedBrowser[] = ['chromium', 'chrome', 'msedge'];
  private static logger = new LoggerManager('BrowserInstaller');

  /**
   * 安装指定的 Playwright 浏览器
   * @param browserName 浏览器名称
   * @param options 安装选项
   * @returns 安装结果
   */
  static async install(
    browserName: SupportedBrowser,
    options: BrowserInstallOptions = {}
  ): Promise<BrowserInstallResult> {
    const startTime = Date.now();
    const { force = false, verbose = true, timeout = this.DEFAULT_TIMEOUT } = options;

    // 验证浏览器名称
    if (!this.SUPPORTED_BROWSERS.includes(browserName)) {
      const error = getExceptionMessage(ExceptionCode.VALIDATION) + `: Unsupported browser: ${browserName}. Supported browsers: ${this.SUPPORTED_BROWSERS.join(', ')}`;
      return this.createFailureResult(browserName, error, startTime);
    }

    try {
      // 1. 获取浏览器可执行文件信息
      const executable = registry.findExecutable(browserName);
      if (!executable) {
        const error = getExceptionMessage(ExceptionCode.NOT_FOUND) + `: Failed to find executable configuration for ${browserName}`;
        return this.createFailureResult(browserName, error, startTime);
      }

      // 2. 检查是否已安装
      const isInstalled = await this.checkInstallation(executable);
      const executablePath = executable.executablePath?.();

      if (isInstalled && !force) {
        return {
          success: true,
          browserName,
          executablePath,
          installDirectory: executable.directory,
          isNewInstall: false,
          duration: Date.now() - startTime
        };
      }

      // 3. 执行安装
      const installResult = await this.performInstallation(executable, timeout, verbose);

      if (installResult.success) {
        return {
          success: true,
          browserName,
          executablePath,
          installDirectory: executable.directory,
          isNewInstall: true,
          duration: Date.now() - startTime
        };
      } else {
        return this.createFailureResult(browserName, installResult.error || 'Unknown installation error', startTime);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : getExceptionMessage(ExceptionCode.UNKNOWN);
      this.logger.getUIPreviewLogger().error('Browser installation failed:', error);
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + errorMessage);
    }
  }

  /**
   * 检查浏览器是否已安装
   * @param browserName 浏览器名称
   * @returns 浏览器信息
   */
  static async checkBrowser(browserName: SupportedBrowser): Promise<BrowserInfo> {
    try {
      const executable = registry.findExecutable(browserName);
      if (!executable) {
        return {
          name: browserName,
          isInstalled: false,
          error: getExceptionMessage(ExceptionCode.NOT_FOUND)
        };
      }

      const isInstalled = await this.checkInstallation(executable);
      const executablePath = executable.executablePath?.();

      return {
        name: browserName,
        isInstalled,
        executablePath,
        directory: executable.directory,
      };
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Browser check failed:', error);
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + (error instanceof Error ? error.message : getExceptionMessage(ExceptionCode.UNKNOWN)));
    }
  }

  /**
   * 检查所有支持的浏览器状态
   * @returns 所有浏览器的信息数组
   */
  static async checkAllBrowsers(): Promise<BrowserInfo[]> {
    const results = await Promise.allSettled(
      this.SUPPORTED_BROWSERS.map(browser => this.checkBrowser(browser))
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + (result.reason instanceof Error ? result.reason.message : getExceptionMessage(ExceptionCode.UNKNOWN)));
      }
    });
  }

  /**
   * 卸载指定浏览器（清理安装目录）
   * @param browserName 浏览器名称
   * @returns 是否成功卸载
   */
  static async uninstall(browserName: SupportedBrowser): Promise<boolean> {
    try {
      const executable = registry.findExecutable(browserName);
      if (!executable || !executable.directory) {
        return true; // 认为已经卸载
      }

      const directory = executable.directory;
      const exists = await fs.access(directory).then(() => true).catch(() => false);

      if (exists) {
        await fs.rm(directory, { recursive: true, force: true });
      }

      return true;
    } catch (error) {
      this.logger.getUIPreviewLogger().error('Browser uninstall failed:', error);
      throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + (error instanceof Error ? error.message : getExceptionMessage(ExceptionCode.UNKNOWN)));
    }
  }

  /**
   * 检查浏览器是否已安装
   */
  private static async checkInstallation(executable: any): Promise<boolean> {
    try {
      const executablePath = executable.executablePath?.();
      if (!executablePath) {
        return false;
      }

      // 使用异步检查文件存在性
      await fs.access(executablePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 执行浏览器安装
   */
  private static async performInstallation(
    executable: any,
    timeout: number,
    verbose: boolean
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const packageJson = require('playwright-core/package.json') as { version: string };
      const installBrowser = createInstaller(browsersJson as any, packageJson.version);

      // 使用 Promise.race 实现超时控制
      const installPromise = installBrowser([executable.name]);
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Installation timeout after ${timeout}ms`)), timeout);
      });

      await Promise.race([installPromise, timeoutPromise]);

      // 验证安装是否成功
      const isInstalled = await this.checkInstallation(executable);
      if (!isInstalled) {
        return { success: false, error: 'Installation completed but browser executable not found' };
      }

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown installation error';
      this.logger.getUIPreviewLogger().error('Browser installation failed:', error);
      return { success: false, error: getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + errorMessage };
    }
  }

  /**
   * 创建失败结果对象
   */
  private static createFailureResult(
    browserName: SupportedBrowser,
    error: string,
    startTime: number
  ): BrowserInstallResult {
    throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + error);
  }
}

/**
 * 安装指定 Playwright 浏览器（简化接口，向后兼容）
 * @param browserName 浏览器名称
 * @param options 安装选项
 * @returns Promise，安装完成时resolve
 */
export async function installBrowser(
  browserName: SupportedBrowser,
  options?: BrowserInstallOptions
): Promise<void> {
  const result = await BrowserInstaller.install(browserName, options);

  if (!result.success) {
    throw new Error(getExceptionMessage(ExceptionCode.BROWSER_INSTALL) + ': ' + (result.error || `Failed to install ${browserName}`));
  }
}

/**
 * 检查浏览器安装状态
 * @param browserName 浏览器名称
 * @returns 浏览器信息
 */
export async function checkBrowserInstallation(browserName: SupportedBrowser): Promise<BrowserInfo> {
  return BrowserInstaller.checkBrowser(browserName);
}

/**
 * 获取所有支持的浏览器列表
 * @returns 支持的浏览器名称数组
 */
export function getSupportedBrowsers(): SupportedBrowser[] {
  return [...BrowserInstaller['SUPPORTED_BROWSERS']];
}

// 默认导出向后兼容
export default installBrowser;
