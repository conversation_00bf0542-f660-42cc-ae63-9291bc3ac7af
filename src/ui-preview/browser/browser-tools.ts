import { BrowserToolSystem } from './core/browser-tool-system';
import { ToolResponse } from './types/tool-definition';

/**
 * 浏览器工具主接口
 * 提供统一的浏览器自动化工具访问接口
 */
export class BrowserTools {
    private toolSystem = new BrowserToolSystem();
    private initialized = false;

    /**
     * 初始化浏览器工具
     */
    initialize() {
        if (this.initialized) {
            return;
        }

        this.toolSystem.initialize();
        this.initialized = true;
    }

    /**
     * 执行浏览器操作
     */
    async execute(
        action: string,
        params: any,
        tabId?: string
    ): Promise<ToolResponse> {
        this.ensureInitialized();

        // 创建工具上下文（tabId可选）
        const toolContext = this.toolSystem.createToolContext(tabId);
        const unifiedTool = this.toolSystem.getUnifiedTool();

        const timeout = 30 * 1000;
        let timeoutId: NodeJS.Timeout;

        const timeoutPromise = new Promise<ToolResponse>((resolve) => {
            timeoutId = setTimeout(() => {
                resolve({
                    content: [{
                        type: 'text',
                        text: `Tool execution timeout after ${timeout / 1000} seconds`
                    }],
                    isError: true
                });
            }, timeout);
        });

        const executePromise = unifiedTool.handler({ action, ...params, tabId }, toolContext);

        try {
            const result = await Promise.race([executePromise, timeoutPromise]);
            return result;
        } finally {
            if (timeoutId!) {
                clearTimeout(timeoutId);
            }
        }
    }

    /**
     * 获取统一工具定义（用于AI模型）
     */
    getUnifiedToolDefinition() {
        this.ensureInitialized();
        return this.toolSystem.getUnifiedTool();
    }

    /**
     * 获取可用操作列表
     */
    getAvailableActions(): string[] {
        this.ensureInitialized();
        return this.toolSystem.getToolRegistry().getAvailableActions();
    }

    /**
     * 获取系统统计信息
     */
    getSystemStats() {
        this.ensureInitialized();
        return this.toolSystem.getSystemStats();
    }

    /**
     * 确保系统已初始化
     */
    private ensureInitialized(): void {
        if (!this.initialized) {
            this.initialize();
        }
    }

    getToolSystem(): BrowserToolSystem {
        return this.toolSystem;
    }
}

/**
 * 创建浏览器工具实例
 */
export async function createBrowserTools(): Promise<BrowserTools> {
    const tools = new BrowserTools();
    tools.initialize();
    return tools;
}