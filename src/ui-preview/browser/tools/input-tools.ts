import { z } from 'zod';
import { <PERSON>rowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ElementResolver } from '../core/element-resolver';
import { ElementValidator } from '../core/element-validator';
import { ToolHelpers } from '../core/tool-helpers';
import { createRefTool } from './common-tools';

/**
 * Input tools collection
 */
export const InputTools: Record<string, BrowserToolDefinition> = createRefTool({
    browserType: {
        name: 'browser_type',
        description: 'Type text into editable elements on the web page using CSS selector or precise ref from snapshot.',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            text: z.string().describe('Text to type into the element'),
            submit: z.boolean().optional().default(false).describe('Whether to submit the form after typing (press Enter)'),
            slowly: z.boolean().optional().default(false).describe('Whether to type character by character with delays'),
            clear: z.boolean().optional().default(true).describe('Whether to clear the field before typing')
        }),
        examples: [
            {
                description: 'Type text using precise ref',
                params: { ref: 'e1', text: '<EMAIL>' },
                expectedResult: 'Successfully typed "<EMAIL>" into element'
            },
        ],
        handler: async (params, context) => {
            try {
                // 验证参数
                if (!params.selector && !params.ref) {
                    throw new Error('Either selector or ref parameter is required');
                }

                if (!params.text || params.text.trim() === '') {
                    throw new Error('Text parameter is required and cannot be empty');
                }

                const page = await ToolHelpers.requirePage(context);

                // 使用改进的元素解析器
                const locator = await ElementResolver.resolveElementToLocator(page, params, context);

                // 滚动元素到视图中（如果需要）
                await locator.scrollIntoViewIfNeeded();

                // 聚焦元素
                await locator.focus();

                // 如果需要清空，先清空内容
                if (params.clear !== false) {
                    await locator.clear();
                }

                // 根据slowly参数决定输入方式
                if (params.slowly) {
                    // 逐字符输入，模拟真实用户输入
                    await locator.type(params.text, { delay: 100 });
                } else {
                    // 直接填充文本
                    await locator.fill(params.text);
                }

                // 如果需要提交，按Enter键
                if (params.submit) {
                    await locator.press('Enter');
                }

                const elementDesc = params.selector || params.ref || 'element';
                const submitText = params.submit ? ' and submitted' : '';
                const clearText = params.clear !== false ? ' (cleared first)' : '';

                // 生成 Playwright 代码
                const selectorString = params.ref ? `aria-ref=${params.ref}` : params.selector || 'element';
                let code: string;
                if (params.slowly) {
                    code = `await page.locator('${selectorString}').type('${params.text}', { delay: 100 });`;
                } else {
                    code = `await page.locator('${selectorString}').fill('${params.text}');`;
                }

                if (params.submit) {
                    code += `\nawait page.locator('${selectorString}').press('Enter');`;
                }

                const tabId = ToolHelpers.getTabId(context);
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully typed "${params.text}" into ${elementDesc}${clearText}${submitText}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                // 检查是否是元素引用错误
                if (errorMessage.includes('not found in the current page snapshot')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Invalid element reference. Please capture a page snapshot first to get the latest element references.

Error details: ${errorMessage}

Suggested actions: Call browser_snapshot tool to get the latest page state`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素不可见的错误
                if (errorMessage.includes('not visible')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Element not visible. Element may be hidden or not in viewport.

Error details: ${errorMessage}

Suggested actions: Check element state or use a more precise CSS selector`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素不可编辑的错误
                if (errorMessage.includes('not editable')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Element not editable. Element may be disabled or not an input field.

Error details: ${errorMessage}

Suggested actions: Check element type or use a more precise CSS selector`
                            }
                        ],
                        isError: true
                    };
                }

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to type text: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    browserPressKey: {
        name: 'browser_press_key',
        description: 'Press keyboard keys on the current page or a specific element using CSS selector.',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            key: z.string().describe('Key to press (e.g., "Enter", "Escape", "Tab", "ArrowDown")'),
            selector: z.string().optional().describe('CSS selector of the element to press key on (e.g., "#search-input", ".submit-btn", "input[type=text]")'),
            ref: z.string().optional().describe('Exact target element reference from the page snapshot (e.g., "e1", "e2")'),
            tabId: z.string().optional().describe('ID of the browser tab to use')
        }),
        examples: [
            {
                description: 'Press Enter key on the page',
                params: { key: 'Enter' },
                expectedResult: 'Successfully pressed Enter key'
            },
            {
                description: 'Press Escape key on a specific element',
                params: { key: 'Escape', selector: '.modal-dialog' },
                expectedResult: 'Successfully pressed Escape key on modal dialog'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                let code: string;
                let elementDesc: string;

                if (params.selector || params.ref) {
                    // 在特定元素上按键
                    const locator = await ElementResolver.resolveElementToLocator(page, params, context);

                    // 滚动元素到视图中（如果需要）
                    await locator.scrollIntoViewIfNeeded();

                    // 在元素上按键
                    await locator.press(params.key);

                    elementDesc = params.selector || params.ref || 'element';
                    const selectorString = params.ref ? `aria-ref=${params.ref}` : params.selector || 'element';
                    code = `await page.locator('${selectorString}').press('${params.key}');`;
                } else {
                    // 在页面上按键
                    await page.keyboard.press(params.key);

                    elementDesc = 'page';
                    code = `await page.keyboard.press('${params.key}');`;
                }

                const tabId = ToolHelpers.getTabId(context);
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully pressed ${params.key} key on ${elementDesc}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to press key: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});