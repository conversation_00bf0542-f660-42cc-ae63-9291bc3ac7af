import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ToolHelpers } from '../core/tool-helpers';
import { BrowserManager } from '../browserManager';
import { createTabTool } from './common-tools';

/**
 * Navigation Tools Collection
 * 
 * This module provides browser navigation functionality including:
 * - Navigate to URLs (opens in new tab)
 * - Navigate back in history
 * - Navigate forward in history
 * 
 * All tools include comprehensive error handling and user-friendly feedback.
 * 
 * <AUTHOR> Tools Team
 * @version 1.0.0
 */

export const NavigationTools = createTabTool({
    refreshPage: {
        name: 'browser_refresh_page',
        description: 'Refresh the current page. Equivalent to pressing F5 or clicking the browser refresh button.',
        category: ToolCategory.NAVIGATION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            waitUntil: z.enum(['load', 'domcontentloaded', 'networkidle']).optional().default('domcontentloaded').describe('Wait condition for page load completion'),
            timeout: z.number().optional().default(30000).describe('Navigation timeout in milliseconds')
        }),
        examples: [
            {
                description: 'Refresh current page',
                params: {},
                expectedResult: 'Successfully refreshed the current page'
            },
            {
                description: 'Refresh page and wait for network idle',
                params: { waitUntil: 'networkidle' },
                expectedResult: 'Successfully refreshed the page and waited for network to be idle'
            },
            {
                description: 'Refresh page with custom timeout',
                params: { timeout: 60000 },
                expectedResult: 'Successfully refreshed the page with 60 second timeout'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);
                const currentTitle = await page.title();

                // 执行页面刷新
                await page.reload({
                    waitUntil: params.waitUntil || 'domcontentloaded',
                    timeout: params.timeout || 30000,
                });

                const tabId = ToolHelpers.getTabId(context);
                const newTitle = await page.title();
                const newUrl = page.url();

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Page Refresh Successful
Successfully refreshed the current page

**Page Information:**
- **Previous Title:** ${currentTitle}
- **New Title:** ${newTitle}
- **URL:** ${newUrl}
- **Tab ID:** ${tabId || 'unknown'}
- **Wait Condition:** ${params.waitUntil || 'domcontentloaded'}
- **Timeout:** ${params.timeout || 30000}ms

**Note:** Page has been refreshed and all content has been reloaded from the server.`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Page Refresh Failed
Failed to refresh the current page

**Error Details:** ${error.message}

**Possible Causes:**
- Network connectivity issues
- Server not responding
- Page timeout exceeded
- Browser not available

**Solutions:**
1. Check network connection
2. Verify server is accessible
3. Try with a longer timeout
4. Ensure browser is running`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    navigateBack: {
        name: 'browser_navigate_back',
        description: 'Navigate back to the previous page in browser history. Equivalent to clicking the browser back button.',
        category: ToolCategory.NAVIGATION,
        capability: ToolCapability.CORE,
        parameters: z.object({}),
        examples: [
            {
                description: 'Navigate back to previous page',
                params: {},
                expectedResult: 'Successfully navigated back to the previous page in history'
            },
            {
                description: 'Go back after following a link',
                params: {},
                expectedResult: 'Returned to the previous page'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);
                const code = `await page.goBack();`;

                try {
                    await page.goBack({ waitUntil: 'networkidle' });

                    const tabId = ToolHelpers.getTabId(context);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### ⬅️ Navigation Back Successful
Successfully navigated back to the previous page

**Current Page:**
- **Title:** ${await page.title()}
- **URL:** ${page.url()}
- **Tab ID:** ${tabId || 'unknown'}
`
                            }
                        ],
                        isError: false
                    };
                } catch (error: any) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### ❌ Back Navigation Failed
Unable to go back to previous page

**Error Details:** ${error.message}

**Possible Causes:**
- Current page is the first page in history
- Page loading not completed
- Browser history is empty`
                            }
                        ],
                        isError: true
                    };
                }
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### ❌ Browser Error
Browser not started or page unavailable

**Error Details:** ${error.message}

**Solutions:**
1. Please start the browser first
2. Create a new tab
3. Ensure current page is fully loaded
4. Retry operation`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    navigateForward: {
        name: 'browser_navigate_forward',
        description: 'Navigate forward to the next page in browser history. Equivalent to clicking the browser forward button.',
        category: ToolCategory.NAVIGATION,
        capability: ToolCapability.CORE,
        parameters: z.object({}),
        examples: [
            {
                description: 'Navigate forward to next page',
                params: {},
                expectedResult: 'Successfully navigated forward to the next page in history'
            },
            {
                description: 'Go forward after going back',
                params: {},
                expectedResult: 'Returned to the next page in history'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);
                const code = `await page.goForward();`;

                try {
                    await page.goForward({ waitUntil: 'networkidle' });

                    const tabId = ToolHelpers.getTabId(context);
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### ➡️ Navigation Forward Successful
Successfully navigated forward to the next page

**Current Page:**
- **Title:** ${await page.title()}
- **URL:** ${page.url()}
- **Tab ID:** ${tabId || 'unknown'}
`
                            }
                        ],
                        isError: false
                    };
                } catch (error: any) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### ❌ Forward Navigation Failed
Unable to go forward to next page

**Error Details:** ${error.message}

**Possible Causes:**
- Current page is the last page in history
- Page loading not completed
- No forward pages in browser history`
                            }
                        ],
                        isError: true
                    };
                }
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### ❌ Browser Error
Browser not started or page unavailable

**Error Details:** ${error.message}

**Solutions:**
1. Please start the browser first
2. Create a new tab
3. Ensure the current page is fully loaded
4. Retry the operation`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
})