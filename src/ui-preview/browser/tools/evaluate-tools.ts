import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ToolHelpers } from '../core/tool-helpers';
import { ElementResolver } from '../core/element-resolver';
import { createRefTool } from './common-tools';

/**
 * Evaluate tools collection
 * Based on Microsoft Playwright MCP implementation
 */
export const EvaluateTools: Record<string, BrowserToolDefinition> = createRefTool({
    browserEvaluate: {
        name: 'browser_evaluate',
        description: 'Evaluate JavaScript expression on page or element.',
        category: ToolCategory.EVALUATION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            function: z.string().describe('JavaScript function as string: "() => { /* code */ }" or "(element) => { /* code */ }" when element is provided'),
        }),
        examples: [
            {
                description: 'Scroll to top and get scroll position',
                params: {
                    function: '() => { window.scrollTo(0, 0); return { scrollTop: window.pageYOffset, scrollLeft: window.pageXOffset }; }',
                },
                expectedResult: 'Scrolls to top and returns scroll position'
            },
        ],
        handler: async (params, context) => {
            try {
                // 获取 tabId，如果没有提供则使用 context 中的
                const tabId = params.tabId || context.tabId;
                const page = await ToolHelpers.requirePage(context, tabId);

                let locator: any = undefined;
                let code: string;
                let result: any;

                // 验证并处理函数字符串
                const functionStr = params.function.trim();
                // 直接使用字符串形式的函数，Playwright 会自动处理
                if (params.ref && params.selector) {
                    locator = await ElementResolver.resolveElementToLocator(page, {
                        element: params.selector,
                        ref: params.ref
                    }, context);

                    // 验证元素存在
                    const count = await locator.count();
                    if (count === 0) {
                        throw new Error(`Element not found: ${params.selector}`);
                    }

                    // 执行元素级评估 - 直接传递函数字符串
                    result = await locator._evaluateFunction(functionStr);
                    const selectorString = params.ref ? `aria-ref=${params.ref}` : params.selector || 'element';
                    code = `await page.locator('${selectorString}').evaluate(${JSON.stringify(functionStr)});`;
                } else if (params.selector) {
                    locator = await ElementResolver.resolveElementToLocator(page, {
                        element: params.selector
                    }, context);

                    // 验证元素存在
                    const count = await locator.count();
                    if (count === 0) {
                        throw new Error(`Element not found: ${params.selector}`);
                    }

                    // 执行元素级评估
                    result = await locator._evaluateFunction(functionStr);
                    code = `await page.locator('${params.selector}').evaluate(${JSON.stringify(functionStr)});`;
                } else {
                    // 页面级评估
                    result = await (page as any)._evaluateFunction(functionStr);
                    code = `await page.evaluate(${JSON.stringify(functionStr)});`;
                }

                // 格式化结果
                let resultText: string;
                if (result === null) {
                    resultText = 'null';
                } else if (result === undefined) {
                    resultText = 'undefined';
                } else if (typeof result === 'object') {
                    resultText = JSON.stringify(result, null, 2);
                } else {
                    resultText = String(result);
                }

                const evaluationType = locator ? 'element-level' : 'page-level';
                const elementDesc = params.selector || 'page';

                const finalTabId = ToolHelpers.getTabId(context, tabId);
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully evaluated JavaScript function (${evaluationType})

**Function**: ${params.function}
**Target**: ${elementDesc}
**Result**: ${resultText}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                // 检查是否是浏览器未启动的错误
                if (errorMessage.includes('Page is required for this operation but not available')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Browser not started or page unavailable. Please start the browser or create a new tab first.

Error details: ${errorMessage}`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素引用错误
                if (errorMessage.includes('not found in the current page snapshot')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Invalid element reference. Please capture a page snapshot first to get the latest element references.

Error details: ${errorMessage}

Suggested actions: Call browser_snapshot tool to get the latest page state`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素未找到的错误
                if (errorMessage.includes('Element not found')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Element not found. Please check the CSS selector or capture a page snapshot.

Error details: ${errorMessage}

Suggested actions: 
1. Use a more precise CSS selector
2. Call browser_snapshot tool to get page state
3. Use precise element reference (ref)`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是 JavaScript 执行错误
                if (errorMessage.includes('Evaluation failed') || errorMessage.includes('ReferenceError') || errorMessage.includes('SyntaxError') || errorMessage.includes('TypeError')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
JavaScript execution failed. Please check function syntax and logic.

**Function**: ${params.function}
**Error details**: ${errorMessage}

**Suggested actions**: 
1. Check JavaScript function syntax - ensure it's a valid function string
2. For page-level evaluation: use "() => { /* your code */ }"
3. For element-level evaluation: use "(element) => { /* your code */ }"
4. Ensure function is valid in target context
5. Verify element properties and methods exist

**Examples**:
- Page level: "() => document.title"
- Element level: "(element) => element.textContent"`
                            }
                        ],
                        isError: true
                    };
                }

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to evaluate JavaScript function: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});