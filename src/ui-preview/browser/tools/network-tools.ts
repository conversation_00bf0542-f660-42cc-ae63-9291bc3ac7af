import { z } from 'zod';
import { <PERSON><PERSON>er<PERSON>oolDefinition, NetworkRequest, ToolCategory, ToolCapability } from '../types/tool-definition';
import { CacheManager } from '../core/cache-manager';
import { createTabTool } from './common-tools';

/**
 * Network tools collection
 */
export const NetworkTools: Record<string, BrowserToolDefinition> = createTabTool({
    networkRequests: {
        name: 'browser_network_requests',
        description: 'Get network request records with pagination and filtering support',
        category: ToolCategory.DEBUG,
        capability: ToolCapability.CORE,
        parameters: z.object({
            statusFilter: z.array(z.union([z.number(), z.string()])).optional().describe('Status code filter'),
            methodFilter: z.array(z.string()).optional().describe('Request method filter'),
            urlPattern: z.string().optional().describe('URL pattern matching (regular expression)'),
            page: z.number().optional().default(1).describe('Page number, starting from 1'),
            pageSize: z.number().optional().default(20).describe('Number of items per page, maximum 100'),
            sortOrder: z.enum(['desc', 'asc']).optional().default('desc').describe('Sort order, desc for newest first'),
        }),
        examples: [
            {
                description: 'Get failed network requests',
                params: { statusFilter: [404, 500, 'failed'], page: 1, pageSize: 10 },
                expectedResult: 'Returns the latest 10 failed requests'
            },
            {
                description: 'Search API requests',
                params: { urlPattern: '/api/', methodFilter: ['GET', 'POST'] },
                expectedResult: 'Returns all API-related GET and POST requests'
            }
        ],
        handler: async (params, context) => {
            const { tabId } = context;

            if (!tabId) {
                return {
                    content: [{ type: 'text' as const, text: 'Error: tabId is required for network operations' }],
                    isError: true
                };
            }

            try {
                // 从缓存管理器获取真实的网络请求数据
                let requests = CacheManager.getNetworkRequests(tabId, true);

                let filteredRequests = requests;

                // 应用过滤器
                if (params.statusFilter && params.statusFilter.length > 0) {
                    filteredRequests = filteredRequests.filter(req =>
                        params.statusFilter!.includes(req.status)
                    );
                }

                if (params.methodFilter && params.methodFilter.length > 0) {
                    filteredRequests = filteredRequests.filter(req =>
                        params.methodFilter!.includes(req.method)
                    );
                }

                if (params.urlPattern) {
                    const pattern = new RegExp(params.urlPattern, 'i');
                    filteredRequests = filteredRequests.filter(req =>
                        pattern.test(req.url)
                    );
                }

                // 排序
                filteredRequests.sort((a, b) => {
                    return params.sortOrder === 'desc'
                        ? b.timestamp - a.timestamp
                        : a.timestamp - b.timestamp;
                });

                // 分页
                const totalCount = filteredRequests.length;
                const pageSize = Math.min(params.pageSize || 20, 100);
                const pageNum = Math.max(params.page || 1, 1);
                const startIndex = (pageNum - 1) * pageSize;
                const paginatedRequests = filteredRequests.slice(startIndex, startIndex + pageSize);

                // 格式化输出
                const formattedRequests = paginatedRequests.map(req => {
                    const timeStr = `[${new Date(req.timestamp).toLocaleString()}]`;
                    const statusStr = `[${req.status}]`;
                    const methodStr = `[${req.method}]`;
                    const responseTimeStr = req.responseTime ? ` (${req.responseTime}ms)` : '';
                    return `${timeStr} ${statusStr} ${methodStr} ${req.url}${responseTimeStr}`;
                });

                const totalPages = Math.ceil(totalCount / pageSize);
                const result = `### Network Requests (Page ${pageNum}/${totalPages})
**Total**: ${totalCount} requests | **Showing**: ${paginatedRequests.length} requests
**Scope**: ${params.sinceNavigation ? 'Since page load' : 'All history'}

${formattedRequests.length > 0 ? formattedRequests.join('\n') : 'No network requests found'}

**Pagination**: Page ${pageNum} of ${totalPages} | ${pageSize} per page`;

                return {
                    content: [{ type: 'text' as const, text: result }],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to get network requests: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },
});