import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ElementResolver } from '../core/element-resolver';
import { ToolHelpers } from '../core/tool-helpers';
import { createTabTool } from './common-tools';

/**
 * Mouse tools collection
 * Provides comprehensive mouse interaction capabilities including clicks, moves, drags, and scrolls
 */
export const MouseTools: Record<string, BrowserToolDefinition> = createTabTool({
    // 基于坐标的鼠标点击
    browserMouseClickXY: {
        name: 'browser_mouse_click_xy',
        description: 'Click at specific X,Y coordinates on the page using mouse',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            x: z.number().describe('X coordinate in pixels'),
            y: z.number().describe('Y coordinate in pixels'),
            button: z.enum(['left', 'right', 'middle']).optional().default('left').describe('Mouse button to use'),
            clickCount: z.number().optional().default(1).describe('Number of clicks (1 for single, 2 for double)'),
        }),
        examples: [
            {
                description: 'Click at specific coordinates',
                params: { x: 100, y: 200 },
                expectedResult: 'Successfully clicked at coordinates (100, 200)'
            },
            {
                description: 'Right-click at coordinates',
                params: { x: 150, y: 250, button: 'right' },
                expectedResult: 'Successfully right-clicked at coordinates (150, 250)'
            },
            {
                description: 'Double-click at coordinates',
                params: { x: 200, y: 300, clickCount: 2 },
                expectedResult: 'Successfully double-clicked at coordinates (200, 300)'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                // 执行鼠标点击
                await page.mouse.click(params.x, params.y, {
                    button: params.button || 'left',
                    clickCount: params.clickCount || 1
                });

                const clickType = params.clickCount === 2 ? 'double-clicked' : 'clicked';
                const buttonType = params.button === 'right' ? 'right-' : params.button === 'middle' ? 'middle-' : '';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully ${buttonType}${clickType} at coordinates (${params.x}, ${params.y})
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to click at coordinates: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    // 鼠标移动到指定坐标
    browserMouseMoveXY: {
        name: 'browser_mouse_move_xy',
        description: 'Move mouse cursor to specific X,Y coordinates without clicking',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            x: z.number().describe('X coordinate in pixels'),
            y: z.number().describe('Y coordinate in pixels'),
        }),
        examples: [
            {
                description: 'Move mouse to coordinates',
                params: { x: 100, y: 200 },
                expectedResult: 'Successfully moved mouse to coordinates (100, 200)'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                // 执行鼠标移动
                await page.mouse.move(params.x, params.y);

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully moved mouse to coordinates (${params.x}, ${params.y})
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to move mouse: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    // 基于坐标的鼠标拖拽
    browserMouseDragXY: {
        name: 'browser_mouse_drag_xy',
        description: 'Drag mouse from start coordinates to end coordinates',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            startX: z.number().describe('Starting X coordinate in pixels'),
            startY: z.number().describe('Starting Y coordinate in pixels'),
            endX: z.number().describe('Ending X coordinate in pixels'),
            endY: z.number().describe('Ending Y coordinate in pixels'),
            button: z.enum(['left', 'right', 'middle']).optional().default('left').describe('Mouse button to use for dragging'),
        }),
        examples: [
            {
                description: 'Drag from one point to another',
                params: { startX: 100, startY: 100, endX: 200, endY: 200 },
                expectedResult: 'Successfully dragged from (100, 100) to (200, 200)'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                // 执行鼠标拖拽
                await page.mouse.move(params.startX, params.startY);
                await page.mouse.down({ button: params.button || 'left' });
                await page.mouse.move(params.endX, params.endY);
                await page.mouse.up({ button: params.button || 'left' });

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully dragged from (${params.startX}, ${params.startY}) to (${params.endX}, ${params.endY})
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to drag mouse: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    // 元素间的拖拽
    browserDrag: {
        name: 'browser_drag',
        description: 'Drag and drop from one element to another using CSS selectors or refs',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            startSelector: z.string().optional().describe('CSS selector of the source element'),
            startRef: z.string().optional().describe('Exact source element reference from snapshot'),
            endSelector: z.string().optional().describe('CSS selector of the target element'),
            endRef: z.string().optional().describe('Exact target element reference from snapshot'),
        }),
        examples: [
            {
                description: 'Drag element using selectors',
                params: { startSelector: '.draggable-item', endSelector: '.drop-zone' },
                expectedResult: 'Successfully dragged element from source to target'
            },
            {
                description: 'Drag element using refs',
                params: { startRef: 'e1', endRef: 'e2' },
                expectedResult: 'Successfully dragged element using precise references'
            }
        ],
        handler: async (params, context) => {
            try {
                // 验证参数
                if ((!params.startSelector && !params.startRef) || (!params.endSelector && !params.endRef)) {
                    throw new Error('Both start and end elements must be specified using either selector or ref');
                }

                const page = await ToolHelpers.requirePage(context);

                // 解析源元素
                const startLocator = await ElementResolver.resolveElementToLocator(page, {
                    element: params.startSelector,
                    ref: params.startRef
                }, context);

                // 解析目标元素
                const endLocator = await ElementResolver.resolveElementToLocator(page, {
                    element: params.endSelector,
                    ref: params.endRef
                }, context);

                // 验证元素存在
                const startCount = await startLocator.count();
                const endCount = await endLocator.count();

                if (startCount === 0) {
                    throw new Error(`Source element not found: ${params.startSelector || params.startRef}`);
                }
                if (endCount === 0) {
                    throw new Error(`Target element not found: ${params.endSelector || params.endRef}`);
                }

                // 执行拖拽操作
                await startLocator.dragTo(endLocator);

                const startDesc = params.startSelector || params.startRef || 'source element';
                const endDesc = params.endSelector || params.endRef || 'target element';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully dragged from ${startDesc} to ${endDesc}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to drag element: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    // 鼠标滚轮操作
    browserMouseWheel: {
        name: 'browser_mouse_wheel',
        description: 'Scroll using mouse wheel at specific coordinates or on an element',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            deltaX: z.number().optional().describe('Horizontal scroll delta (positive for right, negative for left)'),
            deltaY: z.number().optional().describe('Vertical scroll delta (positive for down, negative for up)'),
            x: z.number().optional().describe('X coordinate for wheel event (if not specified, uses current mouse position)'),
            y: z.number().optional().describe('Y coordinate for wheel event (if not specified, uses current mouse position)'),
        }),
        examples: [
            {
                description: 'Scroll down',
                params: { deltaY: 100 },
                expectedResult: 'Successfully scrolled down by 100 pixels'
            },
            {
                description: 'Scroll up',
                params: { deltaY: -100 },
                expectedResult: 'Successfully scrolled up by 100 pixels'
            },
            {
                description: 'Scroll horizontally',
                params: { deltaX: 50 },
                expectedResult: 'Successfully scrolled right by 50 pixels'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                // 执行鼠标滚轮操作
                await page.mouse.wheel(params.deltaX || 0, params.deltaY || 0);

                const scrollDirection = [];
                if (params.deltaY) {
                    scrollDirection.push(params.deltaY > 0 ? 'down' : 'up');
                }
                if (params.deltaX) {
                    scrollDirection.push(params.deltaX > 0 ? 'right' : 'left');
                }

                const scrollText = scrollDirection.length > 0
                    ? `scrolled ${scrollDirection.join(' and ')}`
                    : 'performed wheel action';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully ${scrollText}
- Delta X: ${params.deltaX || 0}
- Delta Y: ${params.deltaY || 0}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to perform mouse wheel action: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    // 鼠标按下和释放
    browserMouseDown: {
        name: 'browser_mouse_down',
        description: 'Press down mouse button at current position or specified coordinates',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            button: z.enum(['left', 'right', 'middle']).optional().default('left').describe('Mouse button to press'),
            x: z.number().optional().describe('X coordinate (if not specified, uses current mouse position)'),
            y: z.number().optional().describe('Y coordinate (if not specified, uses current mouse position)'),
        }),
        examples: [
            {
                description: 'Press left mouse button',
                params: { button: 'left' },
                expectedResult: 'Successfully pressed left mouse button'
            },
            {
                description: 'Press right mouse button at coordinates',
                params: { button: 'right', x: 100, y: 200 },
                expectedResult: 'Successfully pressed right mouse button at (100, 200)'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                if (params.x !== undefined && params.y !== undefined) {
                    // 移动到指定坐标并按下
                    await page.mouse.move(params.x, params.y);
                }

                await page.mouse.down({ button: params.button || 'left' });

                const positionText = params.x !== undefined && params.y !== undefined
                    ? ` at coordinates (${params.x}, ${params.y})`
                    : ' at current position';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully pressed ${params.button || 'left'} mouse button${positionText}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to press mouse button: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    browserMouseUp: {
        name: 'browser_mouse_up',
        description: 'Release mouse button at current position or specified coordinates',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            button: z.enum(['left', 'right', 'middle']).optional().default('left').describe('Mouse button to release'),
            x: z.number().optional().describe('X coordinate (if not specified, uses current mouse position)'),
            y: z.number().optional().describe('Y coordinate (if not specified, uses current mouse position)'),
        }),
        examples: [
            {
                description: 'Release left mouse button',
                params: { button: 'left' },
                expectedResult: 'Successfully released left mouse button'
            },
            {
                description: 'Release right mouse button at coordinates',
                params: { button: 'right', x: 100, y: 200 },
                expectedResult: 'Successfully released right mouse button at (100, 200)'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                if (params.x !== undefined && params.y !== undefined) {
                    // 移动到指定坐标并释放
                    await page.mouse.move(params.x, params.y);
                }

                await page.mouse.up({ button: params.button || 'left' });

                const positionText = params.x !== undefined && params.y !== undefined
                    ? ` at coordinates (${params.x}, ${params.y})`
                    : ' at current position';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully released ${params.button || 'left'} mouse button${positionText}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);
                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to release mouse button: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});
