import { BrowserToolDefinition } from "../types/tool-definition";
import { z, ZodObject } from 'zod';

// 统一管理通用参数定义
export const CommonParameters = {
    tabId: z.string().optional().describe('ID of the browser tab to use'),
    ref: z.string().optional().describe('Exact target element reference from the page snapshot (e.g., "e1", "e2")'),
    selector: z.string().optional().describe('CSS selector of the target element (e.g., "#submit-btn", ".form-field", "button[type=submit]")')
} as const;

// 统一管理示例参数值
export const CommonExampleValues = {
    tabId: 'tab_xxxxx_xxxx',
    ref: ['e1', 'e2', 'e3', 'e4', 'e5'],
    selector: [
        '#submit-btn',
        '.form-field',
        'button[type=submit]',
        '.menu-item',
        '#main-content'
    ]
} as const;

// 参数检测工具
interface ParameterCheckResult {
    [key: string]: boolean;
}

function checkExistingParameters(schema: any, paramNames: string[]): ParameterCheckResult {
    const result: ParameterCheckResult = {};

    try {
        // 使用更直接的方法：检查 schema 的内部结构
        if (schema._def && schema._def.shape) {
            // 对于 ZodObject，直接检查 shape
            const shape = schema._def.shape();
            paramNames.forEach(paramName => {
                result[paramName] = paramName in shape;
            });
        } else {
            // 回退方法：使用 strict() 模式来检测未知键
            const strictSchema = schema.strict ? schema.strict() : schema;

            paramNames.forEach(paramName => {
                // 创建一个只包含当前参数的测试对象
                const testParams = { [paramName]: 'test' };
                const testParse = strictSchema.safeParse(testParams);

                if (testParse.success) {
                    result[paramName] = true;
                } else if (testParse.error) {
                    // 检查是否是因为不识别的键而失败
                    const hasUnrecognizedKey = testParse.error.issues.some((issue: any) =>
                        issue.code === 'unrecognized_keys' &&
                        issue.keys &&
                        issue.keys.includes(paramName)
                    );
                    result[paramName] = !hasUnrecognizedKey;
                } else {
                    result[paramName] = false;
                }
            });
        }
    } catch (e) {
        // 如果出错，假设所有参数都不存在
        paramNames.forEach(name => result[name] = false);
    }

    return result;
}

// 扩展 schema 的工具函数
function extendSchema(originalSchema: any, additionalParams: Record<string, any>) {
    if (Object.keys(additionalParams).length === 0) {
        return originalSchema;
    }

    if (originalSchema instanceof ZodObject) {
        return originalSchema.extend(additionalParams);
    } else {
        return z.object(additionalParams).and(originalSchema);
    }
}

// 为示例添加参数的工具函数
function addExampleParams(
    example: any,
    index: number,
    missingParams: ParameterCheckResult
): any {
    const newParams = { ...example.params };

    if (missingParams.tabId && !newParams.tabId) {
        newParams.tabId = CommonExampleValues.tabId;
    }

    if (missingParams.ref && !newParams.ref) {
        newParams.ref = CommonExampleValues.ref[index % CommonExampleValues.ref.length];
    }

    if (missingParams.selector && !newParams.selector) {
        newParams.selector = CommonExampleValues.selector[index % CommonExampleValues.selector.length];
    }

    return {
        ...example,
        params: newParams
    };
}

export const createTabTool = (toolDefinition: Record<string, BrowserToolDefinition>) => {
    // 遍历 parameters， 补充 tabId 参数
    // 遍历 examples， 补充 tabId参数
    const result: Record<string, BrowserToolDefinition> = {};

    for (const [key, tool] of Object.entries(toolDefinition)) {
        const newTool = { ...tool };
        const originalSchema = tool.parameters;

        // 检查是否已有 tabId 参数
        const existingParams = checkExistingParameters(originalSchema, ['tabId']);

        // 添加缺失的参数
        const additionalParams: Record<string, any> = {};
        if (!existingParams.tabId) {
            additionalParams.tabId = CommonParameters.tabId;
        }

        // 扩展 parameters schema
        newTool.parameters = extendSchema(originalSchema, additionalParams);

        // 更新 examples
        newTool.examples = tool.examples.map((example, index) =>
            addExampleParams(example, index, { tabId: !existingParams.tabId })
        );

        result[key] = newTool;
    }

    return result;
}

export const createRefTool = (toolDefinition: Record<string, BrowserToolDefinition>) => {
    // 遍历 parameters， 补充 tabId， ref, selector 参数
    // 遍历 examples， 补充 tabId， ref, selector 参数
    const result: Record<string, BrowserToolDefinition> = {};

    for (const [key, tool] of Object.entries(toolDefinition)) {
        const newTool = { ...tool };
        const originalSchema = tool.parameters;

        // 检查现有参数
        const existingParams = checkExistingParameters(originalSchema, ['tabId', 'ref', 'selector']);

        // 构建需要添加的参数
        const additionalParams: Record<string, any> = {};
        if (!existingParams.tabId) {
            additionalParams.tabId = CommonParameters.tabId;
        }
        if (!existingParams.ref) {
            additionalParams.ref = CommonParameters.ref;
        }
        if (!existingParams.selector) {
            additionalParams.selector = CommonParameters.selector;
        }

        // 扩展 parameters schema
        newTool.parameters = extendSchema(originalSchema, additionalParams);

        // 更新 examples
        newTool.examples = tool.examples.map((example, index) =>
            addExampleParams(example, index, {
                tabId: !existingParams.tabId,
                ref: !existingParams.ref,
                selector: !existingParams.selector
            })
        );

        result[key] = newTool;
    }

    return result;
}