import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ElementResolver } from '../core/element-resolver';
import { ToolHelpers } from '../core/tool-helpers';
import { uploadBuffer } from '@/util/upload';
import { GlobalConfig } from '@/util/global';
import { createTabTool } from './common-tools';

/**
 * Screenshot tools collection
 * 
 * IMPORTANT FOR AI MODELS:
 * When using these screenshot tools, you MUST:
 * 1. Analyze the current page state before taking screenshots
 * 2. Set clear expectations about what should be visible
 * 3. After capturing, carefully compare the actual screenshot with your expectations
 * 4. Identify any discrepancies, missing elements, or unexpected visual issues
 * 5. Provide detailed analysis of what you observe vs. what you expected
 * 
 * This careful analysis and comparison process is crucial for accurate UI testing and debugging.
 */
export const ScreenshotTools: Record<string, BrowserToolDefinition> = createTabTool({
    takeScreenshot: {
        name: 'browser_take_screenshot',
        description: 'Take a screenshot of the current page viewport. IMPORTANT: Before taking screenshots, carefully analyze and compare the current page state with your expectations. Pay attention to: 1) Page loading status and content visibility, 2) Element positioning and layout, 3) Any dynamic content or animations that might affect the screenshot, 4) Viewport size and responsive design considerations. After taking the screenshot, thoroughly examine the captured image to verify it matches your analysis and expectations.',
        category: ToolCategory.SCREENSHOT,
        capability: ToolCapability.CORE,
        parameters: z.object({
        }),
        examples: [
            {
                description: 'Take screenshot of current viewport - Analyze page state first, then capture and verify the result',
                params: {},
                expectedResult: 'Successfully took screenshot after careful analysis and verification of page state'
            },
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);
                const timestamp = Date.now();
                const filename = `screenshot_${timestamp}.jpeg`;

                const screenshotOptions: any = {
                    type: 'jpeg',
                    fullPage: false
                };

                let screenshotBuffer: Buffer;
                screenshotBuffer = await page.screenshot(screenshotOptions);

                const file = await uploadBuffer(GlobalConfig.getConfig().getKwaiPilotDomain(), {
                    buffer: screenshotBuffer,
                    filename: filename,
                    mimeType: 'image/jpeg'
                });

                const tabId = ToolHelpers.getTabId(context);
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Screenshot Analysis & Result
Screenshot captured for [tabId: ${tabId || 'unknown'}] [${await page.title()}] (${page.url()})

**Analysis Instructions for Model:**
Please carefully examine this screenshot and compare it with your expectations. Consider:
- Does the page layout match what you expected?
- Are all elements properly positioned and visible?
- Is the content fully loaded and rendered correctly?
- Are there any unexpected visual elements or missing components?
- Does the overall appearance align with the intended design or functionality?

Provide detailed observations about what you see in the screenshot and how it compares to your analysis.`
                        },
                        {
                            type: 'image' as const,
                            source: {
                                type: 'url',
                                url: file.url || ''
                            }
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                // 提供详细的错误诊断信息
                let diagnosticInfo = '';
                try {
                    const { TabManager } = await import('../core/tab-manager');
                    const tabManager = TabManager.getInstance();
                    const allTabs = tabManager.getAllTabs();

                    diagnosticInfo = `

### Diagnostic Information
- Context tabId: ${context.tabId || 'not provided'}
- Available tabs: ${allTabs.length}
- Active tabs: ${allTabs.filter(tab => !tab.isClosed).length}
- Tab details: ${JSON.stringify(allTabs.map(tab => ({
                        tabId: tab.tabId,
                        url: tab.url,
                        isClosed: tab.isClosed
                    })), null, 2)}`;
                } catch (diagError) {
                    diagnosticInfo = `\n### Diagnostic Error: ${diagError}`;
                }

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to take screenshot: ${error.message}${diagnosticInfo}

### Troubleshooting
1. Make sure a browser tab is open and active
2. Verify the tabId is correct if provided
3. Check if the page is still available and not closed`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});