import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ToolHelpers } from '../core/tool-helpers';
import { CacheManager } from '../core/cache-manager';
import { createTabTool } from './common-tools';

/**
 * Snapshot tools collection
 * Based on playwright-mcp implementation
 */
export const SnapshotTools: Record<string, BrowserToolDefinition> = createTabTool({
    browserSnapshot: {
        name: 'browser_snapshot',
        description: 'Capture accessibility snapshot of the current page, this is better than screenshot.  `tabId` is the id of the browser page',
        category: ToolCategory.DEBUG,
        capability: ToolCapability.CORE,
        parameters: z.object({
        }),
        examples: [
            {
                description: 'Capture accessibility snapshot of current page',
                params: { tabId: 'tab_xxxxx_xxxx' },
                expectedResult: 'Successfully captured accessibility snapshot of the page'
            }
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);

                // 获取页面信息
                const url = page.url();
                const title = await page.title();

                // 尝试多种方式获取快照
                let ariaSnapshot: string = '';
                let snapshotMethod = '';

                try {
                    // 方法1: 尝试使用 Playwright 内部的 _snapshotForAI 方法
                    ariaSnapshot = await (page as any)._snapshotForAI();
                    snapshotMethod = '_snapshotForAI';
                } catch (error1) {
                }

                // 缓存快照
                const tabId = ToolHelpers.getTabId(context);
                if (tabId) {
                    CacheManager.cacheSnapshot(tabId, ariaSnapshot);
                }

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully captured accessibility snapshot of the current page

### Page state
- Page URL: ${url}
- Tab Id: ${tabId}
- Page Title: ${title}
- Snapshot Method: ${snapshotMethod}
- Page Snapshot:
\`\`\`yaml
${ariaSnapshot}
\`\`\`
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to capture accessibility snapshot: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});
