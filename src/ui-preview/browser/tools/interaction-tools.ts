import { z } from 'zod';
import { <PERSON>rowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ElementResolver } from '../core/element-resolver';
import { ElementValidator } from '../core/element-validator';
import { ToolHelpers } from '../core/tool-helpers';
import { createRefTool } from './common-tools';

/**
 * Interaction tools collection
 */
export const InteractionTools: Record<string, BrowserToolDefinition> = createRefTool({
    browserClick: {
        name: 'browser_click',
        description: 'Click on an element on the web page using CSS selector or precise ref from snapshot.',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            button: z.enum(['left', 'right', 'middle']).optional().default('left').describe('Mouse button to use for clicking'),
            doubleClick: z.boolean().optional().default(false).describe('Whether to perform a double-click instead of single-click'),
        }),
        examples: [
            {
                description: 'Click a button using CSS selector',
                params: { tabId: 'tab_xxxxx_xxxx', selector: 'button[type=submit]' },
                expectedResult: 'Successfully clicked submit button'
            },
            {
                description: 'Click an element using precise ref',
                params: { tabId: 'tab_xxxxx_xxxx', ref: 'e1' },
                expectedResult: 'Successfully clicked element'
            },
        ],
        handler: async (params, context) => {
            try {
                // 验证参数
                if (!params.selector && !params.ref) {
                    throw new Error('Either selector or ref parameter is required');
                }

                const page = await ToolHelpers.requirePage(context);

                // 使用改进的元素解析器
                const locator = await ElementResolver.resolveElementToLocator(page, params, context);

                const clickOptions = {
                    button: params.button || 'left',
                    clickCount: params.doubleClick ? 2 : 1,
                    timeout: 15000 // 增加超时时间到15秒
                };

                // 滚动元素到视图中（如果需要）
                await locator.scrollIntoViewIfNeeded();

                // 执行点击操作
                await locator.click(clickOptions);

                // 等待页面稳定
                await page.waitForLoadState('networkidle', { timeout: 5000 }).catch(() => {
                    // 忽略网络空闲超时，继续执行
                });

                const clickType = params.doubleClick ? 'double-clicked' : 'clicked';
                const elementDesc = params.selector || params.ref || 'element';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully ${clickType} ${elementDesc}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                // 检查是否是浏览器未启动的错误
                if (errorMessage.includes('Page is required for this operation but not available')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Browser not started or page unavailable. Please start the browser or create a new tab first.

Error details: ${errorMessage}`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素引用错误
                if (errorMessage.includes('not found in the current page snapshot')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Invalid element reference. Please capture a page snapshot first to get the latest element references.

Error details: ${errorMessage}

Suggested actions: Call browser_snapshot tool to get the latest page state`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素不可点击的错误
                if (errorMessage.includes('not clickable')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Element not clickable. Element may be hidden, disabled, or not in viewport.

Error details: ${errorMessage}

Suggested actions: Check element state or use a more precise CSS selector`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素未找到的错误
                if (errorMessage.includes('No element found matching CSS selector')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
No matching element found. Please check the CSS selector or capture a page snapshot.

Error details: ${errorMessage}

Suggested actions: 
1. Use a more precise CSS selector
2. Call browser_snapshot tool to get page state
3. Use precise element reference (ref)`
                            }
                        ],
                        isError: true
                    };
                }

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to click element: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    browserHover: {
        name: 'browser_hover',
        description: 'Hover over an element on the web page using CSS selector or precise ref from snapshot.',
        category: ToolCategory.INTERACTION,
        capability: ToolCapability.CORE,
        parameters: z.object({
            selector: z.string().optional().describe('CSS selector of the element to hover (e.g., ".menu-btn", "#tooltip-trigger", "button[aria-label=menu]")'),
            ref: z.string().optional().describe('Exact target element reference from the page snapshot (e.g., "e1", "e2")'),
            tabId: z.string().optional().describe('ID of the browser tab to use')
        }).refine(data => data.selector || data.ref, {
            message: 'Either selector or ref parameter is required'
        }),
        examples: [
            {
                description: 'Hover over a menu button',
                params: { selector: '.menu-button' },
                expectedResult: 'Successfully hovered over menu button'
            },
            {
                description: 'Hover over an element using precise ref',
                params: { ref: 'e1' },
                expectedResult: 'Successfully hovered over element'
            }
        ],
        handler: async (params, context) => {
            try {
                // 验证参数
                if (!params.selector && !params.ref) {
                    throw new Error('Either selector or ref parameter is required');
                }

                const page = await ToolHelpers.requirePage(context);

                // 使用改进的元素解析器
                const locator = await ElementResolver.resolveElementToLocator(page, params, context);

                // 滚动元素到视图中（如果需要）
                await locator.scrollIntoViewIfNeeded();

                // 执行悬停操作
                await locator.hover();

                const elementDesc = params.selector || params.ref || 'element';

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Successfully hovered over ${elementDesc}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                // 检查是否是元素引用错误
                if (errorMessage.includes('not found in the current page snapshot')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Invalid element reference. Please capture a page snapshot first to get the latest element references.

Error details: ${errorMessage}

Suggested actions: Call browser_snapshot tool to get the latest page state`
                            }
                        ],
                        isError: true
                    };
                }

                // 检查是否是元素不可见的错误
                if (errorMessage.includes('not visible')) {
                    return {
                        content: [
                            {
                                type: 'text',
                                text: `### Error
Element not visible. Element may be hidden or not in viewport.

Error details: ${errorMessage}

Suggested actions: Check element state or use a more precise CSS selector`
                            }
                        ],
                        isError: true
                    };
                }

                return {
                    content: [
                        {
                            type: 'text',
                            text: `### Error
Failed to hover over element: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },
});