import { z } from 'zod';
import { <PERSON>rowserToolDefinition, ToolCategory, ToolCapability, ToolContext, ToolResponse } from '../types/tool-definition';
import { TabManager } from '../core/tab-manager';
import { ToolHelpers } from '../core/tool-helpers';
import { BrowserManager } from '../browserManager';

/**
 * Create a new tab
 */
const createTabTool: BrowserToolDefinition = {
    name: 'create_tab',
    description: 'Create a new Tab and navigate to a specified URL. Supports HTTP/HTTPS URLs and local development servers.',
    category: ToolCategory.NAVIGATION,
    capability: ToolCapability.CORE,
    parameters: z.object({
        url: z.string()
            .url('Must be a valid URL')
            .describe('The URL to navigate to (e.g., https://example.com, http://localhost:3000)')
    }),
    examples: [
        {
            description: 'Navigate to GitHub homepage',
            params: { url: 'https://github.com' },
            expectedResult: 'Successfully navigated to GitHub homepage in a new tab'
        },
        {
            description: 'Navigate to local development server',
            params: { url: 'http://localhost:3000' },
            expectedResult: 'Successfully navigated to local development server'
        },
        {
            description: 'Navigate to documentation site',
            params: { url: 'https://docs.example.com' },
            expectedResult: 'Successfully navigated to documentation site'
        }
    ],
    handler: async (params, context) => {
        try {
            const browserManager = BrowserManager.getInstance();
            // const code = `await browserManager.navigateTo('${params.url}');`;

            const { page, tabId } = await browserManager.navigateTo(params.url);

            let url, title;
            try {
                url = page.url();
                title = await page.title();
            } catch (error) {
                title = 'Untitled';
                url = 'Untitled';
            };

            return {
                content: [
                    {
                        type: 'text' as const,
                        text: `### ✅ Navigation Successful
Successfully navigated to: **${params.url}**

**Page Information:**
- **Title:** ${title}
- **Current URL:** ${url}
- **Tab ID:** ${tabId}
`
                    }
                ],
                isError: false
            };
        } catch (error: any) {
            return {
                content: [
                    {
                        type: 'text',
                        text: `### ❌ Navigation Failed
Browser startup failed or navigation error.

**Error Type:** ${error.name || 'Unknown Error'}
**Error Details:** ${error.message}

**Possible Solutions:**
- Check if URL format is correct
- Confirm network connection is normal
- Verify if target website is accessible
- Retry operation or restart browser`
                    }
                ],
                isError: true
            };
        }
    }
};

/**
 * Switch to a tab
 */
const switchTabTool: BrowserToolDefinition = {
    name: 'switch_tab',
    description: 'Switch to a different browser tab',
    category: ToolCategory.NAVIGATION,
    capability: ToolCapability.CORE_TABS,
    parameters: z.object({
        tabId: z.string().optional().describe('Tab ID to switch to'),
        url: z.string().optional().describe('URL pattern to match for tab switching'),
        title: z.string().optional().describe('Title pattern to match for tab switching'),
        index: z.number().optional().describe('Tab index (0-based) to switch to')
    }),
    examples: [
        {
            description: 'Switch to tab by URL pattern',
            params: { url: 'github.com' },
            expectedResult: 'Successfully switched to GitHub tab'
        },
        {
            description: 'Switch to tab by title',
            params: { title: 'Google' },
            expectedResult: 'Successfully switched to Google tab'
        },
        {
            description: 'Switch to tab by index',
            params: { index: 1 },
            expectedResult: 'Successfully switched to tab at index 1'
        }
    ],
    handler: async (params, context: ToolContext): Promise<ToolResponse> => {
        try {
            const { tabId, url, title, index } = params;
            const tabManager = TabManager.getInstance();
            const allTabs = tabManager.getAllTabs();

            let targetPage = null;
            let targetTabId = '';

            if (tabId) {
                // 优先使用 tabId 查找
                targetPage = tabManager.getPageByTabId(tabId);
                targetTabId = tabId;
            } else if (index !== undefined) {
                if (index >= 0 && index < allTabs.length) {
                    const tab = allTabs[index];
                    targetPage = tab.page;
                    targetTabId = tab.tabId;
                }
            } else if (url) {
                const tab = allTabs.find(t => t.page.url().includes(url));
                if (tab) {
                    targetPage = tab.page;
                    targetTabId = tab.tabId;
                }
            } else if (title) {
                for (const tab of allTabs) {
                    try {
                        const pageTitle = await tab.page.title();
                        if (pageTitle.includes(title)) {
                            targetPage = tab.page;
                            targetTabId = tab.tabId;
                            break;
                        }
                    } catch (e) {
                        // Skip pages that can't be accessed
                        continue;
                    }
                }
            }

            if (!targetPage) {
                return {
                    content: [{
                        type: 'text' as const,
                        text: 'No matching tab found'
                    }],
                    isError: true
                };
            }

            await targetPage.bringToFront();

            // 更新标签页活跃时间
            tabManager.updateTabActivity(targetTabId);

            const pageTitle = await targetPage.title().catch(() => 'Unknown');
            const pageUrl = targetPage.url();

            return {
                content: [{
                    type: 'text' as const,
                    text: `Successfully switched to tab: "${pageTitle}" (${pageUrl}) [TabID: ${targetTabId}]`
                }]
            };
        } catch (error) {
            return {
                content: [{
                    type: 'text' as const,
                    text: `Failed to switch tab: ${error instanceof Error ? error.message : String(error)}`
                }],
                isError: true
            };
        }
    }
};

/**
 * List all open tabs
 */
const listTabsTool: BrowserToolDefinition = {
    name: 'list_tabs',
    description: 'List all open browser tabs',
    category: ToolCategory.DEBUG,
    capability: ToolCapability.CORE_TABS,
    parameters: z.object({
        includeDetails: z.boolean().optional().default(false).describe('Include detailed information about each tab')
    }),
    examples: [
        {
            description: 'List all tabs with basic info',
            params: {},
            expectedResult: 'Listed all open tabs'
        },
        {
            description: 'List all tabs with detailed information',
            params: { includeDetails: true },
            expectedResult: 'Listed all open tabs with detailed information'
        }
    ],
    handler: async (params, context: ToolContext): Promise<ToolResponse> => {
        try {
            const { includeDetails } = params;
            const tabManager = TabManager.getInstance();
            const allTabs = tabManager.getAllTabs();

            const tabInfo = [];

            for (let i = 0; i < allTabs.length; i++) {
                const tab = allTabs[i];
                try {
                    const isActive = tab.page === context.page;
                    const title = await tab.page.title().catch(() => tab.title || 'Unknown');
                    const url = tab.page.url() || tab.url;

                    let info = `${i}: [TabID: ${tab.tabId}] ${title} - ${url}${isActive ? ' (active)' : ''}`;

                    if (includeDetails) {
                        const viewport = tab.page.viewportSize();
                        const isClosed = tab.page.isClosed();
                        const createdAt = new Date(tab.createdAt).toISOString();
                        const lastActiveAt = new Date(tab.lastActiveAt).toISOString();

                        info += `\n  - Viewport: ${viewport?.width}x${viewport?.height}`;
                        info += `\n  - Status: ${isClosed ? 'closed' : 'open'}`;
                        info += `\n  - TabID: ${tab.tabId}`;
                        info += `\n  - Created: ${createdAt}`;
                        info += `\n  - Last Active: ${lastActiveAt}`;
                    }

                    tabInfo.push(info);
                } catch (e) {
                    tabInfo.push(`${i}: [TabID: ${tab.tabId}] [Inaccessible tab]`);
                }
            }

            return {
                content: [{
                    type: 'text' as const,
                    text: `Open tabs (${allTabs.length}):\n${tabInfo.join('\n')}`
                }]
            };
        } catch (error) {
            return {
                content: [{
                    type: 'text' as const,
                    text: `Failed to list tabs: ${error instanceof Error ? error.message : String(error)}`
                }],
                isError: true
            };
        }
    }
};

/**
 * Tab tools collection
 */
export const TabTools: Record<string, BrowserToolDefinition> = {
    createTab: createTabTool,
    switchTab: switchTabTool,
    listTabs: listTabsTool,
};