import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability, ToolContext, ToolResponse } from '../types/tool-definition';
import { TabManager } from '../core/tab-manager';

/**
 * Get tab info by ID
 */
const getTabInfoTool: BrowserToolDefinition = {
    name: 'get_tab_info',
    description: 'Get detailed information about a specific tab by its ID',
    category: ToolCategory.DEBUG,
    capability: ToolCapability.CORE_TABS,
    parameters: z.object({
        targetTabId: z.string().describe('The tab ID to get information for')
    }),
    examples: [
        {
            description: 'Get information for a specific tab',
            params: { targetTabId: 'tab_1234567890_abc123' },
            expectedResult: 'Returns detailed information about the specified tab'
        }
    ],
    handler: async (params, context: ToolContext): Promise<ToolResponse> => {
        try {
            const { targetTabId } = params;
            const tabManager = TabManager.getInstance();
            const tabInfo = tabManager.getTabInfo(targetTabId);

            if (!tabInfo) {
                return {
                    content: [{
                        type: 'text' as const,
                        text: `Tab with ID ${targetTabId} not found`
                    }],
                    isError: true
                };
            }

            const title = await tabInfo.page.title().catch(() => tabInfo.title || 'Unknown');
            const url = tabInfo.page.url() || tabInfo.url;
            const isActive = tabInfo.page === context.page;
            const isClosed = tabInfo.page.isClosed();
            const viewport = tabInfo.page.viewportSize();

            return {
                content: [{
                    type: 'text' as const,
                    text: `Tab Information:
Tab ID: ${targetTabId}
Title: ${title}
URL: ${url}
Status: ${isClosed ? 'closed' : 'open'}
Active: ${isActive ? 'yes' : 'no'}
Viewport: ${viewport?.width}x${viewport?.height}
Created: ${new Date(tabInfo.createdAt).toISOString()}
Last Active: ${new Date(tabInfo.lastActiveAt).toISOString()}`
                }]
            };
        } catch (error) {
            return {
                content: [{
                    type: 'text' as const,
                    text: `Failed to get tab info: ${error instanceof Error ? error.message : String(error)}`
                }],
                isError: true
            };
        }
    }
};

/**
 * Tab info tools collection
 */
export const TabInfoTools: Record<string, BrowserToolDefinition> = {
    getTabInfo: getTabInfoTool,
};