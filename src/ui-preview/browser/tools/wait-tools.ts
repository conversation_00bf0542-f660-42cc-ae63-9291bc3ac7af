import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { ElementResolver } from '../core/element-resolver';
import { ToolHelpers } from '../core/tool-helpers';

/**
 * Wait tools collection
 */
export const WaitTools: Record<string, BrowserToolDefinition> = {
    waitFor: {
        name: 'browser_wait_for',
        description: 'Wait for specified conditions using CSS selectors or time delays',
        category: ToolCategory.WAIT,
        capability: ToolCapability.CORE,
        parameters: z.object({
            time: z.number().optional().describe('Wait time in milliseconds'),
        }),
        examples: [
            {
                description: 'Wait for 2 seconds',
                params: { time: 2000 },
                expectedResult: 'Successfully waited for 2 seconds'
            },
        ],
        handler: async (params, context) => {
            try {
                const page = await ToolHelpers.requirePage(context);
                let code = '';
                let result = '';

                if (params.time) {
                    code = `await page.waitForTimeout(${params.time});`;
                    await page.waitForTimeout(params.time);
                    result = `Waited for ${params.time}ms`;
                }

                if (!result) {
                    result = 'No wait condition specified';
                }

                const tabId = ToolHelpers.getTabId(context);
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
${result}
`
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                const errorMessage = error instanceof Error ? error.message : String(error);

                // 检查是否是浏览器未启动的错误
                if (errorMessage.includes('Page is required for this operation but not available')) {
                    return {
                        content: [
                            {
                                type: 'text' as const,
                                text: `### Error
Browser not started or page unavailable. Please start the browser or create a new tab first.

Error details: ${errorMessage}`
                            }
                        ],
                        isError: true
                    };
                }

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Wait operation failed: ${errorMessage}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
};