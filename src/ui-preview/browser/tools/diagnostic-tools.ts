import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { TabManager } from '../core/tab-manager';
import { PageLifecycleManager } from '../core/page-lifecycle-manager';

/**
 * Diagnostic tools collection
 */
export const DiagnosticTools: Record<string, BrowserToolDefinition> = {
    diagnosticInfo: {
        name: 'browser_diagnostic_info',
        description: 'Get diagnostic information about browser tabs and pages',
        category: ToolCategory.DEBUG,
        capability: ToolCapability.CORE,
        parameters: z.object({
            tabId: z.string().optional().describe('Specific tab ID to diagnose')
        }),
        examples: [
            {
                description: 'Get general diagnostic info',
                params: {},
                expectedResult: 'Returns information about all available tabs'
            },
            {
                description: 'Diagnose specific tab',
                params: { tabId: 'tab_123' },
                expectedResult: 'Returns detailed information about the specified tab'
            }
        ],
        handler: async (params, context) => {
            try {
                const tabManager = TabManager.getInstance();
                const allTabs = tabManager.getAllTabs();
                const targetTabId = params.tabId || context.tabId;

                const diagnosticInfo = {
                    context: {
                        providedTabId: params.tabId,
                        contextTabId: context.tabId,
                        finalTabId: targetTabId,
                        hasContextPage: !!context.page,
                        hasGetPageMethod: !!context.getPage,
                        hasContextContext: !!context.context,
                        hasGetContextMethod: !!context.getContext
                    },
                    tabs: {
                        total: allTabs.length,
                        active: allTabs.filter(tab => !tab.isClosed).length,
                        closed: allTabs.filter(tab => tab.isClosed).length,
                        details: allTabs.map(tab => ({
                            tabId: tab.tabId,
                            url: tab.url,
                            title: tab.title,
                            isClosed: tab.isClosed,
                            createdAt: new Date(tab.createdAt).toISOString(),
                            lastActiveAt: new Date(tab.lastActiveAt).toISOString(),
                            lifecycleState: PageLifecycleManager.getPageState(tab.tabId)
                        }))
                    },
                    targetTab: null as any
                };

                if (targetTabId) {
                    const targetPage = tabManager.getPageByTabId(targetTabId);
                    const targetTabInfo = tabManager.getTabInfo(targetTabId);
                    const lifecycleState = PageLifecycleManager.getPageState(targetTabId);

                    diagnosticInfo.targetTab = {
                        tabId: targetTabId,
                        exists: !!targetTabInfo,
                        pageAvailable: !!targetPage,
                        tabInfo: targetTabInfo,
                        lifecycleState,
                        pageUrl: targetPage ? targetPage.url() : null,
                        pageIsClosed: targetPage ? targetPage.isClosed() : null
                    };
                }

                const formattedOutput = `### Browser Diagnostic Information

**Context Information:**
- Provided tabId: ${diagnosticInfo.context.providedTabId || 'none'}
- Context tabId: ${diagnosticInfo.context.contextTabId || 'none'}
- Final tabId: ${diagnosticInfo.context.finalTabId || 'none'}
- Has context page: ${diagnosticInfo.context.hasContextPage}
- Has getPage method: ${diagnosticInfo.context.hasGetPageMethod}

**Tab Summary:**
- Total tabs: ${diagnosticInfo.tabs.total}
- Active tabs: ${diagnosticInfo.tabs.active}
- Closed tabs: ${diagnosticInfo.tabs.closed}

**Available Tabs:**
${diagnosticInfo.tabs.details.map(tab =>
                    `- ${tab.tabId}: ${tab.url} (${tab.isClosed ? 'closed' : 'active'})`
                ).join('\n')}

${targetTabId ? `**Target Tab Analysis (${targetTabId}):**
- Tab exists: ${diagnosticInfo.targetTab.exists}
- Page available: ${diagnosticInfo.targetTab.pageAvailable}
- Page URL: ${diagnosticInfo.targetTab.pageUrl || 'N/A'}
- Page closed: ${diagnosticInfo.targetTab.pageIsClosed}
- Lifecycle state: ${JSON.stringify(diagnosticInfo.targetTab.lifecycleState, null, 2)}` : ''}

**Recommendations:**
${diagnosticInfo.tabs.active === 0 ? '⚠️  No active tabs found. Create a new tab first.' : ''}
${targetTabId && !diagnosticInfo.targetTab.exists ? `⚠️  Target tab "${targetTabId}" does not exist.` : ''}
${targetTabId && !diagnosticInfo.targetTab.pageAvailable ? `⚠️  Target tab "${targetTabId}" page is not available.` : ''}
${diagnosticInfo.tabs.active > 0 && !targetTabId ? '💡 Use one of the available tab IDs for operations.' : ''}`;

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: formattedOutput
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to get diagnostic information: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
};