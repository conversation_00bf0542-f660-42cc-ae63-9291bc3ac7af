import { z } from 'zod';
import { BrowserToolDefinition, ToolCategory, ToolCapability } from '../types/tool-definition';
import { CacheManager } from '../core/cache-manager';
import { createTabTool } from './common-tools';

/**
 * Console tools collection
 */
export const ConsoleTools: Record<string, BrowserToolDefinition> = createTabTool({
    consoleMessages: {
        name: 'browser_console_messages',
        description: 'Get console messages with pagination and filtering support',
        category: ToolCategory.DEBUG,
        capability: ToolCapability.CORE,
        parameters: z.object({
            messageTypes: z.array(z.enum(['log', 'info', 'warn', 'error', 'debug', 'trace'])).optional().describe('Filter message types'),
            page: z.number().optional().default(1).describe('Page number, starting from 1'),
            pageSize: z.number().optional().default(20).describe('Number of items per page, maximum 100'),
            sortOrder: z.enum(['desc', 'asc']).optional().default('desc').describe('Sort order, desc for newest first'),
            textPattern: z.string().optional().describe('Message text pattern matching (regular expression)'),
        }),
        examples: [
            {
                description: 'Get latest error messages',
                params: { messageTypes: ['error'], page: 1, pageSize: 10 },
                expectedResult: 'Returns the latest 10 error messages'
            },
            {
                description: 'Search messages containing API',
                params: { textPattern: 'API', sortOrder: 'desc' },
                expectedResult: 'Returns messages containing API keyword'
            }
        ],
        handler: async (params, context) => {
            const { tabId } = context;

            if (!tabId) {
                return {
                    content: [{ type: 'text' as const, text: 'Error: tabId is required for console operations' }],
                    isError: true
                };
            }

            try {
                let messages = CacheManager.getConsoleMessages(tabId, true);

                // 应用过滤器
                if (params.messageTypes && params.messageTypes.length > 0) {
                    messages = messages.filter(msg => params.messageTypes!.includes(msg.type as any));
                }

                if (params.textPattern) {
                    const pattern = new RegExp(params.textPattern, 'i');
                    messages = messages.filter(msg => pattern.test(msg.text));
                }

                // 排序
                messages.sort((a, b) => {
                    return params.sortOrder === 'desc'
                        ? b.timestamp - a.timestamp
                        : a.timestamp - b.timestamp;
                });

                // 分页
                const totalCount = messages.length;
                const pageSize = Math.min(params.pageSize || 20, 100);
                const pageNum = Math.max(params.page || 1, 1);
                const startIndex = (pageNum - 1) * pageSize;
                const paginatedMessages = messages.slice(startIndex, startIndex + pageSize);

                // 格式化输出
                const formattedMessages = paginatedMessages.map(msg => {
                    const timeStr = `[${new Date(msg.timestamp).toLocaleString()}]`;
                    const typeStr = `[${msg.type.toUpperCase()}]`;
                    return `${timeStr} ${typeStr} ${msg.text}`;
                });

                const totalPages = Math.ceil(totalCount / pageSize);
                const result = `### Console Messages (Page ${pageNum}/${totalPages})
**Total**: ${totalCount} messages | **Showing**: ${paginatedMessages.length} messages
**Scope**: ${params.sinceNavigation ? 'Since page load' : 'All history'}

${formattedMessages.length > 0 ? formattedMessages.join('\n') : 'No console messages found'}

**Pagination**: Page ${pageNum} of ${totalPages} | ${pageSize} per page`;

                return {
                    content: [{ type: 'text' as const, text: result }],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to get console messages: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        }
    },

    consoleClear: {
        name: 'browser_console_clear',
        description: 'Clear console message history',
        category: ToolCategory.DEBUG,
        capability: ToolCapability.CORE,
        parameters: z.object({
        }),
        examples: [
            {
                description: 'Clear console history',
                params: { confirmClear: true },
                expectedResult: 'Successfully cleared console message history'
            }
        ],
        handler: async (params, context) => {
            const { tabId } = context;

            if (!tabId) {
                return {
                    content: [{ type: 'text' as const, text: 'Error: tabId is required for console operations' }],
                    isError: true
                };
            }

            try {
                const messageCount = CacheManager.getConsoleMessages(tabId, false).length;
                CacheManager.clearConsoleHistory(tabId);

                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Result
Console message history cleared (${messageCount} messages removed)

### Ran Playwright code
\`\`\`js
// Console history cleared programmatically
\`\`\``
                        }
                    ],
                    isError: false
                };
            } catch (error: any) {
                return {
                    content: [
                        {
                            type: 'text' as const,
                            text: `### Error
Failed to clear console history: ${error.message}`
                        }
                    ],
                    isError: true
                };
            }
        }
    }
});