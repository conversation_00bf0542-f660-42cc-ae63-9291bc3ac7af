/**
 * 浏览器工具系统配置
 */

export interface ToolSystemConfig {
    // 缓存配置
    cache: {
        snapshotMaxAge: number;           // 快照缓存最大年龄（毫秒）
        maxConsoleMessages: number;       // 最大控制台消息数量
        maxNetworkRequests: number;       // 最大网络请求记录数量
    };

    // 分页配置
    pagination: {
        defaultPageSize: number;          // 默认页面大小
        maxPageSize: number;              // 最大页面大小
    };

    // 超时配置
    timeouts: {
        defaultElementTimeout: number;    // 默认元素等待超时
        defaultNavigationTimeout: number; // 默认导航超时
        defaultActionTimeout: number;     // 默认操作超时
    };

    // 截图配置
    screenshot: {
        defaultFormat: 'png' | 'jpeg';    // 默认截图格式
        defaultQuality: number;           // 默认JPEG质量
        maxFileSize: number;              // 最大文件大小（字节）
    };

    // 批处理配置
    batch: {
        maxBatchSize: number;             // 最大批处理大小
        defaultContinueOnError: boolean;  // 默认错误时是否继续
    };

    // 调试配置
    debug: {
        enableVerboseLogging: boolean;    // 启用详细日志
        logExecutionTime: boolean;        // 记录执行时间
        enablePerformanceMetrics: boolean; // 启用性能指标
    };
}

/**
 * 默认配置
 */
export const defaultToolSystemConfig: ToolSystemConfig = {
    cache: {
        snapshotMaxAge: 2000,             // 5秒
        maxConsoleMessages: 100,         // 1000条消息
        maxNetworkRequests: 100           // 500个请求
    },

    pagination: {
        defaultPageSize: 20,              // 默认20条
        maxPageSize: 100                  // 最大100条
    },

    timeouts: {
        defaultElementTimeout: 5000,     // 30秒
        defaultNavigationTimeout: 30000,  // 60秒
        defaultActionTimeout: 5000       // 30秒
    },

    screenshot: {
        defaultFormat: 'png',             // PNG格式
        defaultQuality: 90,               // 90%质量
        maxFileSize: 1 * 1024 * 1024     // 10MB
    },

    batch: {
        maxBatchSize: 50,                 // 最大50个操作
        defaultContinueOnError: false     // 默认遇错停止
    },

    debug: {
        enableVerboseLogging: false,      // 关闭详细日志
        logExecutionTime: true,           // 记录执行时间
        enablePerformanceMetrics: false   // 关闭性能指标
    }
};

/**
 * 配置管理器
 */
export class ToolSystemConfigManager {
    private static config: ToolSystemConfig = { ...defaultToolSystemConfig };

    /**
     * 获取当前配置
     */
    static getConfig(): ToolSystemConfig {
        return { ...this.config };
    }

    /**
     * 更新配置
     */
    static updateConfig(updates: Partial<ToolSystemConfig>): void {
        this.config = this.mergeConfig(this.config, updates);
    }

    /**
     * 重置为默认配置
     */
    static resetToDefault(): void {
        this.config = { ...defaultToolSystemConfig };
    }

    /**
     * 获取特定配置项
     */
    static getCacheConfig() {
        return this.config.cache;
    }

    static getPaginationConfig() {
        return this.config.pagination;
    }

    static getTimeoutConfig() {
        return this.config.timeouts;
    }

    static getScreenshotConfig() {
        return this.config.screenshot;
    }

    static getBatchConfig() {
        return this.config.batch;
    }

    static getDebugConfig() {
        return this.config.debug;
    }

    /**
     * 深度合并配置
     */
    private static mergeConfig(
        target: ToolSystemConfig,
        source: Partial<ToolSystemConfig>
    ): ToolSystemConfig {
        const result = { ...target };

        for (const key in source) {
            const sourceValue = source[key as keyof ToolSystemConfig];
            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
                result[key as keyof ToolSystemConfig] = {
                    ...result[key as keyof ToolSystemConfig],
                    ...sourceValue
                } as any;
            } else if (sourceValue !== undefined) {
                (result as any)[key] = sourceValue;
            }
        }

        return result;
    }

    /**
     * 验证配置
     */
    static validateConfig(config: Partial<ToolSystemConfig>): string[] {
        const errors: string[] = [];

        if (config.cache) {
            if (config.cache.snapshotMaxAge && config.cache.snapshotMaxAge < 0) {
                errors.push('snapshotMaxAge must be non-negative');
            }
            if (config.cache.maxConsoleMessages && config.cache.maxConsoleMessages < 1) {
                errors.push('maxConsoleMessages must be positive');
            }
            if (config.cache.maxNetworkRequests && config.cache.maxNetworkRequests < 1) {
                errors.push('maxNetworkRequests must be positive');
            }
        }

        if (config.pagination) {
            if (config.pagination.defaultPageSize && config.pagination.defaultPageSize < 1) {
                errors.push('defaultPageSize must be positive');
            }
            if (config.pagination.maxPageSize && config.pagination.maxPageSize < 1) {
                errors.push('maxPageSize must be positive');
            }
        }

        if (config.timeouts) {
            if (config.timeouts.defaultElementTimeout && config.timeouts.defaultElementTimeout < 0) {
                errors.push('defaultElementTimeout must be non-negative');
            }
        }

        if (config.screenshot) {
            if (config.screenshot.defaultQuality &&
                (config.screenshot.defaultQuality < 1 || config.screenshot.defaultQuality > 100)) {
                errors.push('defaultQuality must be between 1 and 100');
            }
        }

        return errors;
    }
}