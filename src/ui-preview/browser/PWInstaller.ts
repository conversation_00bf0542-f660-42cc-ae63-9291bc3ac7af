import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as util from 'util';
import * as http from 'http';
import * as https from 'https';
import * as crypto from 'crypto';
import { execSync } from 'child_process';
import { URL } from 'url';

import * as extract from 'extract-zip';
import { ExceptionCode, getExceptionMessage } from './exception-codes';
import { LoggerManager } from '../LoggerManager';

// === Utility Functions (Integrated) ===

// From crypto.ts
function calculateSha1(buffer: Buffer | string): string {
    const hash = crypto.createHash('sha1');
    hash.update(buffer);
    return hash.digest('hex');
}

// From fileUtils.ts
const existsAsync = (path: string): Promise<boolean> =>
    new Promise((resolve) => fs.stat(path, (err) => resolve(!err)));

// From stackTrace.ts (simplified)
// Note: captureRawStack removed as it's not used in this simplified version

// From manualPromise.ts
class ManualPromise<T = void> {
    private _resolve!: (t: T) => void;
    private _reject!: (e: Error) => void;
    private _isDone: boolean;
    private _promise: Promise<T>;

    constructor() {
        this._isDone = false;
        this._promise = new Promise<T>((resolve, reject) => {
            this._resolve = resolve;
            this._reject = reject;
        });
    }

    isDone() {
        return this._isDone;
    }

    resolve(t: T) {
        this._isDone = true;
        this._resolve(t);
    }

    reject(e: Error) {
        this._isDone = true;
        this._reject(e);
    }

    then<TResult1 = T, TResult2 = never>(
        onfulfilled?:
            | ((value: T) => TResult1 | PromiseLike<TResult1>)
            | undefined
            | null,
        onrejected?:
            | ((reason: any) => TResult2 | PromiseLike<TResult2>)
            | undefined
            | null
    ): Promise<TResult1 | TResult2> {
        return this._promise.then(onfulfilled, onrejected);
    }

    catch<TResult = never>(
        onrejected?:
            | ((reason: any) => TResult | PromiseLike<TResult>)
            | undefined
            | null
    ): Promise<T | TResult> {
        return this._promise.catch(onrejected);
    }

    finally(onfinally?: (() => void) | undefined | null): Promise<T> {
        return this._promise.finally(onfinally);
    }

    get [Symbol.toStringTag]() {
        return 'ManualPromise';
    }
}

// From linuxUtils.ts
let didFailToReadOSRelease = false;
let osRelease:
    | {
        id: string;
        version: string;
    }
    | undefined;

function getLinuxDistributionInfoSync():
    | { id: string; version: string }
    | undefined {
    if (process.platform !== 'linux') return undefined;
    if (!osRelease && !didFailToReadOSRelease) {
        try {
            const osReleaseText = fs.readFileSync('/etc/os-release', 'utf8');
            const fields = parseOSReleaseText(osReleaseText);
            osRelease = {
                id: fields.get('id') ?? '',
                version: fields.get('version_id') ?? '',
            };
        } catch (e) {
            didFailToReadOSRelease = true;
        }
    }
    return osRelease;
}

function parseOSReleaseText(osReleaseText: string): Map<string, string> {
    const fields = new Map();
    for (const line of osReleaseText.split('\n')) {
        const tokens = line.split('=');
        const name = tokens.shift();
        let value = tokens.join('=').trim();
        if (value.startsWith('"') && value.endsWith('"'))
            value = value.substring(1, value.length - 1);
        if (!name) continue;
        fields.set(name.toLowerCase(), value);
    }
    return fields;
}

// Platform calculation cache
let platformCache: {
    hostPlatform: HostPlatform;
    isOfficiallySupportedPlatform: boolean;
} | null = null;

// From hostPlatform.ts
function calculatePlatform(): {
    hostPlatform: HostPlatform;
    isOfficiallySupportedPlatform: boolean;
} {
    if (platformCache) {
        return platformCache;
    }
    const platform = os.platform();
    if (platform === 'darwin') {
        const ver = os
            .release()
            .split('.')
            .map((a: string) => parseInt(a, 10));
        let macVersion = '';
        if (ver[0] < 18) {
            macVersion = 'mac10.13';
        } else if (ver[0] === 18) {
            macVersion = 'mac10.14';
        } else if (ver[0] === 19) {
            macVersion = 'mac10.15';
        } else {
            const LAST_STABLE_MACOS_MAJOR_VERSION = 15;
            macVersion =
                'mac' + Math.min(ver[0] - 9, LAST_STABLE_MACOS_MAJOR_VERSION);
            if (os.cpus().some((cpu) => cpu.model.includes('Apple')))
                macVersion += '-arm64';
        }
        const result = {
            hostPlatform: macVersion as HostPlatform,
            isOfficiallySupportedPlatform: true,
        };
        platformCache = result;
        return result;
    }
    if (platform === 'linux') {
        if (!['x64', 'arm64'].includes(os.arch())) {
            platformCache = {
                hostPlatform: '<unknown>',
                isOfficiallySupportedPlatform: false,
            };
            return platformCache;
        }

        const archSuffix = '-' + os.arch();
        const distroInfo = getLinuxDistributionInfoSync();

        if (
            distroInfo?.id === 'ubuntu' ||
            distroInfo?.id === 'pop' ||
            distroInfo?.id === 'neon' ||
            distroInfo?.id === 'tuxedo'
        ) {
            const isUbuntu = distroInfo?.id === 'ubuntu';
            const version = distroInfo?.version;
            const major = parseInt(distroInfo.version, 10);
            if (major < 20)
                return {
                    hostPlatform: ('ubuntu18.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform: false,
                };
            if (major < 22)
                return {
                    hostPlatform: ('ubuntu20.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform:
                        isUbuntu && version === '20.04',
                };
            if (major < 24)
                return {
                    hostPlatform: ('ubuntu22.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform:
                        isUbuntu && version === '22.04',
                };
            if (major < 26)
                return {
                    hostPlatform: ('ubuntu24.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform:
                        isUbuntu && version === '24.04',
                };
            return {
                hostPlatform: ('ubuntu' +
                    distroInfo.version +
                    archSuffix) as HostPlatform,
                isOfficiallySupportedPlatform: false,
            };
        }
        if (distroInfo?.id === 'linuxmint') {
            const mintMajor = parseInt(distroInfo.version, 10);
            if (mintMajor <= 20)
                return {
                    hostPlatform: ('ubuntu20.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform: false,
                };
            if (mintMajor === 21)
                return {
                    hostPlatform: ('ubuntu22.04' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform: false,
                };
            return {
                hostPlatform: ('ubuntu24.04' + archSuffix) as HostPlatform,
                isOfficiallySupportedPlatform: false,
            };
        }
        if (distroInfo?.id === 'debian' || distroInfo?.id === 'raspbian') {
            const isOfficiallySupportedPlatform = distroInfo?.id === 'debian';
            if (distroInfo?.version === '11')
                return {
                    hostPlatform: ('debian11' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform,
                };
            if (distroInfo?.version === '12')
                return {
                    hostPlatform: ('debian12' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform,
                };
            if (distroInfo?.version === '')
                return {
                    hostPlatform: ('debian12' + archSuffix) as HostPlatform,
                    isOfficiallySupportedPlatform,
                };
        }
        return {
            hostPlatform: ('ubuntu20.04' + archSuffix) as HostPlatform,
            isOfficiallySupportedPlatform: false,
        };
    }
    if (platform === 'win32')
        return { hostPlatform: 'win64', isOfficiallySupportedPlatform: true };
    return { hostPlatform: '<unknown>', isOfficiallySupportedPlatform: false };
}

const { hostPlatform, isOfficiallySupportedPlatform } = calculatePlatform(); // eslint-disable-line @typescript-eslint/no-unused-vars


// Cache for user agents with different versions
const userAgentCache = new Map<string, string>();

function getUserAgent(providedVersion?: string): string {
    const cacheKey = providedVersion || 'default';

    if (userAgentCache.has(cacheKey)) {
        return userAgentCache.get(cacheKey)!;
    }

    try {
        const userAgent = determineUserAgent(providedVersion);
        userAgentCache.set(cacheKey, userAgent);
        return userAgent;
    } catch (e) {
        const fallbackUserAgent = 'Playwright/unknown';
        userAgentCache.set(cacheKey, fallbackUserAgent);
        return fallbackUserAgent;
    }
}

function determineUserAgent(providedVersion?: string): string {
    let osIdentifier = 'unknown';
    let osVersion = 'unknown';
    if (process.platform === 'win32') {
        const version = os.release().split('.');
        osIdentifier = 'windows';
        osVersion = `${version[0]}.${version[1]}`;
    } else if (process.platform === 'darwin') {
        const version = execSync('sw_vers -productVersion', {
            stdio: ['ignore', 'pipe', 'ignore'],
        })
            .toString()
            .trim()
            .split('.');
        osIdentifier = 'macOS';
        osVersion = `${version[0]}.${version[1]}`;
    } else if (process.platform === 'linux') {
        const distroInfo = getLinuxDistributionInfoSync();
        if (distroInfo) {
            osIdentifier = distroInfo.id || 'linux';
            osVersion = distroInfo.version || 'unknown';
        } else {
            osIdentifier = 'linux';
        }
    }
    const additionalTokens: string[] = [];
    const serializedTokens = additionalTokens.length
        ? ' ' + additionalTokens.join(' ')
        : '';

    const { embedderName, embedderVersion } = getEmbedderName();
    return `Playwright/${getPlaywrightVersion(false, providedVersion)} (${os.arch()}; ${osIdentifier} ${osVersion}) ${embedderName}/${embedderVersion}${serializedTokens}`;
}

function getEmbedderName(): { embedderName: string; embedderVersion: string } {
    const embedderName = 'node';
    const embedderVersion = process.version
        .substring(1)
        .split('.')
        .slice(0, 2)
        .join('.');
    return { embedderName, embedderVersion };
}

function validateVersion(version: string): boolean {
    // 验证版本格式：x.y.z 或 x.y.z-xxx
    const versionRegex = /^\d+\.\d+\.\d+(?:-[\w\-\.]+)?$/;
    return versionRegex.test(version);
}

function formatFileSize(bytes: number): string {
    return (bytes / 1024 / 1024).toFixed(2);
}

function createProgressMessage(downloadedBytes: number, totalBytes: number): string {
    const progress = totalBytes > 0 ? ((downloadedBytes / totalBytes) * 100).toFixed(1) : '0';
    const downloadedMB = formatFileSize(downloadedBytes);
    const totalMB = formatFileSize(totalBytes);
    return `⏳ 下载进度: ${progress}% (${downloadedMB}/${totalMB} MB)`;
}

function getPlaywrightVersion(majorMinorOnly = false, providedVersion?: string): string {
    if (!providedVersion) {
        throw new Error(`${getExceptionMessage(ExceptionCode.VALIDATION)}: Playwright version is required. (code: ${ExceptionCode.VALIDATION})`);
    }

    if (!validateVersion(providedVersion)) {
        throw new Error(`${getExceptionMessage(ExceptionCode.VALIDATION)}: Invalid version format: "${providedVersion}". (code: ${ExceptionCode.VALIDATION})`);
    }

    return majorMinorOnly ? providedVersion.split('.').slice(0, 2).join('.') : providedVersion;
}

// Simple file locking implementation (replaces lockfile dependency)
class SimpleLockfile {
    static async lock(
        directory: string,
        options: any
    ): Promise<() => Promise<void>> {
        const lockPath = options.lockfilePath;
        let retries = options.retries?.retries || LOCK_RETRY_COUNT;
        const factor = options.retries?.factor || DOWNLOAD_RETRY_FACTOR;

        while (retries-- > 0) {
            try {
                await fs.promises.writeFile(lockPath, process.pid.toString(), {
                    flag: 'wx',
                });
                return async () => {
                    try {
                        await fs.promises.unlink(lockPath);
                    } catch (e) {
                        // Ignore errors when unlinking
                    }
                };
            } catch (e: any) {
                if (e.code !== 'EEXIST') {
                    throw e;
                }
                if (retries === 0) {
                    const error: any = new Error('Could not acquire lock');
                    error.code = 'ELOCKED';
                    throw error;
                }
                // Wait with exponential backoff
                const delay =
                    Math.pow(factor, options.retries.retries - retries) * 100;
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error(`${getExceptionMessage(ExceptionCode.INTERNAL)}: Lock acquisition failed (code: ${ExceptionCode.INTERNAL})`);
    }
}

const lockfile = { lock: SimpleLockfile.lock };

// === Types ===
type HostPlatform =
    | 'ubuntu18.04-x64'
    | 'ubuntu20.04-x64'
    | 'ubuntu22.04-x64'
    | 'ubuntu24.04-x64'
    | 'ubuntu18.04-arm64'
    | 'ubuntu20.04-arm64'
    | 'ubuntu22.04-arm64'
    | 'ubuntu24.04-arm64'
    | 'debian11-x64'
    | 'debian11-arm64'
    | 'debian12-x64'
    | 'debian12-arm64'
    | 'mac10.13'
    | 'mac10.14'
    | 'mac10.15'
    | 'mac11'
    | 'mac11-arm64'
    | 'mac12'
    | 'mac12-arm64'
    | 'mac13'
    | 'mac13-arm64'
    | 'mac14'
    | 'mac14-arm64'
    | 'mac15'
    | 'mac15-arm64'
    | 'win64'
    | '<unknown>';

type DownloadPaths = Record<HostPlatform, string | undefined>;
type BrowserName =
    | 'chromium'
    | 'firefox'
    | 'webkit'
    | '_bidiFirefox'
    | '_bidiChromium';
type InternalTool =
    | 'ffmpeg'
    | 'winldd'
    | 'firefox-beta'
    | 'chromium-tip-of-tree'
    | 'chromium-headless-shell'
    | 'chromium-tip-of-tree-headless-shell'
    | 'android';

type BrowsersJSON = {
    comment: string;
    browsers: {
        name: string;
        revision: string;
        browserVersion?: string;
        installByDefault: boolean;
        revisionOverrides?: { [os: string]: string };
    }[];
};

type BrowsersJSONDescriptor = {
    name: string;
    revision: string;
    hasRevisionOverride: boolean;
    browserVersion?: string;
    installByDefault: boolean;
    dir: string;
};

interface Executable {
    type: 'browser' | 'tool' | 'channel';
    name: BrowserName | InternalTool;
    browserName: BrowserName | undefined;
    installType:
    | 'download-by-default'
    | 'download-on-demand'
    | 'install-script'
    | 'none';
    directory: string | undefined;
    downloadURLs?: string[];
    browserVersion?: string;
    executablePathOrDie(sdkLanguage: string): string;
    executablePath(sdkLanguage: string): string | undefined;
    _validateHostRequirements(sdkLanguage: string): Promise<void>;
}

interface ExecutableImpl extends Executable {
    _install?: () => Promise<void>;
    _dependencyGroup?: string;
    _isHermeticInstallation?: boolean;
}

// === Constants ===
const PACKAGE_PATH = path.join(__dirname, '..', '..', '..');
const NET_DEFAULT_TIMEOUT = 30000;
const DOWNLOAD_RETRY_COUNT = 5;
const DOWNLOAD_RETRY_FACTOR = 1.27579;
const LOCK_RETRY_COUNT = 20;
const PROGRESS_UPDATE_INTERVAL = 1000; // ms
const INSTALLATION_COMPLETE_MARKER = 'INSTALLATION_COMPLETE';

const PLAYWRIGHT_CDN_MIRRORS = [
    'https://cdn.playwright.dev/dbazure/download/playwright',
    'https://playwright.download.prss.microsoft.com/dbazure/download/playwright',
    'https://cdn.playwright.dev',
];

const DOWNLOAD_ENV_VARS = {
    CHROMIUM: 'PLAYWRIGHT_CHROMIUM_DOWNLOAD_HOST',
    FIREFOX: 'PLAYWRIGHT_FIREFOX_DOWNLOAD_HOST',
    WEBKIT: 'PLAYWRIGHT_WEBKIT_DOWNLOAD_HOST',
    GENERAL: 'PLAYWRIGHT_DOWNLOAD_HOST',
    TIMEOUT: 'PLAYWRIGHT_DOWNLOAD_CONNECTION_TIMEOUT'
} as const;

type DownloadEnvVar = typeof DOWNLOAD_ENV_VARS[keyof typeof DOWNLOAD_ENV_VARS];

// 日志级别枚举
enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3
}

// 进度回调类型
type ProgressCallback = (progress: {
    phase: 'download' | 'extract' | 'complete';
    browserName: string;
    downloadedBytes?: number;
    totalBytes?: number;
    percentage?: number;
    message: string;
}) => void;

// 安装配置接口
// interface InstallationConfig {
//     timeout?: number;
//     version?: string;
//     maxRetries?: number;
//     logLevel?: LogLevel;
//     onProgress?: ProgressCallback;
//     skipExisting?: boolean;
// }

// Enhanced types for better type safety
interface DownloadOptions {
    readonly url: string;
    readonly zipPath: string;
    readonly userAgent: string;
    readonly socketTimeout: number;
}

/**
 * 通用配置参数 - 在整个安装过程中复用的配置
 * 
 * 设计优势：
 * 1. 避免参数重复传递
 * 2. 集中管理通用配置
 * 3. 便于添加新的通用参数
 */
interface CommonInstallOptions {
    readonly socketTimeout: number;
    readonly playwrightVersion?: string;
}

/**
 * 特定浏览器下载参数 - 每个浏览器独有的配置
 * 
 * 设计优势：
 * 1. 明确区分浏览器特定配置
 * 2. 便于并行处理多个浏览器
 * 3. 类型安全，避免配置混淆
 */
interface BrowserSpecificOptions {
    readonly browserDirectory: string;
    readonly executablePath: string | undefined;
    readonly downloadURLs: readonly string[];
    readonly downloadFileName: string;
}

const EXECUTABLE_PATHS = {
    chromium: {
        linux: ['chrome-linux', 'chrome'],
        mac: ['chrome-mac', 'Chromium.app', 'Contents', 'MacOS', 'Chromium'],
        win: ['chrome-win', 'chrome.exe'],
    },
    'chromium-headless-shell': {
        linux: ['chrome-linux', 'headless_shell'],
        mac: ['chrome-mac', 'headless_shell'],
        win: ['chrome-win', 'headless_shell.exe'],
    },
    firefox: {
        linux: ['firefox', 'firefox'],
        mac: ['firefox', 'Nightly.app', 'Contents', 'MacOS', 'firefox'],
        win: ['firefox', 'firefox.exe'],
    },
    webkit: {
        linux: ['pw_run.sh'],
        mac: ['pw_run.sh'],
        win: ['Playwright.exe'],
    },
    ffmpeg: {
        linux: ['ffmpeg-linux'],
        mac: ['ffmpeg-mac'],
        win: ['ffmpeg-win64.exe'],
    },
    winldd: {
        linux: undefined,
        mac: undefined,
        win: ['PrintDeps.exe'],
    },
};

const DOWNLOAD_PATHS: Record<BrowserName | InternalTool, DownloadPaths> = {
    chromium: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64': 'builds/chromium/%s/chromium-linux.zip',
        'ubuntu22.04-x64': 'builds/chromium/%s/chromium-linux.zip',
        'ubuntu24.04-x64': 'builds/chromium/%s/chromium-linux.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': 'builds/chromium/%s/chromium-linux-arm64.zip',
        'ubuntu22.04-arm64': 'builds/chromium/%s/chromium-linux-arm64.zip',
        'ubuntu24.04-arm64': 'builds/chromium/%s/chromium-linux-arm64.zip',
        'debian11-x64': 'builds/chromium/%s/chromium-linux.zip',
        'debian11-arm64': 'builds/chromium/%s/chromium-linux-arm64.zip',
        'debian12-x64': 'builds/chromium/%s/chromium-linux.zip',
        'debian12-arm64': 'builds/chromium/%s/chromium-linux-arm64.zip',
        'mac10.13': 'builds/chromium/%s/chromium-mac.zip',
        'mac10.14': 'builds/chromium/%s/chromium-mac.zip',
        'mac10.15': 'builds/chromium/%s/chromium-mac.zip',
        mac11: 'builds/chromium/%s/chromium-mac.zip',
        'mac11-arm64': 'builds/chromium/%s/chromium-mac-arm64.zip',
        mac12: 'builds/chromium/%s/chromium-mac.zip',
        'mac12-arm64': 'builds/chromium/%s/chromium-mac-arm64.zip',
        mac13: 'builds/chromium/%s/chromium-mac.zip',
        'mac13-arm64': 'builds/chromium/%s/chromium-mac-arm64.zip',
        mac14: 'builds/chromium/%s/chromium-mac.zip',
        'mac14-arm64': 'builds/chromium/%s/chromium-mac-arm64.zip',
        mac15: 'builds/chromium/%s/chromium-mac.zip',
        'mac15-arm64': 'builds/chromium/%s/chromium-mac-arm64.zip',
        win64: 'builds/chromium/%s/chromium-win64.zip',
    },
    'chromium-headless-shell': {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64':
            'builds/chromium/%s/chromium-headless-shell-linux.zip',
        'ubuntu22.04-x64':
            'builds/chromium/%s/chromium-headless-shell-linux.zip',
        'ubuntu24.04-x64':
            'builds/chromium/%s/chromium-headless-shell-linux.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64':
            'builds/chromium/%s/chromium-headless-shell-linux-arm64.zip',
        'ubuntu22.04-arm64':
            'builds/chromium/%s/chromium-headless-shell-linux-arm64.zip',
        'ubuntu24.04-arm64':
            'builds/chromium/%s/chromium-headless-shell-linux-arm64.zip',
        'debian11-x64': 'builds/chromium/%s/chromium-headless-shell-linux.zip',
        'debian11-arm64':
            'builds/chromium/%s/chromium-headless-shell-linux-arm64.zip',
        'debian12-x64': 'builds/chromium/%s/chromium-headless-shell-linux.zip',
        'debian12-arm64':
            'builds/chromium/%s/chromium-headless-shell-linux-arm64.zip',
        'mac10.13': undefined,
        'mac10.14': undefined,
        'mac10.15': undefined,
        mac11: 'builds/chromium/%s/chromium-headless-shell-mac.zip',
        'mac11-arm64':
            'builds/chromium/%s/chromium-headless-shell-mac-arm64.zip',
        mac12: 'builds/chromium/%s/chromium-headless-shell-mac.zip',
        'mac12-arm64':
            'builds/chromium/%s/chromium-headless-shell-mac-arm64.zip',
        mac13: 'builds/chromium/%s/chromium-headless-shell-mac.zip',
        'mac13-arm64':
            'builds/chromium/%s/chromium-headless-shell-mac-arm64.zip',
        mac14: 'builds/chromium/%s/chromium-headless-shell-mac.zip',
        'mac14-arm64':
            'builds/chromium/%s/chromium-headless-shell-mac-arm64.zip',
        mac15: 'builds/chromium/%s/chromium-headless-shell-mac.zip',
        'mac15-arm64':
            'builds/chromium/%s/chromium-headless-shell-mac-arm64.zip',
        win64: 'builds/chromium/%s/chromium-headless-shell-win64.zip',
    },
    firefox: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64': 'builds/firefox/%s/firefox-ubuntu-20.04.zip',
        'ubuntu22.04-x64': 'builds/firefox/%s/firefox-ubuntu-22.04.zip',
        'ubuntu24.04-x64': 'builds/firefox/%s/firefox-ubuntu-24.04.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': 'builds/firefox/%s/firefox-ubuntu-20.04-arm64.zip',
        'ubuntu22.04-arm64': 'builds/firefox/%s/firefox-ubuntu-22.04-arm64.zip',
        'ubuntu24.04-arm64': 'builds/firefox/%s/firefox-ubuntu-24.04-arm64.zip',
        'debian11-x64': 'builds/firefox/%s/firefox-debian-11.zip',
        'debian11-arm64': 'builds/firefox/%s/firefox-debian-11-arm64.zip',
        'debian12-x64': 'builds/firefox/%s/firefox-debian-12.zip',
        'debian12-arm64': 'builds/firefox/%s/firefox-debian-12-arm64.zip',
        'mac10.13': 'builds/firefox/%s/firefox-mac.zip',
        'mac10.14': 'builds/firefox/%s/firefox-mac.zip',
        'mac10.15': 'builds/firefox/%s/firefox-mac.zip',
        mac11: 'builds/firefox/%s/firefox-mac.zip',
        'mac11-arm64': 'builds/firefox/%s/firefox-mac-arm64.zip',
        mac12: 'builds/firefox/%s/firefox-mac.zip',
        'mac12-arm64': 'builds/firefox/%s/firefox-mac-arm64.zip',
        mac13: 'builds/firefox/%s/firefox-mac.zip',
        'mac13-arm64': 'builds/firefox/%s/firefox-mac-arm64.zip',
        mac14: 'builds/firefox/%s/firefox-mac.zip',
        'mac14-arm64': 'builds/firefox/%s/firefox-mac-arm64.zip',
        mac15: 'builds/firefox/%s/firefox-mac.zip',
        'mac15-arm64': 'builds/firefox/%s/firefox-mac-arm64.zip',
        win64: 'builds/firefox/%s/firefox-win64.zip',
    },
    webkit: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': 'builds/webkit/%s/webkit-ubuntu-18.04.zip',
        'ubuntu20.04-x64': 'builds/webkit/%s/webkit-ubuntu-20.04.zip',
        'ubuntu22.04-x64': 'builds/webkit/%s/webkit-ubuntu-22.04.zip',
        'ubuntu24.04-x64': 'builds/webkit/%s/webkit-ubuntu-24.04.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': 'builds/webkit/%s/webkit-ubuntu-20.04-arm64.zip',
        'ubuntu22.04-arm64': 'builds/webkit/%s/webkit-ubuntu-22.04-arm64.zip',
        'ubuntu24.04-arm64': 'builds/webkit/%s/webkit-ubuntu-24.04-arm64.zip',
        'debian11-x64': 'builds/webkit/%s/webkit-debian-11.zip',
        'debian11-arm64': 'builds/webkit/%s/webkit-debian-11-arm64.zip',
        'debian12-x64': 'builds/webkit/%s/webkit-debian-12.zip',
        'debian12-arm64': 'builds/webkit/%s/webkit-debian-12-arm64.zip',
        'mac10.13': undefined,
        'mac10.14': undefined,
        'mac10.15': 'builds/webkit/%s/webkit-mac-10.15.zip',
        mac11: 'builds/webkit/%s/webkit-mac-11.zip',
        'mac11-arm64': 'builds/webkit/%s/webkit-mac-11-arm64.zip',
        mac12: 'builds/webkit/%s/webkit-mac-12.zip',
        'mac12-arm64': 'builds/webkit/%s/webkit-mac-12-arm64.zip',
        mac13: 'builds/webkit/%s/webkit-mac-13.zip',
        'mac13-arm64': 'builds/webkit/%s/webkit-mac-13-arm64.zip',
        mac14: 'builds/webkit/%s/webkit-mac-14.zip',
        'mac14-arm64': 'builds/webkit/%s/webkit-mac-14-arm64.zip',
        mac15: 'builds/webkit/%s/webkit-mac-15.zip',
        'mac15-arm64': 'builds/webkit/%s/webkit-mac-15-arm64.zip',
        win64: 'builds/webkit/%s/webkit-win64.zip',
    },
    ffmpeg: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64': 'builds/ffmpeg/%s/ffmpeg-linux.zip',
        'ubuntu22.04-x64': 'builds/ffmpeg/%s/ffmpeg-linux.zip',
        'ubuntu24.04-x64': 'builds/ffmpeg/%s/ffmpeg-linux.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': 'builds/ffmpeg/%s/ffmpeg-linux-arm64.zip',
        'ubuntu22.04-arm64': 'builds/ffmpeg/%s/ffmpeg-linux-arm64.zip',
        'ubuntu24.04-arm64': 'builds/ffmpeg/%s/ffmpeg-linux-arm64.zip',
        'debian11-x64': 'builds/ffmpeg/%s/ffmpeg-linux.zip',
        'debian11-arm64': 'builds/ffmpeg/%s/ffmpeg-linux-arm64.zip',
        'debian12-x64': 'builds/ffmpeg/%s/ffmpeg-linux.zip',
        'debian12-arm64': 'builds/ffmpeg/%s/ffmpeg-linux-arm64.zip',
        'mac10.13': 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac10.14': 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac10.15': 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        mac11: 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac11-arm64': 'builds/ffmpeg/%s/ffmpeg-mac-arm64.zip',
        mac12: 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac12-arm64': 'builds/ffmpeg/%s/ffmpeg-mac-arm64.zip',
        mac13: 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac13-arm64': 'builds/ffmpeg/%s/ffmpeg-mac-arm64.zip',
        mac14: 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac14-arm64': 'builds/ffmpeg/%s/ffmpeg-mac-arm64.zip',
        mac15: 'builds/ffmpeg/%s/ffmpeg-mac.zip',
        'mac15-arm64': 'builds/ffmpeg/%s/ffmpeg-mac-arm64.zip',
        win64: 'builds/ffmpeg/%s/ffmpeg-win64.zip',
    },
    winldd: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64': undefined,
        'ubuntu22.04-x64': undefined,
        'ubuntu24.04-x64': undefined,
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': undefined,
        'ubuntu22.04-arm64': undefined,
        'ubuntu24.04-arm64': undefined,
        'debian11-x64': undefined,
        'debian11-arm64': undefined,
        'debian12-x64': undefined,
        'debian12-arm64': undefined,
        'mac10.13': undefined,
        'mac10.14': undefined,
        'mac10.15': undefined,
        mac11: undefined,
        'mac11-arm64': undefined,
        mac12: undefined,
        'mac12-arm64': undefined,
        mac13: undefined,
        'mac13-arm64': undefined,
        mac14: undefined,
        'mac14-arm64': undefined,
        mac15: undefined,
        'mac15-arm64': undefined,
        win64: 'builds/winldd/%s/winldd-win64.zip',
    },
    'firefox-beta': {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-20.04.zip',
        'ubuntu22.04-x64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-22.04.zip',
        'ubuntu24.04-x64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-24.04.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-20.04-arm64.zip',
        'ubuntu22.04-arm64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-22.04-arm64.zip',
        'ubuntu24.04-arm64':
            'builds/firefox-beta/%s/firefox-beta-ubuntu-24.04-arm64.zip',
        'debian11-x64': 'builds/firefox-beta/%s/firefox-beta-debian-11.zip',
        'debian11-arm64':
            'builds/firefox-beta/%s/firefox-beta-debian-11-arm64.zip',
        'debian12-x64': 'builds/firefox-beta/%s/firefox-beta-debian-12.zip',
        'debian12-arm64':
            'builds/firefox-beta/%s/firefox-beta-debian-12-arm64.zip',
        'mac10.13': 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac10.14': 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac10.15': 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        mac11: 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac11-arm64': 'builds/firefox-beta/%s/firefox-beta-mac-arm64.zip',
        mac12: 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac12-arm64': 'builds/firefox-beta/%s/firefox-beta-mac-arm64.zip',
        mac13: 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac13-arm64': 'builds/firefox-beta/%s/firefox-beta-mac-arm64.zip',
        mac14: 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac14-arm64': 'builds/firefox-beta/%s/firefox-beta-mac-arm64.zip',
        mac15: 'builds/firefox-beta/%s/firefox-beta-mac.zip',
        'mac15-arm64': 'builds/firefox-beta/%s/firefox-beta-mac-arm64.zip',
        win64: 'builds/firefox-beta/%s/firefox-beta-win64.zip',
    },
    'chromium-tip-of-tree': {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux.zip',
        'ubuntu22.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux.zip',
        'ubuntu24.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux-arm64.zip',
        'ubuntu22.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux-arm64.zip',
        'ubuntu24.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux-arm64.zip',
        'debian11-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux.zip',
        'debian11-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux-arm64.zip',
        'debian12-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux.zip',
        'debian12-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-linux-arm64.zip',
        'mac10.13':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac10.14':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac10.15':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        mac11: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac11-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac-arm64.zip',
        mac12: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac12-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac-arm64.zip',
        mac13: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac13-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac-arm64.zip',
        mac14: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac14-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac-arm64.zip',
        mac15: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac.zip',
        'mac15-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-mac-arm64.zip',
        win64: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-win64.zip',
    },
    'chromium-tip-of-tree-headless-shell': {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux.zip',
        'ubuntu22.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux.zip',
        'ubuntu24.04-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux-arm64.zip',
        'ubuntu22.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux-arm64.zip',
        'ubuntu24.04-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux-arm64.zip',
        'debian11-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux.zip',
        'debian11-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux-arm64.zip',
        'debian12-x64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux.zip',
        'debian12-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-linux-arm64.zip',
        'mac10.13':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac10.14':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac10.15':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        mac11: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac11-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac-arm64.zip',
        mac12: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac12-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac-arm64.zip',
        mac13: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac13-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac-arm64.zip',
        mac14: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac14-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac-arm64.zip',
        mac15: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac.zip',
        'mac15-arm64':
            'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-mac-arm64.zip',
        win64: 'builds/chromium-tip-of-tree/%s/chromium-tip-of-tree-headless-shell-win64.zip',
    },
    android: {
        '<unknown>': undefined,
        'ubuntu18.04-x64': undefined,
        'ubuntu20.04-x64': 'builds/android/%s/android.zip',
        'ubuntu22.04-x64': 'builds/android/%s/android.zip',
        'ubuntu24.04-x64': 'builds/android/%s/android.zip',
        'ubuntu18.04-arm64': undefined,
        'ubuntu20.04-arm64': 'builds/android/%s/android.zip',
        'ubuntu22.04-arm64': 'builds/android/%s/android.zip',
        'ubuntu24.04-arm64': 'builds/android/%s/android.zip',
        'debian11-x64': 'builds/android/%s/android.zip',
        'debian11-arm64': 'builds/android/%s/android.zip',
        'debian12-x64': 'builds/android/%s/android.zip',
        'debian12-arm64': 'builds/android/%s/android.zip',
        'mac10.13': 'builds/android/%s/android.zip',
        'mac10.14': 'builds/android/%s/android.zip',
        'mac10.15': 'builds/android/%s/android.zip',
        mac11: 'builds/android/%s/android.zip',
        'mac11-arm64': 'builds/android/%s/android.zip',
        mac12: 'builds/android/%s/android.zip',
        'mac12-arm64': 'builds/android/%s/android.zip',
        mac13: 'builds/android/%s/android.zip',
        'mac13-arm64': 'builds/android/%s/android.zip',
        mac14: 'builds/android/%s/android.zip',
        'mac14-arm64': 'builds/android/%s/android.zip',
        mac15: 'builds/android/%s/android.zip',
        'mac15-arm64': 'builds/android/%s/android.zip',
        win64: 'builds/android/%s/android.zip',
    },
    _bidiFirefox: {} as DownloadPaths,
    _bidiChromium: {} as DownloadPaths,
};

function getRegistryDirectory(customPath?: string): string {
    // 如果提供了自定义路径，优先使用
    if (customPath) {
        if (customPath === "0") {
            return path.join(__dirname, "..", "..", "..", ".local-browsers");
        }
        return path.isAbsolute(customPath) ? customPath : path.resolve(process.cwd(), customPath);
    }

    // 默认缓存目录
    let cacheDirectory: string;
    if (process.platform === "linux")
        cacheDirectory = path.join(os.homedir(), ".cache");
    else if (process.platform === "darwin")
        cacheDirectory = path.join(os.homedir(), "Library", "Caches");
    else if (process.platform === "win32")
        cacheDirectory = path.join(os.homedir(), "AppData", "Local");
    else
        throw new Error("Unsupported platform: " + process.platform);

    return path.join(cacheDirectory, "ms-playwright");
}

// === Core Functions ===
function browserDirectoryToMarkerFilePath(browserDirectory: string): string {
    return path.join(browserDirectory, INSTALLATION_COMPLETE_MARKER);
}

function readDescriptors(browsersJSON: BrowsersJSON, registryDirectory: string): BrowsersJSONDescriptor[] {
    return browsersJSON['browsers'].map((obj) => {
        const name = obj.name;
        const revisionOverride = (obj.revisionOverrides || {})[hostPlatform];
        const revision = revisionOverride || obj.revision;
        const browserDirectoryPrefix = revisionOverride
            ? `${name}_${hostPlatform}_special`
            : `${name}`;
        const descriptor: BrowsersJSONDescriptor = {
            name,
            revision,
            hasRevisionOverride: !!revisionOverride,
            browserVersion: revisionOverride ? undefined : obj.browserVersion,
            installByDefault: !!obj.installByDefault,
            dir: path.join(
                registryDirectory,
                browserDirectoryPrefix.replace(/-/g, '_') + '-' + revision
            ),
        };
        return descriptor;
    });
}

// === Network Functions ===
function httpRequest(
    params: {
        url: string;
        headers?: { [key: string]: string };
        socketTimeout?: number;
    },
    onResponse: (r: http.IncomingMessage) => void,
    onError: (error: Error) => void
): { cancel(error: Error | undefined): void } {
    const parsedURL = new URL(params.url);
    const requestOptions: https.RequestOptions = {
        method: 'GET',
        timeout: params.socketTimeout ?? NET_DEFAULT_TIMEOUT,
        protocol: parsedURL.protocol,
        hostname: parsedURL.hostname,
        port: parsedURL.port,
        path: parsedURL.pathname + parsedURL.search,
        headers: params.headers,
    };

    const requestModule = parsedURL.protocol === 'https:' ? https : http;
    const request = requestModule.request(requestOptions, (response) => {
        if (
            (response.statusCode === 301 ||
                response.statusCode === 302 ||
                response.statusCode === 303 ||
                response.statusCode === 307 ||
                response.statusCode === 308) &&
            response.headers.location
        ) {
            request.destroy();
            httpRequest(
                {
                    ...params,
                    url: new URL(
                        response.headers.location,
                        params.url
                    ).toString(),
                },
                onResponse,
                onError
            );
            return;
        }
        onResponse(response);
    });

    request.on('error', onError);
    request.end();

    return {
        cancel: (error: Error | undefined) => {
            request.destroy(error);
        },
    };
}

function downloadFile(options: DownloadOptions): Promise<void> {
    let downloadedBytes = 0;
    let totalBytes = 0;
    let lastProgressTime = Date.now();
    const promise = new ManualPromise<void>();

    httpRequest(
        {
            url: options.url,
            headers: {
                'User-Agent': options.userAgent,
            },
            socketTimeout: options.socketTimeout,
        },
        (response) => {
            if (response.statusCode !== 200) {
                let content = '';
                const handleError = () => {
                    const error = new Error(
                        `${getExceptionMessage(ExceptionCode.NETWORK)}: HTTP ${response.statusCode} for ${options.url} (code: ${ExceptionCode.NETWORK})\n` +
                        `   Response: ${content.length > 200 ? content.substring(0, 200) + '...' : content}`
                    );
                    response.resume();
                    promise.reject(error);
                };
                response
                    .on('data', (chunk) => (content += chunk))
                    .on('end', handleError)
                    .on('error', handleError);
                return;
            }
            totalBytes = parseInt(
                response.headers['content-length'] || '0',
                10
            );

            const file = fs.createWriteStream(options.zipPath);
            file.on('finish', () => {
                if (downloadedBytes !== totalBytes) {
                    promise.reject(
                        new Error(
                            `${getExceptionMessage(ExceptionCode.NETWORK)}: Size mismatch for ${options.url} (code: ${ExceptionCode.NETWORK})`
                        )
                    );
                } else {
                    promise.resolve();
                }
            });
            file.on('error', (error) => promise.reject(error));
            response.pipe(file);
            response.on('data', onData);
            response.on('error', (error: any) => {
                file.close();
                if (error?.code === 'ECONNRESET') {
                    promise.reject(
                        new Error(
                            `${getExceptionMessage(ExceptionCode.NETWORK)}: Connection reset for ${options.url} (code: ${ExceptionCode.NETWORK})`
                        )
                    );
                } else {
                    promise.reject(
                        new Error(
                            `${getExceptionMessage(ExceptionCode.NETWORK)}: ${error?.message ?? error} (code: ${ExceptionCode.NETWORK})`
                        )
                    );
                }
            });
        },
        (error: any) => promise.reject(error)
    );
    return promise;

    function onData(chunk: string) {
        downloadedBytes += chunk.length;
        // 每秒更新一次进度
        const now = Date.now();
        if (now - lastProgressTime > PROGRESS_UPDATE_INTERVAL || downloadedBytes === totalBytes) {
            lastProgressTime = now;
        }
    }
}

async function downloadBrowser(
    browserOptions: BrowserSpecificOptions,
    commonOptions: CommonInstallOptions
): Promise<boolean> {
    const { browserDirectory, executablePath, downloadURLs, downloadFileName } = browserOptions;
    const { socketTimeout: downloadSocketTimeout, playwrightVersion } = commonOptions;
    const browserName = path.basename(browserDirectory).split('-')[0];

    if (await existsAsync(browserDirectoryToMarkerFilePath(browserDirectory))) {
        return false; // Already downloaded
    }

    const zipPath = path.join(os.tmpdir(), downloadFileName);
    try {
        for (let attempt = 1; attempt <= DOWNLOAD_RETRY_COUNT; ++attempt) {
            const url = downloadURLs[(attempt - 1) % downloadURLs.length];
            try {
                if (attempt > 1) {
                    // Removed process.stdout.write(`\n🔄 重试下载 (第 ${attempt}/${DOWNLOAD_RETRY_COUNT} 次)...`);
                }

                await downloadFile({
                    url,
                    zipPath,
                    userAgent: getUserAgent(playwrightVersion),
                    socketTimeout: downloadSocketTimeout,
                });

                // Removed process.stdout.write(`\n📦 开始解压缩到: ${browserDirectory}`);
                // Extract the archive
                await extract.default(zipPath, { dir: browserDirectory });
                // Removed process.stdout.write(`\n✅ 解压缩完成`);

                // Set executable permissions
                if (executablePath) {
                    // Removed process.stdout.write(`🔧 设置执行权限: ${executablePath}`);
                    await fs.promises.chmod(executablePath, 0o755);
                }

                // Create marker file
                await fs.promises.writeFile(
                    browserDirectoryToMarkerFilePath(browserDirectory),
                    ''
                );
                // Removed process.stdout.write(`\n🎉 ${browserName} 安装完成！`);
                break;
            } catch (error) {
                if (await existsAsync(zipPath))
                    await fs.promises.unlink(zipPath);
                if (await existsAsync(browserDirectory))
                    await fs.promises.rmdir(browserDirectory, {
                        recursive: true,
                    });

                if (attempt >= DOWNLOAD_RETRY_COUNT) {
                    // Removed process.stdout.write(`\n❌ ${browserName} 安装失败，已达到最大重试次数`);
                    throw error;
                }
            }
        }
    } catch (e) {
        throw e;
    } finally {
        if (await existsAsync(zipPath)) await fs.promises.unlink(zipPath);
    }
    return true;
}

// === Registry Class ===
class SimpleRegistry {
    private _executables: ExecutableImpl[];
    private _registryDirectory: string;
    private _commonOptions: CommonInstallOptions;

    constructor(browsersJSON: BrowsersJSON, registryDirectory: string) {
        this._registryDirectory = registryDirectory;
        this._commonOptions = {
            socketTimeout: NET_DEFAULT_TIMEOUT,
            playwrightVersion: undefined
        };
        const descriptors = readDescriptors(browsersJSON, registryDirectory);
        const findExecutablePath = (
            dir: string,
            name: keyof typeof EXECUTABLE_PATHS
        ) => {
            let tokens: string[] | undefined = undefined;
            if (process.platform === 'linux')
                tokens = EXECUTABLE_PATHS[name]['linux'];
            else if (process.platform === 'darwin')
                tokens = EXECUTABLE_PATHS[name]['mac'];
            else if (process.platform === 'win32')
                tokens = EXECUTABLE_PATHS[name]['win'];
            return tokens ? path.join(dir, ...tokens) : undefined;
        };

        this._executables = [];

        // Add chromium
        const chromium = descriptors.find((d) => d.name === 'chromium')!;
        const chromiumExecutable = findExecutablePath(chromium.dir, 'chromium');
        this._executables.push({
            type: 'browser',
            name: 'chromium',
            browserName: 'chromium',
            directory: chromium.dir,
            executablePath: () => chromiumExecutable,
            executablePathOrDie: () => chromiumExecutable!,
            installType: chromium.installByDefault
                ? 'download-by-default'
                : 'download-on-demand',
            _validateHostRequirements: async () => { },
            downloadURLs: this._downloadURLs(chromium),
            browserVersion: chromium.browserVersion,
            _install: () =>
                this._downloadExecutable(chromium, chromiumExecutable),
            _dependencyGroup: 'chromium',
            _isHermeticInstallation: true,
        });

        // Add firefox
        const firefox = descriptors.find((d) => d.name === 'firefox')!;
        const firefoxExecutable = findExecutablePath(firefox.dir, 'firefox');
        this._executables.push({
            type: 'browser',
            name: 'firefox',
            browserName: 'firefox',
            directory: firefox.dir,
            executablePath: () => firefoxExecutable,
            executablePathOrDie: () => firefoxExecutable!,
            installType: firefox.installByDefault
                ? 'download-by-default'
                : 'download-on-demand',
            _validateHostRequirements: async () => { },
            downloadURLs: this._downloadURLs(firefox),
            browserVersion: firefox.browserVersion,
            _install: () =>
                this._downloadExecutable(firefox, firefoxExecutable),
            _dependencyGroup: 'firefox',
            _isHermeticInstallation: true,
        });

        // Add webkit
        const webkit = descriptors.find((d) => d.name === 'webkit')!;
        const webkitExecutable = findExecutablePath(webkit.dir, 'webkit');
        this._executables.push({
            type: 'browser',
            name: 'webkit',
            browserName: 'webkit',
            directory: webkit.dir,
            executablePath: () => webkitExecutable,
            executablePathOrDie: () => webkitExecutable!,
            installType: webkit.installByDefault
                ? 'download-by-default'
                : 'download-on-demand',
            _validateHostRequirements: async () => { },
            downloadURLs: this._downloadURLs(webkit),
            browserVersion: webkit.browserVersion,
            _install: () => this._downloadExecutable(webkit, webkitExecutable),
            _dependencyGroup: 'webkit',
            _isHermeticInstallation: true,
        });

        // Add ffmpeg
        const ffmpeg = descriptors.find((d) => d.name === 'ffmpeg')!;
        const ffmpegExecutable = findExecutablePath(ffmpeg.dir, 'ffmpeg');
        this._executables.push({
            type: 'tool',
            name: 'ffmpeg',
            browserName: undefined,
            directory: ffmpeg.dir,
            executablePath: () => ffmpegExecutable,
            executablePathOrDie: () => ffmpegExecutable!,
            installType: ffmpeg.installByDefault
                ? 'download-by-default'
                : 'download-on-demand',
            _validateHostRequirements: async () => { },
            downloadURLs: this._downloadURLs(ffmpeg),
            browserVersion: ffmpeg.browserVersion,
            _install: () => this._downloadExecutable(ffmpeg, ffmpegExecutable),
            _dependencyGroup: 'tools',
            _isHermeticInstallation: true,
        });
    }

    executables(): Executable[] {
        return this._executables.slice();
    }

    findExecutable(name: string): Executable | undefined {
        return this._executables.find((e) => e.name === name);
    }

    defaultExecutables(): Executable[] {
        return this._executables.filter(
            (e) => e.installType === 'download-by-default'
        );
    }

    private _dedupe(executables: Executable[]): ExecutableImpl[] {
        const unique = new Map<string, ExecutableImpl>();
        for (const executable of executables) {
            const key = executable.name;
            unique.set(key, executable as ExecutableImpl);
        }
        return Array.from(unique.values());
    }

    private _downloadURLs(descriptor: BrowsersJSONDescriptor): string[] {
        const paths = (DOWNLOAD_PATHS as any)[descriptor.name];
        const downloadPathTemplate: string | undefined =
            paths[hostPlatform] || paths['<unknown>'];
        if (!downloadPathTemplate) return [];
        const downloadPath = util.format(
            downloadPathTemplate,
            descriptor.revision
        );

        let downloadURLs = PLAYWRIGHT_CDN_MIRRORS.map(
            (mirror) => `${mirror}/${downloadPath}`
        );
        let downloadHostEnv: string | undefined;
        if (descriptor.name.startsWith('chromium'))
            downloadHostEnv = DOWNLOAD_ENV_VARS.CHROMIUM;
        else if (descriptor.name.startsWith('firefox'))
            downloadHostEnv = DOWNLOAD_ENV_VARS.FIREFOX;
        else if (descriptor.name.startsWith('webkit'))
            downloadHostEnv = DOWNLOAD_ENV_VARS.WEBKIT;

        return downloadURLs;
    }

    private _createBrowserOptions(
        descriptor: BrowsersJSONDescriptor,
        executablePath?: string
    ): BrowserSpecificOptions {
        const downloadURLs = this._downloadURLs(descriptor);
        if (!downloadURLs.length) {
            throw new Error(`${getExceptionMessage(ExceptionCode.NOT_FOUND)}: ${descriptor.name} is not available for ${hostPlatform}. (code: ${ExceptionCode.NOT_FOUND})`);
        }

        return {
            browserDirectory: descriptor.dir,
            executablePath,
            downloadURLs,
            downloadFileName: `playwright-download-${descriptor.name}-${hostPlatform}-${descriptor.revision}.zip`,
        };
    }

    private async _downloadExecutable(
        descriptor: BrowsersJSONDescriptor,
        executablePath?: string
    ) {
        // 🎯 参数分离设计优势：
        // 1. browserOptions：特定于当前浏览器的配置（目录、文件名等）
        // 2. commonOptions：通用配置（超时、版本等），可在整个 Registry 中复用
        const browserOptions = this._createBrowserOptions(descriptor, executablePath);

        await downloadBrowser(browserOptions, this._commonOptions).catch((e) => {
            throw new Error(
                `❌ Download failed for ${descriptor.name}:\n` +
                `   Platform: ${hostPlatform}\n` +
                `   Revision: ${descriptor.revision}\n` +
                `   Cause: ${e.message}\n` +
                `   💡 Try running with different network settings or check your internet connection.`
            );
        });
    }

    /**
     * 设置通用配置选项
     * @param options 要更新的配置项（支持部分更新）
     */
    setCommonOptions(options: Partial<CommonInstallOptions>): void {
        this._commonOptions = {
            ...this._commonOptions,
            ...options
        };
    }

    /**
     * 获取当前通用配置
     * @returns 当前的通用配置副本
     */
    getCommonOptions(): CommonInstallOptions {
        return { ...this._commonOptions };
    }

    /**
     * 批量设置通用配置的便利方法
     */
    configureInstallation(config: {
        timeout?: number;
        version?: string;
        maxRetries?: number;
    }): void {
        const updates: Partial<CommonInstallOptions> = {};

        if (config.timeout !== undefined) {
            Object.assign(updates, { socketTimeout: config.timeout });
        }

        if (config.version !== undefined) {
            Object.assign(updates, { playwrightVersion: config.version });
        }

        this.setCommonOptions(updates);

    }

    async install(
        executablesToInstall: Executable[],
        playwrightVersion?: string
    ) {
        if (playwrightVersion) {
            this.setCommonOptions({ playwrightVersion });
        }
        const executables = this._dedupe(executablesToInstall);
        // Removed process.stdout.write(`\n🎯 准备安装 ${executables.length} 个组件:`);
        executables.forEach((exe, index) => {
            // Removed process.stdout.write(`\n   ${index + 1}. ${exe.name}`);
        });

        await fs.promises.mkdir(this._registryDirectory, { recursive: true });
        // Removed process.stdout.write(`\n📁 浏览器安装目录: ${this._registryDirectory}`);

        const lockfilePath = path.join(this._registryDirectory, '__dirlock');
        const linksDir = path.join(this._registryDirectory, '.links');

        let releaseLock;
        try {
            releaseLock = await lockfile.lock(this._registryDirectory, {
                retries: {
                    retries: LOCK_RETRY_COUNT,
                    factor: DOWNLOAD_RETRY_FACTOR,
                },
                onCompromised: (err: Error) => {
                    throw new Error(`${err.message} Path: ${lockfilePath}`);
                },
                lockfilePath,
            });

            // Create a link first
            await fs.promises.mkdir(linksDir, { recursive: true });
            await fs.promises.writeFile(
                path.join(linksDir, calculateSha1(PACKAGE_PATH)),
                PACKAGE_PATH
            );

            // Install browsers for this package
            // Removed process.stdout.write(`\n🔒 获取安装锁，开始安装...`);

            for (let i = 0; i < executables.length; i++) {
                const executable = executables[i];
                // Removed process.stdout.write(`\n📋 [${i + 1}/${executables.length}] 处理 ${executable.name}...`);

                if (!executable._install) {
                    throw new Error(`${getExceptionMessage(ExceptionCode.NOT_FOUND)}: ${executable.name} cannot be installed automatically. (code: ${ExceptionCode.NOT_FOUND})`);
                }

                await executable._install();
            }

            // Removed process.stdout.write(`\n🎉 所有组件安装完成！`);
        } catch (e) {
            if ((e as any).code === 'ELOCKED') {
                const rmCommand = process.platform === 'win32' ? 'rm -R' : 'rm -rf';
                throw new Error(`${getExceptionMessage(ExceptionCode.CONFLICT)}: Installation in progress. Lock file: ${lockfilePath} (code: ${ExceptionCode.CONFLICT})`);
            } else {
                throw new Error(`${getExceptionMessage(ExceptionCode.INTERNAL)}: ${(e as Error).message} (code: ${ExceptionCode.INTERNAL})`);
            }
        } finally {
            if (releaseLock) await releaseLock();
        }
    }
}

// === Main Entry Point ===

/**
 * 创建浏览器安装器
 * @param browsersJSON 浏览器配置文件
 * @param installPath 安装路径（可选）
 * @param playwrightVersion Playwright版本（可选）
 * @returns 返回配置好的安装函数
 * 
 * @example
 * ```typescript
 * // 创建安装器，预设公共配置
 * const installer = createInstaller(browsersJSON, './browsers', '1.40.0');
 * 
 * // 简洁的安装调用
 * await installer(['chromium', 'firefox'], false);
 * await installer(['webkit'], true); // 强制重装
 * ```
 */
export function createInstaller(
    browsersJSON: BrowsersJSON,
    playwrightVersion?: string,
    installPath?: string,
) {
    // 闭包中保存公共配置
    const registryDirectory = getRegistryDirectory(installPath);

    return async function install(
        browsers: string[],
    ): Promise<void> {
        const startTime = Date.now();

        try {
            // Create registry with custom path
            const registry = new SimpleRegistry(browsersJSON, registryDirectory);

            let executablesToInstall: Executable[];
            if (browsers && browsers.length > 0) {
                // Removed process.stdout.write(`🎯 指定安装浏览器: ${browsers.join(', ')}`);
                executablesToInstall = [];
                for (const browserName of browsers) {
                    const executable = registry.findExecutable(browserName);
                    if (!executable) {
                        throw new Error(`${getExceptionMessage(ExceptionCode.NOT_FOUND)}: ${browserName} (code: ${ExceptionCode.NOT_FOUND})`);
                    }
                    executablesToInstall.push(executable);
                }
            } else {
                // Removed process.stdout.write(`📦 安装默认浏览器`);
                executablesToInstall = registry.defaultExecutables();
            }

            await registry.install(executablesToInstall, playwrightVersion);

            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(2);
            const logger = new LoggerManager('PWInstaller');
            logger.getUIPreviewLogger().info(`安装耗时: ${duration}s`);
        } catch (error) {
            throw error;
        }
    };
}
