import * as netUtils from './netUtils';

/**
 * 检查端口是否可达
 */
// moved to netUtils.ts

/**
 * 尝试 fallback 到 127.0.0.1 或 ::1，如果是 localhost 的情况
 */
export async function resolveReachableLocalUrl(targetUrl: string): Promise<string | null> {
    let url: URL;
    try {
        url = new URL(targetUrl);
    } catch {
        // 非法 URL，直接返回 null，避免抛异常
        return null;
    }
    // 如果未指定端口，根据协议给出常见默认值；大多数本地开发都会显式端口
    const defaultPort = url.protocol === 'https:' ? 443 : 80;
    const port = parseInt(url.port || String(defaultPort), 10);

    // 仅在典型本地不可路由主机名时进行回退处理
    const isLocalHost = url.hostname === 'localhost';
    const isUnspecifiedIPv4 = url.hostname === '0.0.0.0';
    // Node 的 URL.hostname 在 IPv6 未指定地址可能返回 "[::]"
    const isUnspecifiedIPv6 = url.hostname === '::' || url.hostname === '[::]';
    const needsFallback = isLocalHost || isUnspecifiedIPv4 || isUnspecifiedIPv6;

    if (!needsFallback) {
        return targetUrl;
    }

    // 优先 IPv4 回环，再尝试 IPv6 回环
    const fallbackHosts = ['127.0.0.1', '::1'];

    for (const host of fallbackHosts) {
        const reachable = await netUtils.isPortReachable(host, port);
        if (reachable) {
            // 使用重建 base 的方式，确保 IPv6 正确带 []
            const baseHost = host.includes(':') ? `[${host}]` : host;
            const base = `${url.protocol}//${baseHost}${url.port ? `:${url.port}` : ''}`;
            const rewritten = new URL(base);
            rewritten.pathname = url.pathname;
            rewritten.search = url.search;
            rewritten.hash = url.hash;
            return rewritten.toString();
        }
    }

    return null;
}
