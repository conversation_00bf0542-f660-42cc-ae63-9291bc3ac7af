import { SqliteDb } from '../index.js';

export interface Task {
  /** 自增主键 */
  id?: number;
  /** 任务唯一标识 */
  task_id: string;
  /** 文件路径（包含filename） */
  filepath: string;
  /** 代码仓库地址 */
  git_url: string;
  /** 任务创建时间戳 */
  created_at: number;
  /** 任务更新时间戳 */
  updated_at: number;
  /** 任务状态: pending-待处理, indexing-处理中 */
  status: 'pending' | 'indexing';
  /** 文件操作类型: modify-修改, delete-删除, create-创建 */
  file_action: 'modify' | 'delete' | 'create';
  /** 代码仓库路径(相对系统根目录的绝对路径) */
  dir_path: string;
  /** 重试次数 */
  retry_count: number;
}

export class TaskTable {
  static async create(data: Omit<Task, 'id'>) {
    const db = await SqliteDb.get();
    const result = await db.run(
      `INSERT INTO tasks (
        task_id, filepath, git_url, created_at, updated_at,
        status, file_action, dir_path, retry_count
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)`,
      [
        data.task_id,
        data.filepath,
        data.git_url,
        data.created_at,
        data.updated_at,
        data.status,
        data.file_action,
        data.dir_path,
        data.retry_count || 0
      ]
    );
    return result.lastID;
  }

  static async update(id: number, data: Partial<Omit<Task, 'id'>>) {
    const db = await SqliteDb.get();
    const updates = Object.entries(data)
      .map(([key]) => `${key} = ?`)
      .join(', ');
    const values = Object.values(data);

    await db.run(`UPDATE tasks SET ${updates} WHERE id = ?`, [...values, id]);
  }

  static async updateStatusByTaskId(dirPath: string, taskId: string, status: Task['status'], retryCount?: number) {
    const db = await SqliteDb.get();
    await db.run('UPDATE tasks SET status = ?, updated_at = ?, retry_count = ? WHERE dir_path = ? AND task_id = ?', [
      status,
      Date.now(),
      retryCount || 0,
      dirPath,
      taskId
    ]);
  }

  static async batchUpdateStatusByTaskIds(
    dirPath: string,
    taskIds: string[],
    status: Task['status'],
    retryCount?: number
  ) {
    if (!taskIds || taskIds.length === 0) {
      return;
    }

    const db = await SqliteDb.get();

    // SQLite has a limit of 999 variables per statement
    const BATCH_SIZE = 999;
    const currentTime = Date.now();
    // Process in batches
    for (let i = 0; i < taskIds.length; i += BATCH_SIZE) {
      const batch = taskIds.slice(i, i + BATCH_SIZE);
      const placeholders = batch.map(() => '?').join(',');

      await db.run(
        `UPDATE tasks SET status = ?, updated_at = ?, retry_count = ? 
         WHERE dir_path = ? AND task_id IN (${placeholders})`,
        [status, currentTime, retryCount || 0, dirPath, ...batch]
      );
    }
  }

  static async deleteByTaskId(dirPath: string, taskId: string) {
    const db = await SqliteDb.get();
    await db.run('DELETE FROM tasks WHERE dir_path = ? AND task_id = ?', [dirPath, taskId]);
  }

  static async deleteByDirPath(dirPath: string) {
    const db = await SqliteDb.get();
    await db.run('DELETE FROM tasks WHERE dir_path = ?', [dirPath]);
  }
  static async batchDeleteByTaskIds(dirPath: string, taskIds: string[]) {
    if (!taskIds || taskIds.length === 0) {
      return;
    }

    const db = await SqliteDb.get();

    // SQLite has a limit of 999 variables per statement
    const BATCH_SIZE = 999;

    // Process in batches
    for (let i = 0; i < taskIds.length; i += BATCH_SIZE) {
      const batch = taskIds.slice(i, i + BATCH_SIZE);
      const placeholders = batch.map(() => '?').join(',');

      await db.run(`DELETE FROM tasks WHERE dir_path = ? AND task_id IN (${placeholders})`, [dirPath, ...batch]);
    }
  }

  static async findByStatus(dirPath: string, status: string) {
    const db = await SqliteDb.get();
    return await db.all<Task[]>('SELECT * FROM tasks WHERE dir_path = ? AND status = ?', [dirPath, status]);
  }

  static async findByFilePath(filepath: string) {
    const db = await SqliteDb.get();
    return await db.get<Task>('SELECT * FROM tasks WHERE filepath = ?', [filepath]);
  }

  static async countTasks(dirPath: string, statuses: string[]) {
    const db = await SqliteDb.get();
    const placeholders = statuses.map(() => '?').join(',');
    const result = await db.get(
      `SELECT COUNT(*) as count FROM tasks WHERE dir_path = ? AND status IN (${placeholders})`,
      [dirPath, ...statuses]
    );
    return result?.count || 0;
  }

  static async findTasksByPage(dirPath: string, statuses: string[], limit: number, offset: number) {
    const db = await SqliteDb.get();
    const placeholders = statuses.map(() => '?').join(',');
    return await db.all<Task[]>(
      `SELECT * FROM tasks WHERE dir_path = ? AND status IN (${placeholders}) LIMIT ? OFFSET ?`,
      [dirPath, ...statuses, limit, offset * limit]
    );
  }

  static async findTasksAfterIdWithLimit(
    dirPath: string,
    statuses: string[],
    lastId: number,
    limit: number
  ): Promise<Task[]> {
    const db = await SqliteDb.get();
    const statusPlaceholders = statuses.map(() => '?').join(',');
    const query = `
      SELECT * FROM tasks 
      WHERE dir_path = ? 
      AND status IN (${statusPlaceholders})
      AND id > ?
      ORDER BY id ASC
      LIMIT ?
    `;

    const params = [dirPath, ...statuses, lastId, limit];
    return await db.all(query, params);
  }

  static async batchCreate(tasks: Omit<Task, 'id'>[]): Promise<number> {
    if (!tasks || tasks.length === 0) {
      return 0;
    }

    const db = await SqliteDb.get();

    // SQLite has a limit of 999 variables per statement
    // Each task has 8 fields, so we can insert ~124 tasks per batch
    const BATCH_SIZE = 100;
    let lastId = 0;
    // Process in batches
    for (let i = 0; i < tasks.length; i += BATCH_SIZE) {
      const batch = tasks.slice(i, i + BATCH_SIZE);
      const placeholders = batch.map(() => '(?, ?, ?, ?, ?, ?, ?, ?, ?)').join(',');
      const values = batch.flatMap((task) => [
        task.task_id,
        task.filepath,
        task.git_url,
        task.created_at,
        task.updated_at,
        task.status,
        task.file_action,
        task.dir_path,
        task.retry_count || 0
      ]);

      const result = await db.run(
        `INSERT INTO tasks (
          task_id, filepath, git_url, created_at, updated_at,
          status, file_action, dir_path, retry_count
        ) VALUES ${placeholders}`,
        values
      );

      if (result.lastID !== undefined) {
        lastId = result.lastID;
      }
    }

    return lastId;
  }
}
