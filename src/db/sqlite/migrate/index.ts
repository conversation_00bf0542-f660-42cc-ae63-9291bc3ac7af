import { getOriginalIndexSqlitePath, getTmpSqlitePath, getIndexSqlitePath, getSqlitePath } from '@/util/paths';
import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';
import * as os from 'os';
import ConcurrencyLock from '@/index-manager/ConcurrencyLock';
import { GlobalConfig } from '@/util/global';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE } from '@/util/const';
import { loadSqlite3 } from '@/util/mod';
const logger = new Logger('MIGRATE_DB');

// 用于记录迁移进度的文件名
const MIGRATION_STATE_FILE = '.migration_state';

enum MigrationStatus {
  INITIAL = 'initial',
  TMP_COPIED = 'tmp_copied',
  VERIFIED = 'verified',
  // DB_RENAMED = 'db_renamed' 不需要再加一步 rename 的操作
  DATA_CLEARED = 'data_cleared',
  COMPLETED = 'completed'
}
/**
 * 迁移状态
 */
interface MigrationState {
  status: MigrationStatus;
  lastUpdateTime: number;
}
const getStateFilePath = (dirPath: string): string => {
  return path.join(getSqlitePath(dirPath), MIGRATION_STATE_FILE);
};
/**
 * 检查是否存在未完成的迁移
 */
const checkUnfinishedMigration = (dirPath: string): MigrationState => {
  logger.debug('start checking migration state');
  const stateFilePath = getStateFilePath(dirPath);
  const dbPath = getIndexSqlitePath();
  if (fs.existsSync(stateFilePath)) {
    try {
      const stateData = fs.readFileSync(stateFilePath, 'utf8');
      const state = JSON.parse(stateData) as MigrationState;

      return state;
    } catch (error) {
      console.error('Error reading migration state file:', error);
    }
  }
  if (fs.existsSync(dbPath)) {
    // 如果不存在state 文件，并且数据库存在，则证明是已经迁移完成的状态。
    return {
      status: MigrationStatus.COMPLETED,
      lastUpdateTime: Date.now()
    };
  }
  const initialState: MigrationState = {
    status: MigrationStatus.INITIAL,
    lastUpdateTime: Date.now()
  };
  saveMigrationState(dirPath, initialState);
  return initialState;
};

/**
 * 保存迁移状态
 */
const saveMigrationState = (dirPath: string, state: MigrationState): void => {
  logger.debug('start checking migration state : ' + JSON.stringify(state));

  const stateDir = getSqlitePath(dirPath);
  const stateFilePath = path.join(stateDir, MIGRATION_STATE_FILE);

  // 确保目录存在
  if (!fs.existsSync(stateDir)) {
    fs.mkdirSync(stateDir, { recursive: true });
  }

  // 更新时间戳
  state.lastUpdateTime = Date.now();

  // 保存状态
  try {
    fs.writeFileSync(stateFilePath, JSON.stringify(state, null, 2), 'utf8');
  } catch (error) {
    console.error('Error saving migration state:', error);
  }
};

/**
 * 清除迁移状态
 */
const clearMigrationState = (dirPath: string): void => {
  logger.debug('start clearing migration state');
  const stateFilePath = getStateFilePath(dirPath);

  if (fs.existsSync(stateFilePath)) {
    try {
      fs.unlinkSync(stateFilePath);
    } catch (error) {
      console.error('Error clearing migration state:', error);
    }
  }
};

/**
 * Migrate database using direct file copy via Node.js
 */
const migrateByFileCopy = (originalPath: string, targetPath: string): boolean => {
  try {
    logger.info(`Migrating by direct file copy from ${originalPath} to ${targetPath}`);
    fs.copyFileSync(originalPath, targetPath);
    return true;
  } catch (error) {
    logger.error('Error during file copy:', error);
    return false;
  }
};

/**
 * Migrate database using direct file copy via system command
 */
const migrateByCpCommand = (originalPath: string, targetPath: string): boolean => {
  try {
    const isWindows = os.platform() === 'win32';

    if (isWindows) {
      logger.info(`Migrating using Windows copy command from ${originalPath} to ${targetPath}`);
      execSync(`copy "${originalPath}" "${targetPath}"`, { stdio: 'inherit' });
    } else {
      logger.info(`Migrating using cp command from ${originalPath} to ${targetPath}`);
      execSync(`cp "${originalPath}" "${targetPath}"`, { stdio: 'inherit' });
    }
    return true;
  } catch (error) {
    console.error('Error using system copy command:', error);
    return false;
  }
};

/**
 * Migrate database from original location to a temporary location in the new directory
 * With resilience against process crashes and concurrent write operations
 */
export const migrate = async (dirPath: string): Promise<void> => {
  const startTime = Date.now();
  const originalName = getOriginalIndexSqlitePath();
  const newPath = getIndexSqlitePath();
  try {
    logger.info('migrate DB start');
    // 1：判断之前的路径是否存在，如果不存在，则证明是全新的用户，直接过
    if (!fs.existsSync(originalName)) {
      logger.info('original path not exist');
      clearMigrationState(dirPath);
      GlobalConfig.getConfig().setIsDBImgrated(true);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'migrate-new-user',
        millis: Date.now() - startTime,
        extra6: 'success'
      });
      return;
    }
    // 2: 判断该仓库之前是否索引过，如果没有索引过，则是全新的项目，直接过
    if (!fs.existsSync(newPath)) {
      const ifIndexed = await checkIfIndexed(dirPath);
      if (!ifIndexed) {
        logger.info('previous index not exist');
        clearMigrationState(dirPath);
        GlobalConfig.getConfig().setIsDBImgrated(true);
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'migrate-new-project',
          millis: Date.now() - startTime,
          extra6: 'success'
        });
        return;
      }
    }
    // 检查是否有未完成的迁移
    const unfinishedState = checkUnfinishedMigration(dirPath);
    let currentStatus = unfinishedState.status;
    if (currentStatus !== MigrationStatus.COMPLETED) {
      // 索引之前首先上锁，方式出现多个 ide 同时迁移的情况
      await ConcurrencyLock.acquireLock(dirPath);
      logger.info('get thread lock successfully');
    }
    let retryCount = 0;
    const maxRetryCount = 3;
    while (currentStatus !== MigrationStatus.COMPLETED && retryCount < maxRetryCount) {
      const stepStartTime = Date.now();
      try {
        switch (currentStatus) {
          case MigrationStatus.INITIAL:
            // 执行 copy 动作
            runMigrate(originalName, newPath);
            unfinishedState.status = MigrationStatus.TMP_COPIED;
            saveMigrationState(dirPath, unfinishedState);
            currentStatus = MigrationStatus.TMP_COPIED;
            logger.perf({
              namespace: AGENT_NAMESPACE,
              subtag: 'migrate-copy-db',
              millis: Date.now() - stepStartTime,
              extra6: 'success'
            });
            break;
          case MigrationStatus.TMP_COPIED:
            logger.info('Previous migration copied, verifyDatabaseConnection...');
            // verify db connection
            await verifyDatabaseConnection(newPath);
            // 更新状态
            unfinishedState.status = MigrationStatus.VERIFIED;
            saveMigrationState(dirPath, unfinishedState);
            currentStatus = MigrationStatus.VERIFIED;
            logger.perf({
              namespace: AGENT_NAMESPACE,
              subtag: 'migrate-verify-db',
              millis: Date.now() - stepStartTime,
              extra6: 'success'
            });
            break;
          case MigrationStatus.VERIFIED:
            logger.info('Previous migration VERIFIED completed, clearDbData...');
            // clear db data
            await clearDbData(dirPath, newPath);
            unfinishedState.status = MigrationStatus.DATA_CLEARED;
            saveMigrationState(dirPath, unfinishedState);
            currentStatus = MigrationStatus.DATA_CLEARED;
            logger.perf({
              namespace: AGENT_NAMESPACE,
              subtag: 'migrate-clear-db-data',
              millis: Date.now() - stepStartTime,
              extra6: 'success'
            });
            break;
          case MigrationStatus.DATA_CLEARED:
            logger.info('Data cleanup completed, verifyDatabaseConnection ...');
            // 删除数据之后，再次确认连接状态
            await verifyDatabaseConnection(newPath);
            unfinishedState.status = MigrationStatus.COMPLETED;
            saveMigrationState(dirPath, unfinishedState);
            currentStatus = MigrationStatus.COMPLETED;
            logger.perf({
              namespace: AGENT_NAMESPACE,
              subtag: 'migrate-completed',
              millis: Date.now() - stepStartTime,
              extra6: 'success'
            });
            break;
          default:
            unfinishedState.status = MigrationStatus.INITIAL;
            saveMigrationState(dirPath, unfinishedState);
            currentStatus = MigrationStatus.INITIAL;
            break;
        }
      } catch (e) {
        logger.error('Migration switch failed:', e);
        // 如果迁移的过程出现异常，则直接从头开始
        retryCount++;
        // 如果DB 无法连接，则重新 copy
        unfinishedState.status = MigrationStatus.INITIAL;
        saveMigrationState(dirPath, unfinishedState);
        currentStatus = MigrationStatus.INITIAL;
        logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: 'migrate-failed',
          millis: Date.now() - stepStartTime,
          extra6: JSON.stringify({
            error: e,
            retryCount,
            currentStatus,
            unfinishedState
          })
        });
      }
    }
    if (currentStatus === MigrationStatus.COMPLETED) {
      clearMigrationState(dirPath);
      logger.info('migrate DB successfully');
      GlobalConfig.getConfig().setIsDBImgrated(true);
      logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'migrate-success',
        millis: Date.now() - startTime,
        extra6: 'success'
      });
    }
  } catch (error) {
    logger.error('Migration while loop failed : ', error);
    GlobalConfig.getConfig().setIsDBImgrated(false);
    logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'migrate-failed',
      millis: Date.now() - startTime,
      extra6: JSON.stringify({
        error: error
      })
    });
  }
};

const runMigrate = (origin: string, dist: string) => {
  // 执行迁移动作之前先把之前的旧文件删掉
  logger.info(`migrate start, delete old file: ${dist}`);
  if (fs.existsSync(dist)) {
    try {
      fs.unlinkSync(dist);
    } catch (error) {
      throw `delete old file failed: ${error}`;
    }
  }
  logger.debug(`migrate start, copy file from ${origin} to ${dist}`);
  // 首先尝试先用 cp 命令 copy，如果不行，则用 fs 自带的 copysync
  let migrationSuccess = migrateByCpCommand(origin, dist);

  if (!migrationSuccess) {
    logger.debug('Migration by cp command failed, use file copy instead');
    migrationSuccess = migrateByFileCopy(origin, dist);
  }
  if (!migrationSuccess) {
    logger.error('Migration failed');
    throw 'Migration failed';
  }
};

const checkIfIndexed = async (dirPath: string): Promise<boolean> => {
  logger.debug('Check if indexed' + dirPath);
  const originalPath = getOriginalIndexSqlitePath();

  // 如果原始数据库文件不存在，则直接返回 false
  if (!fs.existsSync(originalPath)) {
    logger.info(`Original database not found at ${originalPath}`);
    return false;
  }
  const sqlite3 = await loadSqlite3();

  return new Promise<boolean>((resolve) => {
    try {
      const db = new sqlite3.Database(originalPath, sqlite3.OPEN_READONLY, (err) => {
        if (err) {
          logger.error(`Error connecting to database: ${err.message}`);
          resolve(false);
          return;
        }

        // 查询 repo_status 表中是否存在匹配的 dirPath
        const query = `SELECT 1 FROM repo_status WHERE dir_path = ?`;

        db.get(query, [dirPath], (err, row) => {
          db.close();

          if (err) {
            logger.error(`Error querying repo_status table: ${err.message}`);
            resolve(false);
            return;
          }

          // 如果找到记录，则表示该目录已被索引
          const isIndexed = !!row;
          logger.info(`Directory ${dirPath} indexed status: ${isIndexed ? 'indexed' : 'not indexed'}`);
          resolve(isIndexed);
        });
      });
    } catch (error) {
      logger.error(`Exception in checkIfIndexed: ${error}`);
      resolve(false);
    }
  });
};

const clearDbData = async (dirPath: string, distPath: string) => {
  try {
    const sqlite3 = await loadSqlite3();
    const db = new sqlite3.Database(distPath);

    // 获取所有表名
    const tables: any[] = await new Promise((resolve) => {
      db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, rows) => {
        if (err) {
          logger.error('Error retrieving table names:', err);
          resolve([]);
        } else {
          resolve(rows);
        }
      });
    });

    // 过滤出需要删除的表（以_code_info 或 _node_graph结尾，但不是当前目录的表）
    const tablesToDelete = tables
      .map((t) => t.name)
      .filter((name) => name.endsWith('_code_info') || name.endsWith('_node_graph'));

    if (tablesToDelete.length > 0) {
      logger.info(`Found ${tablesToDelete.length} obsolete tables to delete: ${tablesToDelete.join(', ')}`);

      // 依次删除表
      for (const tableName of tablesToDelete) {
        try {
          logger.info(`Dropping table: ${tableName}`);
          await new Promise<void>((resolve) => {
            db.exec(`DROP TABLE IF EXISTS ${tableName}`, (err) => {
              if (err) {
                logger.error(`Error dropping table ${tableName}:`, err);
              }
              resolve();
            });
          });
        } catch (error) {
          logger.error(`Error dropping table ${tableName}:`, error);
        }
      }

      logger.info('Database cleanup completed successfully');
    } else {
      logger.info('No obsolete tables found for cleanup');
    }

    // 清除其他表的数据
    // 清除 file_status 表中与当前目录不相关的数据
    await new Promise<void>((resolve) => {
      db.run(`DELETE FROM file_status WHERE dir_path != ?`, [dirPath], (err) => {
        if (err) {
          logger.error(`Error cleaning file_status table: ${err.message}`);
        } else {
          logger.info(`Cleaned file_status records not matching dir_path: ${dirPath}`);
        }
        resolve();
      });
    });

    // 清除 tasks 表中与当前目录不相关的数据
    await new Promise<void>((resolve) => {
      db.run(`DELETE FROM tasks WHERE dir_path != ?`, [dirPath], (err) => {
        if (err) {
          logger.error(`Error cleaning tasks table: ${err.message}`);
        } else {
          logger.info(`Cleaned tasks records not matching dir_path: ${dirPath}`);
        }
        resolve();
      });
    });

    // 关闭数据库连接
    db.close();
  } catch (error) {
    logger.error('Error during database cleanup:', error);
    throw new Error(`Database cleanup failed: ${error}`);
  }
};

/**
 * Verifies the SQLite database connection works properly
 * Returns true if connection is successful, false otherwise
 */
const verifyDatabaseConnection = (dbPath: string): Promise<boolean> => {
  return new Promise(async (resolve, reject) => {
    if (!fs.existsSync(dbPath)) {
      console.error(`Database file not found at ${dbPath}`);
      reject('verifyDatabaseConnection failed: Database file not found');
      return;
    }
    const sqlite3 = await loadSqlite3();
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        reject(`Error connecting to database: ${err.message}`);
        return;
      }

      // Test a simple query to ensure the database is working
      db.get('SELECT sqlite_version() as version', (err, row: { version?: string }) => {
        if (err) {
          db.close();
          reject(`Database verification failed: ${err.message}`);
          return;
        }

        logger.info(`Database connection verified successfully (SQLite version: ${row?.version})`);
        // 查询 repo_status 表中的数据数量
        db.get('SELECT COUNT(*) as count FROM repo_status', (err, row: { count?: number }) => {
          if (err) {
            logger.warn(`Unable to query repo_status table: ${err.message}, this might be a new database`);
          } else {
            logger.info(`Found ${row?.count || 0} records in repo_status table`);
          }

          // 创建临时表来测试写入权限
          const tempTableName = `temp_write_test_${Date.now()}`;

          db.serialize(() => {
            db.run(`CREATE TABLE IF NOT EXISTS ${tempTableName} (id INTEGER PRIMARY KEY, test_data TEXT)`, (err) => {
              if (err) {
                db.close();
                reject(`Database write permission test failed (create table): ${err.message}`);
                return;
              }

              // 写入测试数据
              db.run(`INSERT INTO ${tempTableName} (test_data) VALUES (?)`, [`test_${Date.now()}`], (err) => {
                if (err) {
                  db.run(`DROP TABLE IF EXISTS ${tempTableName}`);
                  db.close();
                  reject(`Database write permission test failed (insert data): ${err.message}`);
                  return;
                }

                // 删除测试数据
                db.run(`DROP TABLE IF EXISTS ${tempTableName}`, (err) => {
                  db.close();

                  if (err) {
                    reject(`Database write permission test failed (drop table): ${err.message}`);
                    return;
                  }

                  logger.info(`Database write permission test passed successfully`);
                  resolve(true);
                });
              });
            });
          });
        });
      });
    });
  });
};
