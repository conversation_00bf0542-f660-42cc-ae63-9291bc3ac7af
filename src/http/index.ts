import { Logger } from '@/util/log';
import {
  CloudCodeSearchBody,
  CloudCodeSearchResponse,
  CodeRerankBody,
  CodeRerankResponse,
  HttpClientResponse,
  QueryEmbeddingBody,
  QueryEmbeddingResponse,
  IndexingSearchBody,
  IndexingSearchResponse,
  IndexUserFilesResponse,
  VercelOAuthAccessTokenBody,
  VercelOAuthAccessTokenResponse
} from '@/http/types';
import { AGENT_NAMESPACE } from '@/util/const';
import { fetchEventSource, FetchEventSourceInit } from '@fortaine/fetch-event-source';
import { GlobalConfig } from '@/util/global';
import * as fs from 'fs';
import path from 'path';
import { convertToGitSshFormat } from '@/util/git-utils';

function getAcceptLanguageHeader(currentLanguage: string): string {
  // 语言映射表，将语言代码映射到标准的 Accept-Language 格式
  const languageMap: Record<string, string> = {
    'zh-CN': 'zh-CN,zh;q=0.9,en;q=0.8',
    'zh-TW': 'zh-TW,zh;q=0.9,en;q=0.8',
    zh: 'zh-CN,zh;q=0.9,en;q=0.8',
    'en-US': 'en-US,en;q=0.9',
    en: 'en-US,en;q=0.9',
    ja: 'ja,en;q=0.9',
    ko: 'ko,en;q=0.9',
    fr: 'fr,en;q=0.9',
    de: 'de,en;q=0.9',
    es: 'es,en;q=0.9',
    pt: 'pt,en;q=0.9',
    ru: 'ru,en;q=0.9',
    it: 'it,en;q=0.9'
  };

  // 如果找到映射，返回对应的 Accept-Language 值
  if (languageMap[currentLanguage]) {
    return languageMap[currentLanguage];
  }

  // 如果没有找到映射，尝试从语言代码中提取主要语言
  const primaryLang = currentLanguage.split('-')[0];
  if (languageMap[primaryLang]) {
    return languageMap[primaryLang];
  }

  // 默认返回英文
  return 'en-US,en;q=0.9';
}

interface RetryConfig {
  maxRetries?: number; // 最大重试次数，默认 3
  retryDelay?: number; // 重试延迟时间（毫秒），默认 1000
  retryCondition?: (error: any, response?: Response) => boolean; // 重试条件判断函数
  onRetry?: (attempt: number, error: any) => void; // 重试回调函数
}

interface HttpClientOptions {
  config?: RequestInit;
  requestInterceptors?: ((param: any) => any)[];
  responseInterceptors?: ((param: any) => any)[];
  retryConfig?: RetryConfig;
}

type onFetchLog = (params: { url: string; headers: Record<string, string> }) => void;

class HttpClient {
  private _config: RequestInit = {};
  private requestInterceptors: ((param: any) => any)[] = [];
  private responseInterceptors: ((param: any) => any)[] = [];
  private retryConfig: RetryConfig;
  private _logger: Logger | null = null;

  // 延迟初始化 logger，避免循环依赖
  get logger(): Logger {
    if (!this._logger) {
      this._logger = new Logger('http-client');
    }
    return this._logger;
  }

  get _baseUrl() {
    return GlobalConfig.getConfig().getKwaiPilotDomain();
  }

  constructor(options?: HttpClientOptions) {
    this._config = options?.config || {};
    this.requestInterceptors = options?.requestInterceptors || [];
    this.responseInterceptors = options?.responseInterceptors || [];

    // 设置默认重试配置
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryCondition: (error: any, response?: Response) => {
        // 默认重试条件：网络错误或 5xx 服务器错误
        if (error && error.name === 'TypeError') return true; // 网络错误
        if (response && response.status >= 500) return true; // 服务器错误
        return false;
      },
      onRetry: (attempt: number, error: any) => {
        this.logger.warn(`请求重试中，第 ${attempt} 次尝试:`, error);
      },
      ...options?.retryConfig
    };
  }

  async request<R>(url: string, requestInit?: RequestInit) {
    const s = Date.now();
    const response = await this.rawRequest(url, requestInit);
    let responseData = (await response.json()) as R;
    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    this.logger.debug(`api response after interceptor url: ${url}, cost:${Date.now() - s}`);
    return responseData;
  }

  async rawRequest(url: string, requestInit?: RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);

    // 动态获取 JWT token 并注入到请求头 (仅在 External 环境下)
    if (GlobalConfig.getConfig().getVersionType() === 'External') {
      try {
        const token = GlobalConfig.getConfig().getJwtToken();
        if (token) {
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${token}`
          };
          this.logger.debug('JWT token injected into request headers', 'http-client');
        } else {
          this.logger.warn('Token provider returned empty token', 'http-client');
        }
      } catch (error: any) {
        this.logger.error('Failed to get JWT token from provider', 'http-client', { err: error });
      }
    }

    // 添加多语言支持 - 从 GlobalConfig 获取当前语言
    const currentLanguage = GlobalConfig.getConfig().getCurrentLanguage();
    const acceptLanguage = getAcceptLanguageHeader(currentLanguage);

    // 只有在没有明确设置 Content-Type 且 body 不是 FormData 时才设置默认的 application/json
    if ((config.headers || !config.headers?.['Content-Type']) && !(config.body instanceof FormData)) {
      config.headers = {
        'Content-Type': 'application/json',
        'Accept-Language': acceptLanguage,
        ...config.headers
      };
    } else {
      config.headers = {
        'Accept-Language': acceptLanguage,
        ...config.headers
      };
    }

    config.method = config.method || 'GET';

    const u = this.buildUrl(url, this._baseUrl, '/api/proxy');

    this.logger.debug(`api request url: ${u.toString()}, ts:${Date.now()}`);
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });
    this.logger.debug('api request after interceptor');

    // 实现重试逻辑
    let lastError: any;
    let response: Response | undefined;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries!; attempt++) {
      try {
        response = await fetch(u, config);

        // 检查响应是否成功
        if (response.ok) {
          this.logger.info(`api request success: ${config.method} ${u.pathname} ${response.status}`);
          return response;
        }

        // 检查是否需要重试
        if (attempt < this.retryConfig.maxRetries! && this.retryConfig.retryCondition!(null, response)) {
          this.retryConfig.onRetry!(attempt + 1, new Error(`HTTP ${response.status}: ${response.statusText}`));
          await this.delay(this.retryConfig.retryDelay! * Math.pow(2, attempt)); // 指数退避
          continue;
        }

        // 不满足重试条件或已达到最大重试次数
        this.logger.error('api request error');
        throw new Error('An error occurred');
      } catch (error: any) {
        lastError = error;

        if (error.type === 'aborted') {
          throw 'user abort';
        }

        // 检查是否需要重试
        if (attempt < this.retryConfig.maxRetries! && this.retryConfig.retryCondition!(error)) {
          this.retryConfig.onRetry!(attempt + 1, error);
          await this.delay(this.retryConfig.retryDelay! * Math.pow(2, attempt)); // 指数退避
          continue;
        }

        // 不满足重试条件或已达到最大重试次数
        this.logger.error('api request error');
        throw new Error(error.toString());
      }
    }

    // 如果执行到这里，说明所有重试都失败了
    throw lastError || new Error('Request failed after all retries');
  }
  async get<T, R = HttpClientResponse<T>>(url: string, params: Record<string, string> = {}, config: RequestInit = {}) {
    const p = this.formatUrl(url, params);
    config.method = 'GET';
    return this.request<R>(p, config);
  }
  async delete<T, R = HttpClientResponse<T>, D extends BodyInit = any>(
    url: string,
    body?: D,
    params: Record<string, string> = {},
    config: RequestInit = {}
  ) {
    const p = this.formatUrl(url, params);
    config.method = 'DELETE';
    if (body) {
      config.body = body;
    }
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends BodyInit = any>(url: string, body?: D, config: RequestInit = {}) {
    config.method = 'POST';
    config.body = body;
    return this.request<R>(url, config);
  }
  async postForm<T, R = HttpClientResponse<T>>(url: string, formData: FormData, config: RequestInit = {}) {
    config.method = 'POST';
    config.body = formData;
    // 不设置 Content-Type，让浏览器自动设置 multipart/form-data 边界
    const headers = { ...config.headers };
    delete (headers as any)['Content-Type'];
    config.headers = headers;
    return this.request<R>(url, config);
  }
  private mergeConfig(originConfig: RequestInit = {}, config: RequestInit = {}) {
    return { ...originConfig, ...config };
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, 'http://example.com');
    try {
      new URL(url);
    } catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;
    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }
    return urlObj.toString();
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 构建完整的 URL，支持多种 baseUrl 格式和特殊环境处理
   * @param url 相对路径
   * @param host 域名部分
   * @param defaultPath 凭接部分
   * @returns 完整的 URL 对象
   */
  private buildUrl(url: string, host: string, defaultPath?: string): URL {
    // 特殊环境处理：qa环境下，特定接口要走pre环境
    if (
      host.includes('qa-kinsight.staging.kuaishou.com') &&
      (url.includes('local-agent-code-embedding') || url.includes('local-agent-code-rerank'))
    ) {
      return new URL(url, 'https://pre-kinsight.test.gifshow.com/');
    }

    // 开发环境处理（当前被注释掉，保留以备后用）
    // if (url.includes('nodeapi')) {
    //   return new URL(url, 'http://localhost:3001');
    // }

    const versionType = GlobalConfig.getConfig().getVersionType();

    // 只有 External 环境才使用代理路径
    const shouldUseProxy = versionType === 'External';
    const effectiveDefaultPath = shouldUseProxy ? defaultPath : undefined;

    if (effectiveDefaultPath) {
      return new URL(`${effectiveDefaultPath}${url.startsWith('/') ? url : `/${url}`}`, host);
    }

    return new URL(url, host);
  }
  async rawFetchEventSource(url: string, options: FetchEventSourceInit & { onFetchLog?: onFetchLog }) {
    let u = null;
    if (typeof url === 'string') {
      u = this.buildUrl(url, this._baseUrl, '/api/proxy/sse');
    }

    // 动态获取 JWT token 并注入到请求头 (仅在 External 环境下)
    if (GlobalConfig.getConfig().getVersionType() === 'External') {
      try {
        const token = GlobalConfig.getConfig().getJwtToken();
        if (token) {
          options.headers = {
            Authorization: `Bearer ${token}`,
            ...options.headers
          };
          this.logger.debug('JWT token injected into fetchEventSource headers', 'http-client');
        } else {
          this.logger.warn('Token provider returned empty token for fetchEventSource', 'http-client');
        }
      } catch (error: any) {
        this.logger.error('Failed to get JWT token from provider for fetchEventSource', 'http-client', { err: error });
      }
    }

    // 添加多语言支持 - 从 GlobalConfig 获取当前语言
    const currentLanguage = GlobalConfig.getConfig().getCurrentLanguage();
    const acceptLanguage = getAcceptLanguageHeader(currentLanguage);

    // 确保设置了默认的 Content-Type 和 Accept-Language
    options.headers = {
      'Content-Type': 'application/json;charset=UTF-8',
      'Accept-Language': acceptLanguage,
      ...options.headers
    };

    this.logger.debug('fetch event source body', 'http-client', {
      value: options.body
    });
    this.logger.debug('fetch event source headers', 'http-client', {
      value: options.headers
    });
    const fetchUrl = u ? u.toString() : url;

    const logger = this.logger;

    options.onFetchLog?.({ headers: options.headers, url: fetchUrl });
    return fetchEventSource(fetchUrl, {
      ...options,
      async onopen(response) {
        if (response.ok && response.headers.get('content-type')?.toLowerCase()?.includes("text/event-stream")) {
          logger.debug(`SSE connection opened successfully: ${response.status} ${response.statusText}`, 'http-client');
          return options.onopen?.(response);
        } else {

          let responseBody = '';
          try {
            // 设置超时时间为5秒
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Read response body timeout')), 5000);
            });
            const textPromise = response.clone().text();
            responseBody = await Promise.race([textPromise, timeoutPromise]) as string;
          } catch (bodyError) {
            logger.debug('Failed to read response body', 'http-client', { error: bodyError });
            responseBody = 'Unable to read response body';
          }

          const errorMessage = `fetchEventSource error: ${response.status} ${response.statusText}. Response: ${responseBody}`;
          logger.warn(errorMessage, 'http-client', {
            url: fetchUrl,
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            responseBody
          });
          throw new Error(errorMessage);
        }
      },
      onerror: (e) => {
        options.onerror?.(e);
        // 如果不抛出错误，就会自动重试;实际上层业务逻辑会有重试逻辑，不应该依赖这里的重试
        // https://github.com/Azure/fetch-event-source/blob/a0529492576e094374602f24d5e64b3a271b4576/src/fetch.ts#L129-L136
        throw e;
      }
    });
  }

  reportPerf(data: any) {
    const u = this.buildUrl('/eapi/kwaipilot/log/time', this._baseUrl, "/api/proxy");
    let headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }
    const token = GlobalConfig.getConfig().getJwtToken();
    if (token) {
      headers = {
        ...headers,
        Authorization: `Bearer ${token}`,
      };
    }
    const config = {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    };
    return fetch(u.toString(), config);
  }
}

export class Api extends HttpClient {
  constructor(config?: HttpClientOptions) {
    super(config);
  }
  async queryEmbedding(body: QueryEmbeddingBody) {
    const startTime = Date.now();
    const { data } = await this.post<QueryEmbeddingResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-embedding',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'queryEmbeddingCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  async codeRerank(body: CodeRerankBody) {
    const startTime = Date.now();
    const { data } = await this.post<CodeRerankResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-rerank',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'codeRerankCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  async cloudCodeSearch(body: CloudCodeSearchBody) {
    const startTime = Date.now();
    const { data } = await this.post<CloudCodeSearchResponse>(
      '/eapi/kwaipilot/plugin/code/search/generate_prompt',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'cloudCodeSearchCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  // todo: 这里是一个未实现的接口
  async getKconfValue<T>(key: string): Promise<T> {
    const response = await this.post<T>(
      `/eapi/kwaipilot/plugin/v2/config`,
      JSON.stringify({
        key
      })
    );
    return response.data;
  }
  async checkCloudIndex(username: string) {
    const response = await this.get<{ data: boolean }>(`/nodeapi/indexing/index-config`, { username });
    return response?.data;
  }
  // 获取索引配置
  async getIndexConfig() {
    const { data } = await this.get<{
      'file-index-batch': number;
      'index-request-batch': number;
    }>(`/nodeapi/indexing/index-config/index`);
    return data;
  }
  // 获取大仓库索引配置
  async getLargeRepoIndexConfig(key: string): Promise<{ commitId: string; branch: string } | null> {
    const startTime = Date.now();
    const { data } = await this.get<{ commitId: string; branch: string } | null>(
      `/eapi/kwaipilot/plugin/code/search/large-repo-index-config`,
      { key }
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'getLargeRepoIndexConfigCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  // 获取仓库索引的文件数
  async getRepoIndexFileCount(repoUrl: string) {
    const { data } = await this.get<{ count: number }>(`/nodeapi/indexing/file-index/user/indexedFiles`, {
      repoUrl: convertToGitSshFormat(repoUrl) || repoUrl || '',
      userId: GlobalConfig.getConfig().getUsername(),
      deviceId: GlobalConfig.getConfig().getDeviceId()
    });
    this.logger.info(`getRepoIndexFileCount: ${repoUrl} ${data?.count}`);
    return data;
  }
  // 更新仓库信息
  async updateRepoInfo(body: {
    userId: string;
    repoUrl: string;
    deviceId: string;
    ideVersion: string;
    dirPath: string;
    branch?: string;
    commitId?: string;
    baseCommitId?: string;
  }) {
    const { data } = await this.post<{ success: boolean }>('/nodeapi/indexing/repo/user', JSON.stringify(body));
    return data;
  }
  fetchEventSource(url: string, options: FetchEventSourceInit & { onFetchLog?: onFetchLog }): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }
  // 索引搜索
  async indexingSearch(body: IndexingSearchBody) {
    const startTime = Date.now();
    const { data } = await this.post<IndexingSearchResponse>('/nodeapi/indexing/search', JSON.stringify(body));
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'indexingSearchCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  // 清除用户索引
  async clearIndexForUser() {
    const { data } = await this.delete<{ success: boolean }>(
      '/nodeapi/indexing/repo/user/all',
      JSON.stringify({
        userId: GlobalConfig.getConfig().getUsername(),
        repoUrl: GlobalConfig.getConfig().getRepoInfo()?.git_url || '',
        deviceId: GlobalConfig.getConfig().getDeviceId()
      })
    );
    return data;
  }

  // 从服务端索引文件
  async indexFiles(
    files: { filepath: string; action: 'modify' | 'delete' | 'create'; content?: string }[],
    dirpath: string
  ): Promise<IndexUserFilesResponse> {
    const startTime = Date.now();
    const gitRepo = GlobalConfig.getConfig().getRepoInfo()?.git_url || '';
    let processedRepoId = gitRepo;
    if (gitRepo.startsWith('/')) {
      // 绝对路径直接使用
      processedRepoId = gitRepo;
      this.logger.debug(`Using absolute path as repo_id: ${processedRepoId}`);
    } else {
      // 认为是git url，转换为SSH格式
      const gitSshUrl = convertToGitSshFormat(gitRepo);
      if (gitSshUrl) {
        processedRepoId = gitSshUrl;
        this.logger.debug(`Converted git URL to SSH format: ${gitRepo} -> ${processedRepoId}`);
      } else {
        // 转换失败时使用原始值
        processedRepoId = gitRepo;
        this.logger.warn(`Failed to convert git URL to SSH format, using original: ${gitRepo}`);
      }
    }
    // 构造 JSON 请求体
    const requestBody = {
      username: GlobalConfig.getConfig().getUsername(),
      repoUrl: processedRepoId || dirpath,
      deviceId: GlobalConfig.getConfig().getDeviceId(),
      dirPath: dirpath,
      files: files.map((file) => {
        const fileData: any = {
          filepath: path.relative(dirpath, file.filepath),
          action: file.action
        };

        // 对于修改或创建操作，读取文件内容
        if (file.action === 'modify' || file.action === 'create') {
          try {
            if (file.content) {
              fileData.content = file.content;
            } else {
              if (fs.existsSync(file.filepath)) {
                const content = fs.readFileSync(file.filepath, 'utf-8');
                fileData.content = content;
                this.logger.debug(`已读取文件内容: ${file.filepath}`);
              } else {
                this.logger.warn(`文件不存在: ${file.filepath}`);
                fileData.content = '';
              }
            }
          } catch (error) {
            this.logger.error(`读取文件失败: ${file.filepath}`, error);
            fileData.content = '';
          }
        }

        return fileData;
      })
    };

    // 使用 post 方法发送 JSON 数据
    const { data } = await this.post<IndexUserFilesResponse>(
      '/nodeapi/indexing/file-index/user',
      JSON.stringify(requestBody)
    ).catch((e) => {
      this.logger.error('Index files failed:', e);
      return { data: {} };
    });
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'indexFilesCost',
      millis: Date.now() - startTime,
      extra3: files.length.toString(),
      extra4: 'batch_index_json'
    });
    return data as IndexUserFilesResponse;
  }

  // Vercel OAuth 代理方法
  async vercelOAuthAccessToken(body: VercelOAuthAccessTokenBody) {

    const { data } = await this.post<VercelOAuthAccessTokenResponse>(
      '/nodeapi/indexing/auth-proxy/vercel/oauth/access_token',
      JSON.stringify(body)
    );
    return data;
  }
}
