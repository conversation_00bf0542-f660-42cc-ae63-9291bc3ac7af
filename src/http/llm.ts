import { GlobalConfig } from '@/util/global';
import { Logger } from '@/util/log';
import { Api } from '@/http/index';

export const LLM_HEADERS = {
  'Content-Type': 'application/json'
};

// 重试配置
export const RETRY_CONFIG = {
  enabled: true, // 是否启用重试机制
  maxRetries: 3,
  baseDelay: 1000, // 基础延迟时间（毫秒）
  maxDelay: 10000, // 最大延迟时间（毫秒）
  backoffMultiplier: 2, // 指数退避倍数
  timeoutMs: 120000 // 超时时间（毫秒）- 60秒
};
const logger = new Logger('project-wiki-LLM');
// 判断是否为可重试的错误
function isRetryableError(error: any): boolean {
  // 网络错误
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
    return true;
  }

  // 超时错误
  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return true;
  }

  // HTTP状态码错误
  if (error.status) {
    const status = error.status;
    // 5xx 服务器错误和某些 4xx 错误可以重试
    return status >= 500 || status === 408 || status === 429;
  }

  // fetch 错误
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return true;
  }

  return false;
}

export async function callLLM(messages: any[] = [], dirPath: string): Promise<any> {
  const requestBody: any = {
    messages: messages,
    dirPath: dirPath,
    username: GlobalConfig.getConfig().getUsername(),
    version: 2
  };

  // 创建 Api 实例，配置重试机制
  const api = new Api({
    retryConfig: RETRY_CONFIG.enabled
      ? {
          maxRetries: RETRY_CONFIG.maxRetries,
          retryDelay: RETRY_CONFIG.baseDelay,
          retryCondition: (error: any) => isRetryableError(error),
          onRetry: (attempt: number, error: any) => {
            logger.warn(`LLM调用失败, 将进行第 ${attempt + 1} 次重试:`, {
              message: error.message,
              code: error.code,
              name: error.name
            });
          }
        }
      : undefined
  });

  try {
    logger.info('开始LLM调用');

    // Api 类会自动根据环境添加正确的前缀路径
    const requestPath = '/nodeapi/indexing/project-info/chat';

    // 使用 Api 实例发送 POST 请求，Api 类会自动处理重试和URL构建
    const response = await api.post(requestPath, JSON.stringify(requestBody), {
      headers: LLM_HEADERS
    });

    logger.info('LLM调用成功' + JSON.stringify(response));
    return response;
  } catch (error: any) {
    const errorInfo = {
      message: error.message,
      code: error.code,
      name: error.name
    };

    logger.error('LLM调用最终失败:', errorInfo);
    throw error;
  }
}
