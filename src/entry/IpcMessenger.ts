import { IProtocol } from '@/protocol/index';
import { AgentCommonMessage, IMessenger, type Message } from '@/protocol/messenger';
import { ChildProcessWithoutNullStreams } from 'node:child_process';
import * as fs from 'node:fs';
import net from 'node:net';
import { v4 as uuidv4 } from 'uuid';
import { Logger } from '@/util/log';
import { getErrorLogsPath } from '@/util/paths';
import { AGENT_NAMESPACE } from '@/util/const';
import { BufferDataHandle } from '@/util/messenger';

class IPCMessengerBase<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private _logger: Logger = new Logger('IpcMessengerBase');
  private readonly dataHandler = new BufferDataHandle();
  _sendMsg(message: Message) {
    throw new Error('Not implemented');
  }
  isInited = false;
  responsePendding: string[] = [];
  typeListeners = new Map<keyof ToProtocol, ((message: Message) => any)[]>();
  idListeners = new Map<string, (message: Message) => any>();
  setIsInited(isInited: boolean) {
    this.isInited = isInited;
    for (const data of this.responsePendding) {
      this._logger.debug(`处理缓存消息: ${data}`);
      this._handleLine(data);
    }
    this.responsePendding = [];
  }
  private _handleLine(line: string) {
    try {
      const msg: Message = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error('Invalid message sent: ' + JSON.stringify(msg));
      }
      this._logger.info(`Received message: ${msg.messageType}`);
      this._logger.debug(`Received message: ${JSON.stringify(msg)}`);

      const skipType = ['config/getIdeSetting', 'state/ideState', 'state/ideInfo'];
      // 项目未初始化完成，并且消息类型不是 config/getIdeSetting 或 state/ideState，则将消息缓存
      if (!this.isInited && !skipType.includes(msg.messageType) && !msg.messageType.includes('assistant/')) {
        this._logger.debug(`缓存消息: ${line}`);
        this.responsePendding.push(line);
        return;
      }

      // Call handler and respond with return value
      const listeners = this.typeListeners.get(msg.messageType as any);
      listeners?.forEach(async (handler) => {
        try {
          this._logger.debug(`handling message before: ${msg.messageType}`);

          const response = await handler(msg);
          this._logger.debug(`handling message after: ${msg.messageType}`);

          if (response && typeof response[Symbol.asyncIterator] === 'function') {
            for await (const update of response) {
              this.send(msg.messageType, update, msg.messageId);
            }
            this.send(msg.messageType, { done: true }, msg.messageId);
          } else {
            this.send(msg.messageType, response, msg.messageId);
          }
        } catch (e: any) {
          this._logger.warn(`Error running handler for "${msg.messageType}": `, e);
          this._onErrorHandlers.forEach((handler) => {
            handler(e);
          });
        }
      });

      // Call handler which is waiting for the response, nothing to return
      const requestHandler = this.idListeners.get(msg.messageId);
      if (requestHandler) {
        this._logger.debug(`处理 ide 返回的请求消息: ${msg.messageType}`);
        requestHandler(msg);
      }
    } catch (e) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + '...' + line.substring(line.length - 100);
      }
      this._logger.error('Error parsing line: ', truncatedLine, e);
      return;
    }
  }

  protected _handleData(data: Buffer) {
    this.dataHandler.handleData(data, (line) => this._handleLine(line));
  }

  private _onErrorHandlers: ((error: Error) => void)[] = [];

  onError(handler: (error: Error) => void) {
    this._onErrorHandlers.push(handler);
  }

  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]> {
    const start = Date.now();
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: Message) => {
        resolve(msg.data);
        this._logger.perf({
          namespace: AGENT_NAMESPACE,
          subtag: messageType as string,
          millis: Date.now() - start
        });
        this.idListeners.delete(messageId);
      };
      this._logger.info(`requst message sended to ide: ${messageType as string}`);
      this.idListeners.set(messageId, handler);
      this.send(messageType, data, messageId);
    });
  }

  mock(data: any) {
    const d = JSON.stringify(data);
    this._handleData(Buffer.from(d));
  }

  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string {
    messageId = messageId ?? uuidv4();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId
    };
    this._sendMsg(msg);
    return messageId;
  }

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0]): ToProtocol[T][1] {
    return this.typeListeners.get(messageType)?.[0]?.({
      messageId: uuidv4(),
      messageType: messageType as string,
      data
    });
  }

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => Promise<ToProtocol[T][1]> | ToProtocol[T][1]
  ): void {
    if (!this.typeListeners.has(messageType)) {
      this.typeListeners.set(messageType, []);
    }
    this.typeListeners.get(messageType)?.push(handler);
  }
}

export class IpcMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  extends IPCMessengerBase<ToProtocol, FromProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private logger = new Logger('IpcMessenger');
  constructor() {
    super();
    process.stdin.on('data', (data) => {
      this.logger.debug(`STDIN: Received data: ${data.toString()}`);
      this._handleData(data);
    });
    process.stdout.on('close', () => {
      fs.writeFileSync(getErrorLogsPath(), `${new Date().toISOString()}\n`);
      this.logger.info('CLOSE: Exiting kwaipilot core stdout close...');
      process.exit(1);
    });
    process.stdin.on('close', () => {
      fs.writeFileSync(getErrorLogsPath(), `${new Date().toISOString()}\n`);
      this.logger.info('CLOSE: Exiting kwaipilot core stdin close...');
      process.exit(1);
    });
  }
  _sendMsg(msg: Message) {
    const d = JSON.stringify(msg);
    this.logger.debug(`STDOUT:Sending message to ide: ${d}`);
    process.stdout?.write(d + '\r\n');
  }
}

export class CoreBinaryMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  extends IPCMessengerBase<ToProtocol, FromProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private logger = new Logger('CoreBinaryMessenger');
  constructor(private readonly subprocess: ChildProcessWithoutNullStreams) {
    super();
    this.logger.info('Setup');
    this.subprocess.stdout.on('data', (data) => {
      this.logger.info(`Received data from core: ${data.toString() + '\n'}`);
      this._handleData(data);
    });
    this.subprocess.stdout.on('close', () => {
      this.logger.info('kwaipilot core exited');
    });
    this.subprocess.stdin.on('close', () => {
      this.logger.info('kwaipilot core exited');
    });
  }
  getAgentVersion(): string {
    return '1.0.0';
  }
  getCommonMessage(): AgentCommonMessage {
    return {
      version: this.getAgentVersion()
    };
  }
  _sendMsg(msg: Message) {
    this.logger.info(`Sending message to core: ${JSON.stringify(msg)}`);
    if (!msg.common) {
      msg.common = this.getCommonMessage();
    }
    const d = JSON.stringify(msg);
    this.subprocess.stdin.write(d + '\r\n');
  }
}

export class CoreBinaryTcpMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  extends IPCMessengerBase<ToProtocol, FromProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private logger = new Logger('CoreBinaryTcpMessenger');
  private port: number = process.env.KWAIPILOT_AGENT_PORT ? parseInt(process.env.KWAIPILOT_AGENT_PORT) : 30001;
  private socket: net.Socket | null = null;

  typeListeners = new Map<keyof ToProtocol, ((message: Message) => any)[]>();
  idListeners = new Map<string, (message: Message) => any>();

  constructor() {
    super();
    this.connectWithRetry();
  }

  private async connectWithRetry(maxRetries: number = 5, retryDelay: number = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.info(`Attempting to connect to server on port ${this.port} (attempt ${attempt}/${maxRetries})`);

        const socket = net.createConnection(this.port, 'localhost');

        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Connection timeout'));
          }, 5000);

          socket.on('connect', () => {
            clearTimeout(timeout);
            this.socket = socket;
            this.logger.info('Successfully connected to server');
            resolve();
          });

          socket.on('error', (err: any) => {
            clearTimeout(timeout);
            reject(err);
          });
        });

        // Set up event handlers
        socket.on('data', (data: Buffer) => {
          this._handleData(data);
        });

        socket.on('end', () => {
          this.logger.info('Disconnected from server');
        });

        socket.on('error', (err: any) => {
          this.logger.error('Client error:', err);
        });

        return; // Successfully connected, exit retry loop

      } catch (error) {
        this.logger.warn(`Connection attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          this.logger.error(`Failed to connect after ${maxRetries} attempts`);
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  close() {
    this.socket?.end();
  }
  getAgentVersion(): string {
    return '1.0.0';
  }
  getCommonMessage(): AgentCommonMessage {
    return {
      version: this.getAgentVersion()
    };
  }
  _sendMsg(msg: Message) {
    if (this.socket) {
      // console.log("[info] Sending message to core:", msg);
      if (!msg.common) {
        msg.common = this.getCommonMessage();
      }
      const d = JSON.stringify(msg);
      this.socket.write(d + '\r\n');
    } else {
      this.logger.error('Socket is not connected');
    }
  }
}
