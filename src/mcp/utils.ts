import which from 'which';

/**
 * Deep comparison of two values for equality
 */
export function deepEqual(a: any, b: any): boolean {
  // Different types, return false directly
  if (typeof a !== typeof b) {
    return false;
  }
  // Object type, convert to JSON string for comparison
  if (typeof a === 'object' && a !== null) {
    return JSON.stringify(a) === JSON.stringify(b);
  }
  // Basic types direct comparison
  return a === b;
}

/**
 * Convert seconds to milliseconds
 * Used to convert timeout time (seconds) in configuration to millisecond units
 *
 * @param seconds - Number of seconds
 * @returns number Corresponding number of milliseconds
 *
 * @example
 * secondsToMs(5) // 5000
 */
export function secondsToMs(seconds: number): number {
  return seconds * 1000;
}

/**
 * Get node path
 * If node path doesn't exist, return 'node'
 * @returns string
 */
export async function getUserNodePath() {
  try {
    let cmdPath = await which('node');
    return cmdPath || 'node';
  } catch (error) {
    return 'node';
  }
}
