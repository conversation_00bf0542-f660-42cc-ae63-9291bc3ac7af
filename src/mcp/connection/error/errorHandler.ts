import { McpConnection } from '../../types';
import { ConnectionErrorLogger } from './errorLogger';
import { RetryManager } from '../retry';
import { MCPLogger } from '@/util/log';

/**
 * Connection error handler
 * Responsible for handling connection errors and deciding handling strategies
 */
export class ConnectionErrorHandler {
    private errorLogger: ConnectionErrorLogger;
    private retryManager: RetryManager;
    private logger: InstanceType<typeof MCPLogger>;
    private onServerChange: () => void;

    constructor(
        retryManager: RetryManager,
        logger: InstanceType<typeof MCPLogger>,
        onServerChange: () => void
    ) {
        this.errorLogger = new ConnectionErrorLogger();
        this.retryManager = retryManager;
        this.logger = logger;
        this.onServerChange = onServerChange;
    }

    /**
     * Handle transport layer errors
     * @param connection - MCP connection object
     * @param error - Error information
     */
    public async handleTransportError(connection: McpConnection, error: any): Promise<void> {
        const errorMessage = error instanceof Error ? error.message : String(error);

        this.logger.error(`mcp server ${connection.server.name} connection error`, error);

        connection.server.status = 'disconnected';
        this.errorLogger.appendErrorMessage(connection, errorMessage);
        this.errorLogger.appendFullErrorMessage(connection, errorMessage);

        this.onServerChange();
    }

    /**
     * Handle client errors
     * @param connection - MCP connection object
     * @param error - Error information
     * @param retryCallback - Retry callback function
     */
    public async handleClientError(
        connection: McpConnection,
        error: any,
        retryCallback?: () => Promise<void>
    ): Promise<void> {
        const errorMessage = error instanceof Error ? error.message : String(error);

        this.logger.error(`mcp server ${connection.server.name} client error`, error);

        connection.server.status = 'disconnected';

        this.errorLogger.appendErrorMessage(connection, errorMessage);
        this.errorLogger.appendFullErrorMessage(connection, errorMessage);

        // Handle connection errors, decide whether to retry
        if (retryCallback) {
            await this.handleConnectionError(connection.server.name, errorMessage, retryCallback, connection);
        }

        this.onServerChange();
    }

    /**
     * Handle connection close
     * @param connection - MCP connection object
     */
    public async handleConnectionClose(connection: McpConnection): Promise<void> {
        this.logger.info(`mcp server ${connection.server.name} connection closed`);
        connection.server.status = 'disconnected';
        this.onServerChange();
    }

    /**
     * Handle connection errors and decide whether to retry
     * @param name - Connection name
     * @param error - Error information
     * @param retryCallback - Retry callback function
     * @param connection - Connection object (optional)
     */
    private async handleConnectionError(
        name: string,
        error: string,
        retryCallback: () => Promise<void>,
        connection?: any
    ): Promise<void> {
        // Only retry servers that have been successfully connected before
        // This logic needs to be handled by the caller, as it needs access to the connection object
        if (!connection.server.prevConnected) {
            this.logger.info(`Connection ${name} not connected, skipping retry`);
            return;
        }

        // Check if error is retryable
        if (this.retryManager.isRetryableError(error)) {
            this.logger.info(`Connection ${name} encountered retryable error: ${error}`);
            // Set status to connecting when retry starts
            await this.retryManager.executeRetry(name, error, retryCallback, connection);
        } else {
            this.logger.warn(`Connection ${name} encountered non-retryable error: ${error}`);
            this.retryManager.clearRetryState(name);
        }
    }

    /**
     * Clear connection error information
     * @param connection - MCP connection object
     */
    public clearErrors(connection: McpConnection): void {
        this.errorLogger.clearErrorMessages(connection);
    }

    /**
     * Get error logger
     */
    public getErrorLogger(): ConnectionErrorLogger {
        return this.errorLogger;
    }

    /**
     * Notify server status change
     */
    public notifyServerChange(): void {
        this.onServerChange();
    }

    /**
     * Get retry manager
     */
    public getRetryManager(): RetryManager {
        return this.retryManager;
    }
}