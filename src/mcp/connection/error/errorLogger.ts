import { McpConnection } from '../../types';
import { getNowTimeStr } from '../utils';

/**
 * Connection error logger
 * Responsible for managing connection error information recording and formatting
 */
export class ConnectionErrorLogger {
    /**
     * Add error message to connection object
     * @param connection - MCP connection object
     * @param error - Error message
     */
    public appendErrorMessage(connection: McpConnection, error: string): void {
        // Don't aggregate multiple error messages, only return the last message
        connection.server.error = error;
    }

    /**
     * Add complete error message to connection object (including timestamp)
     * @param connection - MCP connection object
     * @param error - Error message
     */
    public appendFullErrorMessage(connection: McpConnection, error: string): void {
        const errorWithTime = `[${getNowTimeStr()}] ${error}`;
        const prev = connection.server.fullError ? connection.server.fullError.split('\n') : [];

        // Insert new error at the beginning
        prev.unshift(errorWithTime);

        // Only keep the first 50 (newest at the top)
        const first50 = prev.slice(0, 50);
        connection.server.fullError = first50.join('\n');
    }

    /**
     * Clear connection error messages
     * @param connection - MCP connection object
     */
    public clearErrorMessages(connection: McpConnection): void {
        connection.server.error = '';
        connection.server.fullError = '';
    }
}