import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { McpConnection } from '../types';
import { ServerConfigSchema } from '../settingSchema';
import { formatValidationError } from '../parserHelper/validationHelper';
import { LoggingMessageNotificationSchema, ProgressNotificationSchema } from '@modelcontextprotocol/sdk/types.js';
import { TransportFactory } from './transport';
import { ConnectionErrorHandler } from './error';
import { MCPLogger } from '@/util/log';

/**
 * Connection factory
 * Responsible for creating MCP connection instances
 */
export class ConnectionFactory {
    private transportFactory: TransportFactory;
    private errorHandler: ConnectionErrorHandler;
    private logger: InstanceType<typeof MCPLogger>;
    private clientVersion: string;

    constructor(
        clientVersion: string,
        errorHandler: ConnectionErrorHandler,
        logger: InstanceType<typeof MCPLogger>
    ) {
        this.clientVersion = clientVersion;
        this.errorHandler = errorHandler;
        this.logger = logger;
        this.transportFactory = new TransportFactory(logger);
    }

    /**
     * Create new MCP connection object
     * Create client instance and transport layer based on configuration, but do not establish actual connection
     *
     * @param name - MCP server name
     * @param jsonConfig - Server configuration information
     * @param retryCallback - Retry callback function (optional)
     * @returns Newly created connection object
     */
    public async createConnection(name: string, jsonConfig: unknown, retryCallback?: () => Promise<void>): Promise<McpConnection> {
        this.logger.info('createConnection start', name, jsonConfig);

        // Parse configuration
        const result = ServerConfigSchema.safeParse(jsonConfig);

        if (!result.success) {
            const connection: McpConnection = {
                server: {
                    name,
                    config: JSON.stringify(result),
                    status: 'disconnected',
                    error: formatValidationError(result.error, 'MCP settings file').message
                }
            };

            this.logger.info(`ServerConfigSchema parsing failed: ${name}`, connection);
            return connection;
        }

        const config = result.data;

        // Create client instance
        const client = this.createClient();

        // Set up client notification handlers
        this.setupClientNotificationHandlers(client, name);

        // Create and initialize connection object
        const connection: McpConnection = {
            server: {
                name,
                config: JSON.stringify(config),
                status: config.disabled ? 'disconnected' : 'connecting',
                disabled: config.disabled,
            },
            client,
            transportType: config.transportType,
        };

        // Create transport layer
        const transport = await this.createTransport(config, name, client, connection);

        // Update connection object, add transport layer
        connection.transport = transport;

        this.logger.info(`createConnection success: ${name}`, {
            name,
            config: JSON.stringify(config),
            status: config.disabled ? 'disconnected' : 'connecting',
            disabled: config.disabled,
        });

        return connection;
    }

    /**
     * Create MCP client instance
     * @returns Client instance
     */
    private createClient(): Client {
        return new Client(
            {
                name: 'Kwaipilot-client',
                version: this.clientVersion
            },
            {
                capabilities: {}
            }
        );
    }

    /**
     * Set up client notification handlers
     * @param client - Client instance
     * @param name - Connection name
     */
    private setupClientNotificationHandlers(client: Client, name: string): void {
        client.setNotificationHandler(LoggingMessageNotificationSchema, (notification) => {
            this.logger.info(`mcp server ${name} notification`, notification);
        });

        client.setNotificationHandler(ProgressNotificationSchema, (notification) => {
            this.logger.info(`mcp server ${name} progress`, notification);
        });
    }

    /**
     * Create transport layer
     * @param config - Configuration object
     * @param name - Connection name
     * @param client - Client instance
     * @param connection - Connection object
     * @param isRetry - Whether it's a retry operation
     * @returns Transport layer instance
     */
    private async createTransport(config: any, name: string, client: Client, connection: McpConnection, isRetry: boolean = false): Promise<any> {
        const transportType = this.transportFactory.getTransportType(config);

        // Create error and close handling callbacks
        const onTransportError = async (error: any) => {
            await this.errorHandler.handleTransportError(connection, error);
        };

        const onTransportClose = async () => {
            await this.errorHandler.handleConnectionClose(connection);
        };

        const onClientError = async (error: any) => {
            // Check if already retrying to avoid duplicate triggering
            const retryInfo = this.errorHandler.getRetryManager().getRetryInfo(name);
            if (retryInfo && retryInfo.isRetrying) {
                this.logger.debug(`Connection ${name} is already retrying, skipping duplicate error handling`);
                return;
            }

            // Create retry callback function
            const retryCallback = async () => {
                try {
                    this.logger.info(`Starting retry connection: ${name}`);

                    // Set connection status to connecting
                    connection.server.status = 'connecting';
                    this.errorHandler.notifyServerChange();

                    // Recreate connection and transport layer
                    const config = JSON.parse(connection.server.config);
                    const newTransport = await this.createTransport(config, name, client, connection, true);
                    connection.transport = newTransport;

                    // Reconnect
                    await connection.client!.connect(connection.transport!);

                    // Update connection status to connected
                    connection.server.status = 'connected';
                    this.errorHandler.clearErrors(connection);
                    connection.server.prevConnected = true;

                    this.logger.info(`Connection ${name} retry successful`);

                    // Notify status change
                    this.errorHandler.notifyServerChange();

                } catch (retryError) {
                    this.logger.error(`Connection ${name} retry failed:`, retryError);
                    connection.server.status = 'disconnected';
                    this.errorHandler.notifyServerChange();
                    throw retryError; // Re-throw error for retry manager to handle
                }
            };

            await this.errorHandler.handleClientError(connection, error, retryCallback);
        };

        const onClientClose = async () => {
            await this.errorHandler.handleConnectionClose(connection);
        };

        // Create transport layer
        const transport = await this.transportFactory.createTransport(
            config,
            name,
            onTransportError,
            onTransportClose
        );

        // StreamableHttp and SSE need client-level error handling
        // Only set client event handlers in non-retry cases to avoid duplicate setup
        if (!isRetry) {
            client.onerror = onClientError;
            client.onclose = onClientClose;
        }

        return transport;
    }
}