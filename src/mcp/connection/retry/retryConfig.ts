/**
 * Retry configuration interface
 */
export interface RetryConfig {
    maxRetries: number;
    initialDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    retryableErrors: string[];
}

/**
 * Connection retry status
 */
export interface RetryState {
    isRetrying: boolean;
    retryCount: number;
    lastRetryTime: number;
    nextRetryDelay: number;
}

/** Retry configuration default values */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
    maxRetries: 10,
    initialDelay: 1000, // 1 second
    maxDelay: 30000, // 30 seconds
    backoffMultiplier: 2,
    retryableErrors: [
        'SSE error: TypeError: terminated:',
        'terminated: other side closed',
        'ECONNREFUSED',
        'ENOTFOUND',
        'ETIMEDOUT',
        'ECONNRESET',
        'StreamableHttp error',
        'streamable connection failed',
        'http streaming error'
    ]
};