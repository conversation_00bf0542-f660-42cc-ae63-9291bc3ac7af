import { RetryConfig, RetryState } from './retryConfig';
import { RetryStateManager } from './retryState';
import { MCPLogger } from '@/util/log';

/**
 * Retry manager
 * Responsible for handling connection retry logic
 */
export class RetryManager {
    private stateManager: RetryStateManager;
    private logger: InstanceType<typeof MCPLogger>;

    constructor(retryConfig: RetryConfig, logger: InstanceType<typeof MCPLogger>) {
        this.stateManager = new RetryStateManager(retryConfig);
        this.logger = logger;
    }

    /**
     * Determine if error is retryable
     * @param error - Error message
     * @returns Whether retryable
     */
    public isRetryableError(error: string): boolean {
        if (!error) return false;
        const retryConfig = this.stateManager.getRetryConfig();
        return retryConfig.retryableErrors.some(retryableError =>
            error.includes(retryableError)
        );
    }

    /**
     * Calculate next retry delay time (exponential backoff)
     * @param retryCount - Current retry count
     * @returns Delay time (milliseconds)
     */
    public calculateRetryDelay(retryCount: number): number {
        const retryConfig = this.stateManager.getRetryConfig();
        const delay = retryConfig.initialDelay * Math.pow(retryConfig.backoffMultiplier, retryCount);
        return Math.min(delay, retryConfig.maxDelay);
    }

    /**
     * Execute retry connection
     * @param name - Connection name
     * @param error - Error that caused retry
     * @param retryCallback - Retry callback function
     * @param connection - Connection object (optional, for updating status)
     */
    public async executeRetry(
        name: string,
        error: string,
        retryCallback: () => Promise<void>,
        connection?: any
    ): Promise<void> {
        const retryState = this.stateManager.getRetryState(name);
        if (!retryState) {
            this.stateManager.initializeRetryState(name);
        }

        const currentState = this.stateManager.getRetryState(name)!;
        const retryConfig = this.stateManager.getRetryConfig();

        // Check if maximum retry count exceeded
        if (currentState.retryCount >= retryConfig.maxRetries) {
            this.logger.warn(`Connection ${name} has reached maximum retry count (${retryConfig.maxRetries}), stopping retry`);
            this.stateManager.clearRetryState(name);
            return;
        }

        // Check if already retrying
        if (currentState.isRetrying) {
            this.logger.debug(`Connection ${name} is already retrying, skipping`);
            return;
        }

        // Update retry status
        const nextRetryCount = currentState.retryCount + 1;
        const nextRetryDelay = this.calculateRetryDelay(nextRetryCount);

        this.stateManager.updateRetryState(name, {
            isRetrying: true,
            retryCount: nextRetryCount,
            lastRetryTime: Date.now(),
            nextRetryDelay
        });

        this.logger.info(`Connection ${name} will retry for the ${nextRetryCount}th time in ${nextRetryDelay}ms`);

        // Set retry timer
        const timer = setTimeout(async () => {
            // Set status to connecting when retry starts
            if (connection) {
                connection.server.status = 'connecting';
            }
            try {
                this.logger.info(`Starting retry connection: ${name} (${nextRetryCount}th attempt)`);

                await retryCallback();

                // Connection successful, clear retry status
                this.logger.info(`Connection ${name} retry successful`);
                this.stateManager.clearRetryState(name);

            } catch (retryError) {
                this.logger.error(`Connection ${name} retry failed (${nextRetryCount}th attempt):`, retryError);

                // Update retry status, prepare for next retry
                this.stateManager.updateRetryState(name, { isRetrying: false });

                // Check if continue retrying
                if (nextRetryCount < retryConfig.maxRetries) {
                    // Use setTimeout instead of recursive call to avoid duplicate execution
                    const nextRetryDelay = this.calculateRetryDelay(nextRetryCount + 1);
                    this.logger.info(`Connection ${name} will retry for the ${nextRetryCount + 1}th time in ${nextRetryDelay}ms`);

                    const nextTimer = setTimeout(async () => {
                        await this.executeRetry(name, retryError instanceof Error ? retryError.message : String(retryError), retryCallback);
                    }, nextRetryDelay);

                    this.stateManager.setRetryTimer(name, nextTimer);
                } else {
                    this.logger.warn(`Connection ${name} retry count has reached limit, stopping retry`);
                    this.stateManager.clearRetryState(name);
                }
            }
        }, nextRetryDelay);

        this.stateManager.setRetryTimer(name, timer);
    }

    /**
     * Get connection retry information
     * @param name - Connection name
     * @returns Retry information
     */
    public getRetryInfo(name: string): RetryState | null {
        return this.stateManager.getRetryState(name) || null;
    }

    /**
     * Clear retry status
     * @param name - Connection name
     */
    public clearRetryState(name: string): void {
        this.stateManager.clearRetryState(name);
    }

    /**
     * Update retry configuration
     * @param newConfig - New retry configuration
     */
    public updateRetryConfig(newConfig: Partial<RetryConfig>): void {
        this.stateManager.updateRetryConfig(newConfig);
        this.logger.info('Retry configuration updated:', this.stateManager.getRetryConfig());
    }

    /**
     * Clean up all retry status and timers
     */
    public cleanup(): void {
        this.stateManager.cleanup();
        this.logger.info('All retry status and timers cleaned up');
    }
}