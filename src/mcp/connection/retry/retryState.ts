import { RetryState, RetryConfig } from './retryConfig';

/**
 * Retry status manager
 * Responsible for managing connection retry status and timers
 */
export class RetryStateManager {
    /** Connection retry status mapping */
    private retryStates: Map<string, RetryState> = new Map();

    /** Retry timer mapping */
    private retryTimers: Map<string, NodeJS.Timeout> = new Map();

    /** Retry configuration */
    private retryConfig: RetryConfig;

    constructor(retryConfig: RetryConfig) {
        this.retryConfig = retryConfig;
    }

    /**
     * Initialize connection retry status
     * @param name - Connection name
     */
    public initializeRetryState(name: string): void {
        this.retryStates.set(name, {
            isRetrying: false,
            retryCount: 0,
            lastRetryTime: 0,
            nextRetryDelay: this.retryConfig.initialDelay
        });
    }

    /**
     * Get connection retry status
     * @param name - Connection name
     * @returns Retry status
     */
    public getRetryState(name: string): RetryState | undefined {
        return this.retryStates.get(name);
    }

    /**
     * Update retry status
     * @param name - Connection name
     * @param updates - Status updates to apply
     */
    public updateRetryState(name: string, updates: Partial<RetryState>): void {
        const currentState = this.retryStates.get(name);
        if (currentState) {
            this.retryStates.set(name, { ...currentState, ...updates });
        }
    }

    /**
     * Clear retry status
     * @param name - Connection name
     */
    public clearRetryState(name: string): void {
        this.retryStates.delete(name);
        this.cancelRetry(name);
    }

    /**
     * Cancel retry
     * @param name - Connection name
     */
    public cancelRetry(name: string): void {
        const timer = this.retryTimers.get(name);
        if (timer) {
            clearTimeout(timer);
            this.retryTimers.delete(name);
        }
    }

    /**
     * Set retry timer
     * @param name - Connection name
     * @param timer - Timer
     */
    public setRetryTimer(name: string, timer: NodeJS.Timeout): void {
        this.retryTimers.set(name, timer);
    }

    /**
     * Clean up all retry status and timers
     */
    public cleanup(): void {
        // Clear all retry timers
        this.retryTimers.forEach((timer) => {
            clearTimeout(timer);
        });
        this.retryTimers.clear();

        // Clear all retry status
        this.retryStates.clear();
    }

    /**
     * Update retry configuration
     * @param newConfig - New retry configuration
     */
    public updateRetryConfig(newConfig: Partial<RetryConfig>): void {
        this.retryConfig = { ...this.retryConfig, ...newConfig };
    }

    /**
     * Get retry configuration
     */
    public getRetryConfig(): RetryConfig {
        return this.retryConfig;
    }
}