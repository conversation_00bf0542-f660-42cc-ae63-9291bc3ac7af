import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { McpConnection } from '../../types';
import { SSETransportHandler } from './handlers/sseHandler';
import { MCPLogger } from '@/util/log';

/**
 * Transport layer fallback manager
 * Responsible for handling StreamableHTTP to SSE fallback logic
 */
export class TransportFallbackManager {
    private sseHandler: SSETransportHandler;
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
        this.sseHandler = new SSETransportHandler(logger);
    }

    /**
     * Determine if should fallback to SSE
     * @param error - Error information
     * @returns Whether should fallback
     */
    public shouldFallbackToSse(error: any): boolean {
        const errorMessage = error instanceof Error ? error.message : String(error);

        // Network layer errors and HTTP errors support fallback
        const fallbackErrors = [
            'ECONNREFUSED', 'ENOTFOUND', 'ETIMEDOUT', 'ECONNRESET',
            'fetch failed', 'network error', 'timeout', 'unsupported',
            'StreamableHttp', 'streamable', '404', '500', '502', '503', '504', '405',
            'connection failed', 'request failed', 'http error'
        ];

        return fallbackErrors.some(pattern =>
            errorMessage.toLowerCase().includes(pattern.toLowerCase())
        );
    }

    /**
     * Fallback to SSE connection
     * @param connection - MCP connection object
     * @param configObj - Configuration object
     * @param onError - Error handling callback
     * @param onClose - Close handling callback
     */
    public async fallbackToSse(
        connection: McpConnection,
        configObj: any,
        onError: (error: any) => Promise<void>,
        onClose: () => Promise<void>
    ): Promise<void> {
        const { name } = connection.server;

        try {
            this.logger.info(`Falling back to SSE connection: ${name}`);

            // Close existing transport
            await connection.transport?.close();

            // Create SSE transport
            const sseTransport = this.sseHandler.createTransport(configObj);

            // Update connection configuration
            connection.transport = sseTransport;
            connection.transportType = 'sse';

            // Update transport type in configuration to ensure correct type is used when reconnecting
            const updatedConfig = { ...configObj, transportType: 'sse' };
            connection.server.config = JSON.stringify(updatedConfig);

            this.logger.info(`SSE fallback transport layer created successfully: ${name}`);

        } catch (sseError) {
            this.logger.error(`SSE fallback connection failed: ${name}`, sseError);
            throw new Error(`Connection failed: Both StreamableHttp and SSE cannot connect`);
        }
    }
}