import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { MCPLogger } from '@/util/log';

/**
 * HTTP transport handler
 * Responsible for creating and configuring StreamableHTTP transport layer
 */
export class HTTPTransportHandler {
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
    }

    /**
     * Create StreamableHTTP transport layer
     * @param config - Configuration object
     * @returns StreamableHTTP transport layer instance
     */
    public createTransport(config: any): StreamableHTTPClientTransport {
        const streamableOptions: any = {};

        const headers = config.headers || config.header;
        if (headers && Object.keys(headers).length > 0) {
            this.logger.info('createConnection streamableHttp headers:', headers);
            streamableOptions.headers = headers;
        }

        return new StreamableHTTPClientTransport(new URL(config.url), streamableOptions);
    }

    /**
     * Configure HTTP transport layer error and close handling
     * Note: StreamableHTTP transport layer error handling is usually handled at client level
     * @param transport - Transport layer instance
     * @param name - Connection name
     * @param onError - Error handling callback
     * @param onClose - Close handling callback
     */
    public configureTransportHandlers(
        transport: StreamableHTTPClientTransport,
        name: string,
        onError: (error: any) => Promise<void>,
        onClose: () => Promise<void>
    ): void {
        // StreamableHTTP transport layer usually doesn't directly set onerror and onclose
        // These events are handled at client level
    }
}