import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { processNodeCommand } from '../../utils';
import { MCPLogger } from '@/util/log';

/**
 * Stdio transport handler
 * Responsible for creating and configuring Stdio transport layer
 */
export class StdioTransportHandler {
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
    }

    /**
     * Create Stdio transport layer
     * @param config - Configuration object
     * @returns Stdio transport layer instance
     */
    public async createTransport(config: any): Promise<StdioClientTransport> {
        let command = ((config && config.command) || '').trim();

        // node command needs to use user-set node, requires special handling
        if (command === 'node') {
            this.logger.info('createConnection isNodeCommand');
            command = await processNodeCommand(command);
            this.logger.info('createConnection getNodePath:', command);
        }

        return new StdioClientTransport({
            command,
            args: config.args,
            env: {
                ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
                ...(config.env || {})
            },
            stderr: 'pipe'
        });
    }

    /**
     * Configure Stdio transport layer error and close handling
     * @param transport - Transport layer instance
     * @param name - Connection name
     * @param onError - Error handling callback
     * @param onClose - Close handling callback
     */
    public configureTransportHandlers(
        transport: StdioClientTransport,
        name: string,
        onError: (error: any) => Promise<void>,
        onClose: () => Promise<void>
    ): void {
        transport.onerror = onError;
        transport.onclose = onClose;
    }
}