import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { MCPLogger } from '@/util/log';

/**
 * SSE transport handler
 * Responsible for creating and configuring SSE transport layer
 */
export class SSETransportHandler {
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
    }

    /**
     * Create SSE transport layer
     * @param config - Configuration object
     * @returns SSE transport layer instance
     */
    public createTransport(config: any): SSEClientTransport {
        const sseOptions: any = {};

        // Set SSE required request headers
        const sseHeaders = {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            ...(config.headers || config.header || {})
        };

        this.logger.info('createConnection sse headers:', sseHeaders);

        sseOptions.eventSourceInit = {
            fetch: async (url: string, init: RequestInit) => {
                return fetch(url, {
                    ...init,
                    headers: {
                        ...init.headers,
                        ...sseHeaders
                    }
                });
            }
        };

        sseOptions.requestInit = {
            headers: sseHeaders
        };

        return new SSEClientTransport(new URL(config.url), sseOptions);
    }

    /**
     * Configure SSE transport layer error and close handling
     * Note: SSE transport layer error handling is usually handled at client level
     * @param transport - Transport layer instance
     * @param name - Connection name
     * @param onError - Error handling callback
     * @param onClose - Close handling callback
     */
    public configureTransportHandlers(
        transport: SSEClientTransport,
        name: string,
        onError: (error: any) => Promise<void>,
        onClose: () => Promise<void>
    ): void {
        // SSE transport layer error handling is usually handled at client level
        // Keep empty implementation to maintain interface consistency
    }
}