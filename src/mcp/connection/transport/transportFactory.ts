import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { getTransportType } from '../../settingSchema';
import { StdioTransportHandler } from './handlers/stdioHandler';
import { SSETransportHandler } from './handlers/sseHandler';
import { HTTPTransportHandler } from './handlers/httpHandler';
import { MCPLogger } from '@/util/log';

/**
 * 传输层工厂
 * 负责根据配置创建相应的传输层实例
 */
export class TransportFactory {
    private stdioHandler: StdioTransportHandler;
    private sseHandler: SSETransportHandler;
    private httpHandler: HTTPTransportHandler;
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
        this.stdioHandler = new StdioTransportHandler(logger);
        this.sseHandler = new SSETransportHandler(logger);
        this.httpHandler = new HTTPTransportHandler(logger);
    }

    /**
     * 创建传输层实例
     * @param config - 配置对象
     * @param name - 连接名称
     * @param onError - 错误处理回调
     * @param onClose - 关闭处理回调
     * @returns 传输层实例
     */
    public async createTransport(
        config: any,
        name: string,
        onError: (error: any) => Promise<void>,
        onClose: () => Promise<void>
    ): Promise<StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport> {
        // 推断传输类型（如果没有显式指定）
        const inferredTransportType = getTransportType(config);

        let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport;

        switch (inferredTransportType) {
            case 'streamableHttp':
                transport = this.httpHandler.createTransport(config);
                this.httpHandler.configureTransportHandlers(transport as StreamableHTTPClientTransport, name, onError, onClose);
                break;

            case 'sse':
                transport = this.sseHandler.createTransport(config);
                this.sseHandler.configureTransportHandlers(transport as SSEClientTransport, name, onError, onClose);
                break;

            default: // stdio
                transport = await this.stdioHandler.createTransport(config);
                this.stdioHandler.configureTransportHandlers(transport as StdioClientTransport, name, onError, onClose);
                break;
        }

        return transport;
    }

    /**
     * 获取传输类型
     * @param config - 配置对象
     * @returns 传输类型
     */
    public getTransportType(config: any): string {
        return getTransportType(config);
    }
}