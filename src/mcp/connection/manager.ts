import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { McpConnection } from '../types';
import { getTransportType } from '../settingSchema';
import { ConnectionFactory } from './factory';
import { RetryManager, DEFAULT_RETRY_CONFIG, RetryConfig, RetryState } from './retry';
import { ConnectionErrorHandler } from './error';
import { ToolsManager } from './tools';
import { TransportFallbackManager } from './transport';
import { MCPLogger } from '@/util/log';

/**
 * MCP connection manager
 * Responsible for managing the connection lifecycle of all MCP servers
 */
export class McpConnectionManager {
    private connections: McpConnection[] = [];
    private clientVersion: string;
    private onServerChange: () => void;
    private connectionFactory: ConnectionFactory;
    private retryManager: RetryManager;
    private errorHandler: ConnectionErrorHandler;
    private toolsManager: ToolsManager;
    private fallbackManager: TransportFallbackManager;
    private logger: InstanceType<typeof MCPLogger>;

    constructor(clientVersion: string, onServerChange: () => void, retryConfig?: Partial<RetryConfig>) {
        this.clientVersion = clientVersion;
        this.onServerChange = onServerChange;
        this.logger = new MCPLogger(`mcp-connection-${this.clientVersion}`);

        const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
        this.retryManager = new RetryManager(finalRetryConfig, this.logger);
        this.errorHandler = new ConnectionErrorHandler(this.retryManager, this.logger, this.onServerChange);
        this.toolsManager = new ToolsManager(this.logger);
        this.fallbackManager = new TransportFallbackManager(this.logger);
        this.connectionFactory = new ConnectionFactory(this.clientVersion, this.errorHandler, this.logger);
    }

    public getConnections(): McpConnection[] {
        return this.connections;
    }

    public getConnection(name: string): McpConnection | undefined {
        return this.connections.find((conn) => conn.server.name === name);
    }

    public async createConnection(name: string, jsonConfig: unknown): Promise<McpConnection> {
        const connection = await this.connectionFactory.createConnection(name, jsonConfig);
        this.connections.push(connection);
        return connection;
    }

    public async establishConnection(connection: McpConnection): Promise<void> {
        const { name, config } = connection.server;

        if (!connection.transport || !connection.client) {
            connection.server.status = 'disconnected';
            this.onServerChange();
            return;
        }

        this.logger.info('establishConnection start', connection);

        try {
            const configObj = JSON.parse(config);
            const inferredType = getTransportType(configObj);

            if (inferredType === 'streamableHttp') {
                await this.establishStreamableHttpWithFallback(connection, configObj);
            } else {
                await this.establishDirectConnection(connection, configObj);
            }
        } catch (error) {
            this.logger.error(`Failed to establish connection: ${name}:`, error);
            connection.server.status = 'disconnected';
            this.onServerChange();
            throw error;
        }
    }

    public async removeConnection(name: string): Promise<void> {
        const connection = this.getConnection(name);
        if (connection) {
            this.connections = this.connections.filter((conn) => conn.server.name !== name);
            connection.server.status = 'disconnected';
            this.retryManager.clearRetryState(name);
            this.onServerChange();

            try {
                await connection.transport?.close();
                await connection.client?.close();
            } catch (error) {
                this.logger.error(`Failed to delete connection: ${name}:`, error);
            }
        }
    }

    public async toggleConnectionState(name: string, disabled: boolean): Promise<void> {
        const connection = this.getConnection(name);
        if (!connection) {
            throw new Error(`Connection ${name} not found`);
        }

        connection.server.disabled = disabled;

        if (disabled) {
            await this.removeConnection(name);
            const config = JSON.parse(connection.server.config);
            config.disabled = disabled;
            await this.createConnection(name, config);
        } else {
            try {
                await this.removeConnection(name);
                const config = JSON.parse(connection.server.config);
                config.disabled = disabled;
                const newConnection = await this.createConnection(name, config);
                await this.establishConnection(newConnection);
                this.onServerChange();
            } catch (error) {
                this.logger.error(`Failed to toggle connection: ${name}:`, error);
                this.onServerChange();
                throw error;
            }
        }
    }

    public async manualRetry(name: string): Promise<boolean> {
        const connection = this.getConnection(name);
        if (!connection) {
            this.logger.error(`Connection ${name} does not exist`);
            return false;
        }

        if (connection.server.disabled) {
            this.logger.warn(`Connection ${name} is disabled, cannot retry`);
            return false;
        }

        if (connection.server.status === 'connected') {
            this.logger.info(`Connection ${name} is already connected, no need to retry`);
            return true;
        }

        if (!connection.server.prevConnected) {
            this.logger.warn(`Connection ${name} has never been successfully connected, cannot manually retry`);
            return false;
        }

        this.retryManager.clearRetryState(name);

        const retryCallback = async () => {
            const config = JSON.parse(connection.server.config);
            await this.removeConnection(name);
            const newConnection = await this.createConnection(name, config);
            await this.establishConnection(newConnection);
        };

        await this.retryManager.executeRetry(name, 'Manual retry triggered', retryCallback);
        return true;
    }

    public getRetryInfo(name: string): RetryState | null {
        return this.retryManager.getRetryInfo(name);
    }

    public updateRetryConfig(newConfig: Partial<RetryConfig>): void {
        this.retryManager.updateRetryConfig(newConfig);
    }

    public cleanup(): void {
        this.retryManager.cleanup();
    }

    private async establishStreamableHttpWithFallback(connection: McpConnection, configObj: any): Promise<void> {
        const { name } = connection.server;

        try {
            this.logger.info(`Attempting StreamableHttp connection: ${name}`);
            await this.establishDirectConnection(connection, configObj);
        } catch (streamableError) {
            this.logger.warn(`StreamableHttp connection failed: ${name}`, streamableError);

            if (this.fallbackManager.shouldFallbackToSse(streamableError)) {
                await this.fallbackToSse(connection, configObj);
            } else {
                throw streamableError;
            }
        }
    }

    private async fallbackToSse(connection: McpConnection, configObj: any): Promise<void> {
        const { name } = connection.server;

        const onError = async (error: any) => {
            await this.errorHandler.handleTransportError(connection, error);
        };

        const onClose = async () => {
            await this.errorHandler.handleConnectionClose(connection);
        };

        await this.fallbackManager.fallbackToSse(connection, configObj, onError, onClose);

        await connection.client!.connect(connection.transport!);

        connection.server.status = 'connected';
        this.errorHandler.clearErrors(connection);
        connection.server.prevConnected = true;

        this.retryManager.clearRetryState(name);
        this.onServerChange();

        await this.toolsManager.updateConnectionTools(connection);
        this.onServerChange();

        this.logger.info(`SSE fallback connection successful: ${name}`);
    }

    private async establishDirectConnection(connection: McpConnection, configObj: any): Promise<void> {
        if (configObj.transportType === 'stdio') {
            await connection.transport!.start();
            const stderrStream = (connection.transport as StdioClientTransport).stderr;
            if (stderrStream) {
                stderrStream.on('data', async (data: Buffer) => {
                    const output = data.toString();
                    const isInfoLog = !/\berror\b/i.test(output);

                    if (isInfoLog) {
                        this.logger.info(`Server "${connection.server.name}" info:`, output);
                    } else {
                        this.logger.error(`Server "${connection.server.name}" error:`, output);
                        const conn = this.getConnection(connection.server.name);
                        if (conn) {
                            this.errorHandler.getErrorLogger().appendErrorMessage(conn, output);
                            this.errorHandler.getErrorLogger().appendFullErrorMessage(conn, output);
                            this.onServerChange();
                        }
                    }
                });
            }
        }

        await connection.client!.connect(connection.transport!);

        connection.server.status = 'connected';
        this.errorHandler.clearErrors(connection);
        connection.server.prevConnected = true;

        this.retryManager.clearRetryState(connection.server.name);
        this.onServerChange();

        await this.toolsManager.updateConnectionTools(connection);
        this.onServerChange();
    }
}