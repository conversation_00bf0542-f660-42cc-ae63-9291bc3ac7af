import { getUserNodePath } from '../../utils';

/**
 * Connection-related utility functions
 */

/**
 * Get user Node path
 * @returns Node path
 */
export async function getNodePath(): Promise<string> {
    return await getUserNodePath();
}

/**
 * Process Node command path
 * @param command - Original command
 * @returns Processed command path
 */
export async function processNodeCommand(command: string): Promise<string> {
    if (command.trim() === 'node') {
        return await getNodePath();
    }
    return command;
}