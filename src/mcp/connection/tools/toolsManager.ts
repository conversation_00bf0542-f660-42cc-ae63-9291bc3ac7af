import { McpConnection, McpTool } from '../../types';
import { ListToolsResultSchema } from '@modelcontextprotocol/sdk/types.js';
import { MCPLogger } from '@/util/log';

/** Default timeout for MCP connection requests (milliseconds) */
const DEFAULT_REQUEST_TIMEOUT_MS = 5000;

/**
 * Tools manager
 * Responsible for managing MCP server tool lists
 */
export class ToolsManager {
    private logger: InstanceType<typeof MCPLogger>;

    constructor(logger: InstanceType<typeof MCPLogger>) {
        this.logger = logger;
    }

    /**
     * Get list of tools supported by MCP server
     * @param connection - MCP connection object
     * @returns Tool list, returns empty array if retrieval fails
     */
    public async fetchToolsList(connection: McpConnection): Promise<McpTool[]> {
        try {
            const response = await connection.client?.request(
                {
                    method: 'tools/list'
                },
                ListToolsResultSchema,
                {
                    timeout: DEFAULT_REQUEST_TIMEOUT_MS
                }
            );

            const tools = (response?.tools || []).map((tool) => ({
                ...tool,
                autoApprove: false
            }));

            return tools;
        } catch (error) {
            this.logger.error(`Failed to get tool list: ${connection.server.name}:`, error);
            return [];
        }
    }

    /**
     * Update connection tool list
     * @param connection - MCP connection object
     */
    public async updateConnectionTools(connection: McpConnection): Promise<void> {
        connection.server.tools = await this.fetchToolsList(connection);
    }
}