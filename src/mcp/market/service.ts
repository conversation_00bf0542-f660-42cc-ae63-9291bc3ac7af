import { ResponseBase, STATUS } from '@/protocol/index.d';
import { MarketMcpDetail } from "../types";
import {
  fetchAvailableMcpListByMarketApi,
  fetchMcpDetailByMarketApi,
} from './api';
import { MCPLogger } from '@/util/log';

const logger = new MCPLogger(`mcp-file-helper`);

/**
 * Get paginated list of available MCPs from market
 *
 * @param params - Query parameters, including page number, page size and search keyword
 * @returns Promise<ResponseBase<{ page, pageSize, total, records }>>
 *   - status: Operation status
 *   - code: Status code
 *   - message: Prompt message
 *   - data: Returned paginated data
 *
 * Exception handling:
 *   - If API returns code !== 0 or exception occurs, return FAILED status and error message
 */
export async function fetchAvailableMcpListByMarket(params: {
  page?: number;
  pageSize?: number;
  searchKeyword?: string;
}): Promise<ResponseBase<{
  page?: number;
  pageSize?: number;
  total?: number;
  records?: MarketMcpDetail[];
}>> {
  try {
    const data = await fetchAvailableMcpListByMarketApi(params);

    // code not 0 is considered failure
    if (data.code !== 0) {
      throw new Error(data.message || 'Market API error');
    }
    return {
      status: STATUS.OK,
      code: data.code,
      message: data.message,
      data: data.data
    };
  } catch (e: any) {
    logger.error('fetchAvailableMcpListByMarket error', e);
    // Catch exception, return FAILED status
    return {
      status: STATUS.FAILED,
      code: -1,
      message: e?.message || 'Unknown error',
      data: undefined
    };
  }
}

/**
 * Get MCP details for specified serverId
 *
 * @param params - Object containing serverId
 * @returns Promise<ResponseBase<MarketMcpDetail>>
 *   - status: Operation status
 *   - code: Status code
 *   - message: Prompt message
 *   - data: MCP details data
 *
 * Exception handling:
 *   - If API returns code !== 0 or exception occurs, return FAILED status and error message
 */
export async function fetchMcpDetailByMarket(params: { serverId: string; }): Promise<ResponseBase<MarketMcpDetail>> {
  try {
    const data = await fetchMcpDetailByMarketApi(params);
    // code not 0 is considered failure
    if (data.code !== 0) {
      throw new Error(data.message || 'Market API error');
    }
    return {
      status: STATUS.OK,
      code: 0,
      message: data.message,
      data: data.data
    };
  } catch (e: any) {
    logger.error('fetchMcpDetailByMarket error', e);
    // Catch exception, return FAILED status
    return {
      status: STATUS.FAILED,
      code: -1,
      message: e?.message || 'Unknown error',
      data: undefined
    };
  }
}
