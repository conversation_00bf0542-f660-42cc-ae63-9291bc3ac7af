import { z } from 'zod';
import { CallToolResultSchema, CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import {
  DEFAULT_MCP_TIMEOUT_SECONDS,
  McpServerSimpleConfig,
  McpSettingsSimpleSchema,
  ServerConfigSchema
} from './settingSchema';
import { IMcpClient, InstallMcpParams, MarketMcpDetail, McpServer, McpServerChangeEventDetail, McpToolCallResponse, McpMode } from './types';
import { ResponseBase, STATUS } from '@/protocol/index.d';
import { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { IMessenger } from '@/protocol/messenger';
import {
  checkMcpSettingsFile,
  writeSettingsFile,
  readSettingsFile,
  writeConfigFile,
  readConfigFile,
  watchFile
} from './fileHelper';
import { mergeMcpConfigAndSettings, readAndValidateMcpSettingsFile, readMcpConfigFile } from './parserHelper';
import { secondsToMs } from './utils';
import { getMcpSettingsPath } from '@/util/paths';
import { McpConnectionManager } from './connection';
import { MCPLogger } from '@/util/log';
import { safeJsonStringify } from './parserHelper/jsonParserHelper';
import { throttle } from 'lodash-es';
import { fetchAvailableMcpListByMarket, fetchMcpDetailByMarket } from './market/index';
import { GlobalConfig } from '@/util/global';
import { NotificationAggregator, ConfigChangeNotification, NotificationMessenger } from './notification-aggregator';

const DEFAULT_MCP_MODE = 'auto';

/** Global MCP client instance */
let mcpClientInstance: IMcpClient;

/**
 * MCP client implementation class
 * Provides MCP server management and tool calling functionality
 */
export class McpClient implements IMcpClient {
  /** Whether connection operations are in progress */
  private isConnecting: boolean = false;

  /** Message communication instance */
  private messenger?: IMessenger<ToCoreProtocol, FromCoreProtocol>;

  /** Configuration file watcher */
  private mcpSettingsWatcher?: () => void;

  /** MCP client version number */
  public clientVersion = '1.0.0';

  /** Whether configuration file parsing has errors */
  public parseError = false;

  /** Configuration file parsing error message */
  public parseErrorMessage = '';

  /** Previous server change event details */
  private prevNotifyServerChangeDetailJSON?: string;

  /** Previous written configuration file content */
  private prevConfigFile = '';

  /** MCP connection manager instance */
  private connectionManager: McpConnectionManager;

  /** MCP operation mode, default uses constant configuration */
  // private mcpMode: McpMode = DEFAULT_MCP_MODE;

  private logger = new MCPLogger(`mcp-${this.clientVersion}`);

  /** 通知聚合器实例 */
  private notificationAggregator: NotificationAggregator;

  /**
   * Get the global singleton instance of McpClient
   * Ensures there is only one MCP client instance in the entire application
   *
   * @returns Global unique MCP client instance
   */
  static getInstance(): IMcpClient {
    if (!mcpClientInstance) {
      mcpClientInstance = new McpClient();
    }
    return mcpClientInstance;
  }


  /**
   * 如果mcp服务中有tool.name重复，则在tool.name前加上server.name
   * server.name_tool.name
   */
  private transformMcpServers(servers: McpServer[]): McpServer[] {
    // 收集所有工具名称，检测冲突
    const toolNameCounts = new Map<string, number>();

    // 第一遍遍历：统计工具名称出现次数
    servers.forEach(server => {
      server.tools?.forEach(tool => {
        const count = toolNameCounts.get(tool.name) || 0;
        toolNameCounts.set(tool.name, count + 1);
      });
    });


    servers.forEach(server => {
      server.tools?.forEach(tool => {
        const repeatTool = (toolNameCounts.get(tool.name) || 0) > 1
        if (repeatTool) {
          tool.name = `${server.name}_${tool.name}`
        }
      })
    })
    return servers
  }

  /**
   * Private constructor
   * Initializes MCP client, including:
   * - Creating connection manager
   * - Setting up configuration file monitoring
   * - Initializing server connections
   */
  private constructor() {
    this.connectionManager = new McpConnectionManager(this.clientVersion, () => this.notifyServerChange());
    this.notificationAggregator = new NotificationAggregator(100, this.logger);
    this.watchMcpSettingsFile();
    this.initializeMcpServers();
  }

  /**
   * Release client resources
   * Clean up all connections, listeners and other resources
   */
  public async dispose(): Promise<void> {
    this.removeAllFileWatchers();
    this.notificationAggregator.destroy();
  }

  /**
   * Set message communication instance
   * Used for communication with core process
   *
   * @param messenger - Message communication instance
   */
  public setMessenger(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    this.messenger = messenger;
    // 设置通知聚合器的消息发送器
    this.notificationAggregator.setMessenger({
      send: async (channel: string, notification: ConfigChangeNotification) => {
        if (!this.messenger) {
          throw new Error('Messenger not available');
        }
        await this.messenger.send(channel as 'mcp/configChangeNotification', notification);
      }
    });
  }

  private throttledSendSingleChangeMessage = (() => {
    return throttle(
      async (message) => {
        await this.messenger?.send('mcp/mcpServerChange', message);
      },
      50,
      {
        leading: true,
        trailing: true
      }
    );
  })();

  /**
   * Notify server status changes
   * Send server status change events to core process
   * Including server list, error status and other information
   */
  private async notifyServerChange(): Promise<void> {
    if (!this.messenger) {
      this.logger.error('mcp/mcpServerChange notification failed, messenger not set');
      return;
    }

    let mcpServers = await this.getOrderMcpServers();

    const currentChangeEventDetail: McpServerChangeEventDetail = {
      code: 0,
      isError: this.parseError,
      message: this.parseErrorMessage || '',
      mcpServers: this.parseError ? [] : mcpServers
    };

    const currentChangeEventDetailJSON = safeJsonStringify(currentChangeEventDetail);

    if (currentChangeEventDetailJSON === this.prevNotifyServerChangeDetailJSON) {
      this.logger.debug('notifyServerChange identical');
      return;
    }

    this.throttledSendSingleChangeMessage(currentChangeEventDetail);

    this.logger.info('mcp/mcpServerChange');

    this.prevNotifyServerChangeDetailJSON = currentChangeEventDetailJSON;
  }

  /**
   * Send configuration change notification
   * Send MCP configuration change notification events to core process
   * 
   * @param notification - Notification details
   */


  /**
   * Write MCP configuration file
   * Only write when configuration content changes
   *
   * @param config - Configuration content to write
   */
  private async toWriteMcpConfigFile(config: z.infer<typeof McpSettingsSimpleSchema>) {
    try {
      let configContent = JSON.stringify(config, null, 2);

      if (configContent !== this.prevConfigFile) {
        await writeConfigFile(configContent);
        this.prevConfigFile = configContent;
      }
    } catch (err) {
      this.logger.error(`Failed to write MCP configuration file: ${err}`);
    }
  }

  /**
   * Get the full path of MCP settings file
   *
   * @returns Response object containing settings file path
   * @throws When unable to get or access settings file path
   */
  public async getSettingsPath(): Promise<ResponseBase<string>> {
    try {
      const settingsPath = await checkMcpSettingsFile();
      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: settingsPath
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: STATUS.FAILED,
        message: 'Failed to get mcp settings file path',
        data: ''
      };
    }
  }

  /**
   * Toggle enable/disable status of specified MCP server
   *
   * @param params.serverName - Target server name
   * @param params.disabled - Whether to disable
   * @returns Operation result
   * @throws When server doesn't exist or status toggle fails
   */
  public async toggleMcpServer(params: { serverName: string; disabled: boolean }): Promise<ResponseBase<boolean>> {
    try {
      await this.toggleConnectionDisabled(params.serverName, params.disabled);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`Failed to toggle MCP Server ${params.serverName} status`, error);
      return {
        status: STATUS.FAILED,
        message: 'Failed to toggle MCP Server status',
        data: false
      };
    }
  }

  /**
   * Restart specified MCP server
   *
   * @param params.serverName - Server name to restart
   * @returns Restart operation result
   * @throws When server doesn't exist or restart operation fails
   */
  public async restartMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>> {
    try {
      await this.restartConnection(params.serverName);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`Failed to restart MCP Server ${params.serverName}`, error);
      return {
        status: STATUS.FAILED,
        message: 'Failed to restart MCP Server',
        data: false
      };
    }
  }

  /**
   * Delete specified MCP server
   *
   * @param params.serverName - Server name to delete
   * @returns Delete operation result
   * @throws When server doesn't exist or delete operation fails
   */
  public async deleteMcpServer(params: { serverName: string }): Promise<ResponseBase<boolean>> {
    this.logger.debug(`Deleting MCP Server ${params.serverName}...`);

    try {
      await this.deleteServer(params.serverName);

      this.logger.debug(`Successfully deleted MCP Server ${params.serverName}`);

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error(`Failed to delete MCP Server ${params.serverName}`, error);
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'Failed to delete MCP Server',
        data: false
      };
    }
  }

  async installMcp(params: InstallMcpParams): Promise<ResponseBase<boolean>> {
    try {
      this.logger.info('call installMcp', params);
      // 1. Batch validate all mcpServers
      for (const [name, config] of Object.entries(params.mcpServers)) {
        const result = ServerConfigSchema.safeParse(config);
        if (!result.success) {
          this.logger.error(`Failed to install MCP ${name}`, config, result.error);
          return {
            status: STATUS.FAILED,
            code: -1,
            message: `MCP field invalid: ${name}`,
            data: false
          };
        }
      }

      // 2. Read original settings
      const content = await readSettingsFile();
      const setting = JSON.parse(content || '{}');
      const oldServers = setting.mcpServers || {};

      // 3. Merge new and old mcpServers (new takes priority)
      const mergedServers: Record<string, any> = {};
      // Insert new servers first, maintaining their order
      for (const key of Object.keys(params.mcpServers)) {
        const serverConfig = { ...params.mcpServers[key] };
        // In manual mode, newly installed MCPs are disabled by default
        // if (this.mcpMode === 'manual') {
        //   // Check if force disable is needed (originally not disabled but force disabled)
        //   const wasForceDisabled = !serverConfig.disabled;
        //   serverConfig.disabled = true;

        //   // Only send notification when force disabled (originally enabled but force disabled)
        //   if (wasForceDisabled) {
        //     this.sendConfigChangeNotification({
        //       type: 'manual_mode_force_disabled',
        //       serverName: key,
        //       message: `In manual mode, newly installed MCP server "${key}" has been force disabled and needs manual activation`,
        //       action: 'add'
        //     });
        //   }
        // }
        mergedServers[key] = serverConfig;
      }
      // Then insert old servers that weren't overwritten
      for (const key of Object.keys(oldServers)) {
        if (!(key in params.mcpServers)) {
          mergedServers[key] = oldServers[key];
        }
      }
      setting.mcpServers = mergedServers;

      // 4. Write to file
      await writeSettingsFile(JSON.stringify(setting, null, 2));

      // 5. Initialize
      await this.initializeMcpServers();

      // 6. Notify immediately
      this.notifyServerChange();

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: true
      };
    } catch (error) {
      this.logger.error('installMcp failed', error);
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'installMcp failed',
        data: false
      };
    }
  }

  async fetchAvailableMcpListByMarket(params: { page?: number; pageSize?: number; searchKeyword?: string; }): Promise<ResponseBase<{ page?: number; pageSize?: number; total?: number; records?: MarketMcpDetail[]; }>> {
    this.logger.info('call fetchAvailableMcpListByMarket', params);
    // 1. Batch validate all mcpServers
    return await fetchAvailableMcpListByMarket(params);
  }

  async fetchMcpDetailByMarket(params: { serverId: string; }): Promise<ResponseBase<MarketMcpDetail>> {
    this.logger.info('call fetchMcpDetailByMarket', params);

    if (!params || !params.serverId) {
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'serverId cannot be empty',
        data: undefined
      };
    }

    return await fetchMcpDetailByMarket(params)
  }

  /**
   * Get all opened and connected server list
   *
   * @returns Connected server list
   */
  public getOpenedAndConnectedServers(): McpServer[] {
    // Check MCP status
    // this.checkMcpServers();
    const openedAndConnectedServers = this.connectionManager
      .getConnections()
      .filter((conn) => !conn.server.disabled)
      .filter((conn) => conn.server.status === 'connected')
      .map((conn) => conn.server)

    this.logger.debug(`openedAndConnectedServers: ${JSON.stringify(openedAndConnectedServers)}`)

    /** mcp 工具名称可能重复,因此这里给tool.name前面拼接上 server.name */
    const transFormOpenedAndConnectedServers = this.transformMcpServers(openedAndConnectedServers)

    this.logger.debug(`transFormOpenedAndConnectedServers: ${JSON.stringify(transFormOpenedAndConnectedServers)}`)
    return transFormOpenedAndConnectedServers;
  }

  /**
   * Get current MCP operation mode
   * @returns Current MCP operation mode
   */
  public get mcpMode(): McpMode {
    // 暂时不开放功能
    return DEFAULT_MCP_MODE

    // const ideDevice = GlobalConfig.getConfig().getDevice();

    // if (ideDevice === 'kwaipilot-vscode' || ideDevice === 'kwaipilot-ide') {
    //   return 'manual';
    // }

    // return DEFAULT_MCP_MODE;
  }

  /**
   * Call tools on specified server
   *
   * @param serverName - Target server name
   * @param toolName - Tool name to call
   * @param toolArguments - Tool call arguments
   * @returns Tool execution result
   * @throws When tool call fails
   */
  async callTool(
    serverName: string,
    toolName: string,
    toolArguments?: Record<string, unknown>
  ): Promise<McpToolCallResponse> {
    const connection = this.connectionManager.getConnection(serverName);
    if (!connection || !connection.client) {
      throw new Error(`${serverName} not connected`);
    }

    if (connection.server.disabled) {
      throw new Error(`${serverName} is disabled`);
    }

    const timeout = secondsToMs(DEFAULT_MCP_TIMEOUT_SECONDS);

    const response: CallToolResult = await connection.client.request(
      {
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: toolArguments
        }
      },
      CallToolResultSchema,
      {
        timeout
      }
    );

    return response;
  }

  /**
   * Get server list
   * Optionally filter by status
   *
   * @param filterStatus - Optional server status filter condition
   * @returns Response object containing server list and error status
   */
  public async getDisplayServers(
    filterStatus?: McpServer['status']
  ): Promise<ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>> {
    try {
      if (this.parseError) {
        return {
          status: STATUS.FAILED,
          code: -1,
          message: this.parseErrorMessage || '',
          data: {
            isError: this.parseError,
            mcpServers: []
          }
        };
      }

      const displayServers = await this.getOrderMcpServers();

      return {
        status: STATUS.OK,
        code: 0,
        message: 'success',
        data: {
          mcpServers: displayServers,
          isError: false
        }
      };
    } catch (err) {
      return {
        status: STATUS.FAILED,
        code: -1,
        message: 'Failed to get MCP service list',
        data: {
          mcpServers: [],
          isError: true
        }
      };
    }
  }

  /**
   * Get server list sorted by order in configuration file
   *
   * @returns Sorted server list
   */
  private async getOrderMcpServers(): Promise<McpServer[]> {
    try {
      const config = await this.readAndValidateMcpConfigFile();
      const serverOrder = Object.keys(config?.mcpServers || {});
      return this.connectionManager
        .getConnections()
        .sort((a, b) => {
          const indexA = serverOrder.indexOf(a.server.name);
          const indexB = serverOrder.indexOf(b.server.name);
          return indexA - indexB;
        })
        .map((connection) => connection.server);
    } catch (error) {
      return [];
    }
  }

  /**
   * Read and validate MCP configuration file
   * Merge content from settings file and configuration file
   *
   * @returns Merged configuration object
   */
  private async readAndValidateMcpConfigFile(): Promise<z.infer<typeof McpSettingsSimpleSchema> | undefined> {
    try {
      const { isError, message, content } = await readAndValidateMcpSettingsFile();

      this.parseError = isError;
      this.parseErrorMessage = message || '';

      const config = await readMcpConfigFile();

      let mergedConfig = content;
      if (content) {
        mergedConfig = mergeMcpConfigAndSettings(content, config);
      }

      // Update MCP mode, if not specified in configuration file, use default mode
      // this.mcpMode = mergedConfig?.mode || DEFAULT_MCP_MODE;

      this.logger.debug('mergedConfig', mergedConfig);
      this.logger.debug('mcpMode', this.mcpMode);
      return mergedConfig;
    } catch (error) {
      this.logger.error('readAndValidateMcpConfigFile', error);
      this.parseError = true;
      this.parseErrorMessage = 'Error occurred while reading or validating MCP settings file';
      return undefined;
    }
  }

  /**
   * Remove all file watchers
   */
  private async removeAllFileWatchers(): Promise<void> {
    this.mcpSettingsWatcher?.();
  }

  /**
   * Monitor MCP settings file changes
   * Automatically reload server connections when configuration file changes
   */
  private async watchMcpSettingsFile(): Promise<void> {
    this.mcpSettingsWatcher = watchFile(getMcpSettingsPath(), async () => {
      this.logger.info('settings file changed');
      const settings = await this.readAndValidateMcpConfigFile();

      if (settings) {
        await this.updateServerConnections(settings.mcpServers);
      } else {
        this.logger.error('MCP configuration file read failed');
      }

      this.notifyServerChange();
    });
  }

  /**
   * Delete specified MCP server configuration
   * Remove server from configuration file and clean up related resources
   *
   * Delete process:
   * 1. Read current configuration file
   * 2. Remove specified server from configuration
   * 3. Delete server connection
   * 4. Save updated configuration
   * 5. Notify server changes
   *
   * @param serverName - Server name to delete
   * @throws When deleting server configuration fails
   */
  private async deleteServer(serverName: string): Promise<void> {
    try {
      const content = await readSettingsFile();

      const setting = JSON.parse(content || '{}');

      if (setting.mcpServers[serverName]) {
        delete setting.mcpServers[serverName];

        await writeSettingsFile(JSON.stringify(setting, null, 2));

        await this.deleteServerConfig(serverName);

        this.notifyServerChange();

        const config = await this.readAndValidateMcpConfigFile();

        await this.updateServerConnections(config?.mcpServers || {});
      }
    } catch (error) {
      this.logger.error(`Failed to delete MCP Server ${serverName}`, error);
      throw error;
    }
  }

  /**
   * Delete server configuration
   * Remove server configuration information from configuration file
   *
   * @param serverName - Server name to delete configuration for
   */
  private async deleteServerConfig(serverName: string) {
    try {
      const configContent = await readConfigFile();
      const config = JSON.parse(configContent || '{}');

      if (config.mcpServers[serverName]) {
        delete config.mcpServers[serverName];
        await this.toWriteMcpConfigFile(config);
      }
    } catch (error) {
      this.logger.error(`Failed to delete MCP Server ${serverName} configuration`, error);
    }
  }

  /**
   * Initialize all MCP server connections
   * Read configuration and establish connections with various servers
   */
  private async initializeMcpServers(): Promise<void> {
    try {
      if (await checkMcpSettingsFile()) {
        const settings = await this.readAndValidateMcpConfigFile();

        if (!settings) {
          return;
        }
        await this.updateServerConnections(settings.mcpServers);
      }
    } catch (error) {
      this.logger.error('Failed to initialize MCP Server', error);
    }
  }

  /**
   * Update server connections
   * Update existing connections based on new configuration:
   * 1. Delete removed server connections
   * 2. Create all new connection instances
   * 3. Establish actual connections in sequence
   *
   * @param newServers - New server configuration
   */
  private async updateServerConnections(newServers: Record<string, McpServerSimpleConfig>): Promise<void> {
    this.isConnecting = true;

    try {
      const currentNames = this.connectionManager.getConnections().map((conn) => conn.server.name);
      const newNames = new Set(Object.keys(newServers));

      // Delete removed servers
      for (const name of currentNames) {
        if (!newNames.has(name)) {
          await this.connectionManager.removeConnection(name);
          await this.deleteServerConfig(name);
          this.logger.debug(`Deleted mcp server ${name} connection`);
        }
      }

      const needRestart = new Set<string>();

      // In manual mode, need to compare differences between setting and config files
      if (this.mcpMode === 'manual') {
        await this.handleManualModeServerUpdates(newServers, needRestart);
      } else {
        // Auto mode: directly handle all servers
        await this.handleAutoModeServerUpdates(newServers, needRestart);
      }

      // Establish actual connections in sequence
      for (const connection of this.connectionManager.getConnections()) {
        if (!needRestart.has(connection.server.name)) {
          continue;
        }

        if (!connection.server.disabled) {
          try {
            await this.connectionManager.establishConnection(connection);
            this.logger.debug(`Established MCP Server ${connection.server.name} actual connection`);
          } catch (err) {
            this.logger.error(`Failed to connect MCP Server ${connection.server.name}`, err);
          } finally {
            needRestart.delete(connection.server.name);
          }
        }
      }
    } finally {
      this.isConnecting = false;
    }
  }

  /**
   * Handle server updates in manual mode
   * Compare differences between setting and config files to decide processing logic
   */
  private async handleManualModeServerUpdates(newServers: Record<string, McpServerSimpleConfig>, needRestart: Set<string>): Promise<void> {
    // Read setting and config files
    const settingContent = await readSettingsFile();
    const configContent = await readConfigFile();

    const settingData = settingContent ? JSON.parse(settingContent) : { mcpServers: {} };
    const configData = configContent ? JSON.parse(configContent) : { mcpServers: {} };

    for (const [name, config] of Object.entries(newServers)) {
      const currentConnection = this.connectionManager.getConnection(name);
      const settingServer = settingData.mcpServers?.[name];
      const configServer = configData.mcpServers?.[name];

      // Determine if newly added: exists in setting file but not in config file
      const isNewlyAdded = settingServer && !configServer;

      // Determine if configuration changed: configuration in setting and config files inconsistent (excluding disabled field)
      const isConfigChanged = settingServer && configServer && (() => {
        // Create copies of setting and config, exclude disabled field for comparison
        const { disabled: settingDisabled, ...settingWithoutDisabled } = settingServer;
        const { disabled: configDisabled, ...configWithoutDisabled } = configServer;
        return JSON.stringify(settingWithoutDisabled) !== JSON.stringify(configWithoutDisabled);
      })();

      if (isNewlyAdded) {
        // New server: force disable and notify user
        if (currentConnection) {
          await this.connectionManager.removeConnection(name);
        }

        const newConnection = await this.connectionManager.createConnection(name, { ...config, disabled: true });
        newConnection.server.needsManualActivation = true;
        newConnection.server.changeReason = 'newly_added';
        needRestart.add(name);
        this.logger.debug(`Manual mode: newly added mcp server ${name}, requires manual activation`);

        // Write current configuration to config file to avoid repeated detection of new additions
        configData.mcpServers[name] = { ...settingServer, disabled: true };
        await this.toWriteMcpConfigFile(configData);

        // Collect notifications instead of sending immediately
        this.notificationAggregator.addNotification({
          type: 'manual_mode_force_disabled',
          serverName: name,
          message: `In manual mode, newly added MCP server "${name}" has been force disabled and requires manual activation`,
          action: 'add'
        });
      } else if (isConfigChanged) {
        // Configuration change: force disable and notify user
        if (currentConnection) {
          await this.connectionManager.removeConnection(name);
        }

        const newConnection = await this.connectionManager.createConnection(name, { ...config, disabled: true });
        newConnection.server.needsManualActivation = true;
        newConnection.server.changeReason = 'config_modified';
        needRestart.add(name);
        this.logger.debug(`Manual mode: configuration modified, disabled mcp server ${name}`);

        // Write current configuration to config file to avoid repeated detection of configuration changes
        configData.mcpServers[name] = { ...settingServer, disabled: true };
        await this.toWriteMcpConfigFile(configData);

        // Collect notifications instead of sending immediately
        this.notificationAggregator.addNotification({
          type: 'manual_mode_force_disabled',
          serverName: name,
          message: `In manual mode, modified configuration MCP server "${name}" has been force disabled and requires manual activation`,
          action: 'modify'
        });
      } else {
        // Configuration unchanged, maintain current status
        if (!currentConnection) {
          await this.connectionManager.createConnection(name, config);
          needRestart.add(name);
        }
      }
    }

    // 强制刷新通知，确保及时发送
    await this.notificationAggregator.flushNotifications();
  }

  /**
   * Handle server updates in auto mode
   */
  private async handleAutoModeServerUpdates(newServers: Record<string, McpServerSimpleConfig>, needRestart: Set<string>): Promise<void> {
    for (const [name, config] of Object.entries(newServers)) {
      const currentConnection = this.connectionManager.getConnection(name);

      // Is it newly added
      if (!currentConnection) {
        await this.connectionManager.createConnection(name, config);
        needRestart.add(name);
        continue;
      }

      // Is it an update
      let isConfigChanged = false;
      try {
        const newData = ServerConfigSchema.safeParse(config).data;
        if (currentConnection.server.config !== JSON.stringify(newData)) {
          isConfigChanged = true;
        }
      } catch (error) {
        isConfigChanged = true;
      }

      if (isConfigChanged) {
        await this.connectionManager.removeConnection(name);
        await this.connectionManager.createConnection(name, config);
        this.logger.debug(`Updated mcp server ${name} connection instance`);
        needRestart.add(name);
      }
    }
  }

  /**
   * Re-establish connection for specified server
   * Disconnect existing connection and establish new connection
   *
   * @param serverName - Server name
   * @throws When reconnection fails
   */
  public async restartConnection(serverName: string): Promise<void> {
    this.isConnecting = true;

    const connection = this.connectionManager.getConnection(serverName);
    const config = connection?.server.config;

    if (config) {
      if (connection?.server.disabled) {
        return await this.toggleConnectionDisabled(serverName, false);
      }

      connection.server.status = 'connecting';
      connection.server.error = '';
      this.notifyServerChange();

      try {
        this.logger.debug(`Restarting MCP Server ${serverName}`);

        await this.connectionManager.removeConnection(serverName);
        const newConnection = await this.connectionManager.createConnection(serverName, JSON.parse(config));
        await this.connectionManager.establishConnection(newConnection);
      } catch (error) {
        this.logger.error(`Failed to restart connection: ${serverName}:`, error);
      }
    }
    this.isConnecting = false;
    this.notifyServerChange();
  }

  /**
   * Toggle enable/disable status of specified server
   * Update configuration file and handle connection status through connection manager
   *
   * @param serverName - Server name
   * @param disabled - Whether to disable
   * @throws When toggling status fails
   */
  private async toggleConnectionDisabled(serverName: string, disabled: boolean): Promise<void> {
    try {
      const config = await this.readAndValidateMcpConfigFile();
      if (!config) {
        throw new Error('Failed to read or validate MCP settings');
      }

      if (config.mcpServers[serverName]) {
        // Update configuration file
        config.mcpServers[serverName].disabled = disabled;
        await this.toWriteMcpConfigFile(config);

        // Toggle connection status
        await this.connectionManager.toggleConnectionState(serverName, disabled);

        // If it's an enable operation, clear manual activation flag
        if (!disabled) {
          const connection = this.connectionManager.getConnection(serverName);
          if (connection) {
            connection.server.needsManualActivation = false;
            connection.server.changeReason = undefined;
          }
        }

        this.logger.debug(`Toggled MCP Server ${serverName} disabled status to ${disabled}`);
        return;
      }

      this.logger.error(`MCP configuration file does not contain ${serverName}`);
      throw new Error(`MCP configuration file does not contain ${serverName}`);
    } catch (error) {
      this.logger.error(`Failed to toggle connection status: ${serverName}:`, error);
      throw error;
    }
  }
}

export default McpClient;
