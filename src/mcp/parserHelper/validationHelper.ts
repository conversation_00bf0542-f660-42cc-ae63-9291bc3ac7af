import { z } from 'zod';
import { ERROR_MESSAGES } from '../messages';

/**
 * Validation error class
 * Used to represent errors that occur during configuration validation
 */
export class ValidationError extends Error {
  /**
   * Create validation error instance
   * @param {string} message - Error message
   * @param {ValidationErrorDetail[]} details - Error details list
   * @param {unknown} [source] - Error source
   */
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Format validation error
 * Convert Zod validation error to more user-friendly error message
 *
 * @param {z.ZodError} error - Zod error object
 * @param {string} context - Context where error occurred
 * @returns {ValidationError} Formatted validation error
 */
export function formatValidationError(error: z.ZodError, context: string): ValidationError {
  // Extract specific error information
  const errorDetails = error.errors.map(err => {
    const path = err.path.join('.');
    const message = err.message;
    return `${path}: ${message}`;
  }).join('; ');

  // If there's specific error information, use it; otherwise use generic error message
  const detailedMessage = errorDetails || ERROR_MESSAGES.formatError;

  return new ValidationError(`${context} - ${detailedMessage}`);
}
