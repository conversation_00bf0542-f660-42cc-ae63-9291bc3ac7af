import { z } from 'zod';
import { McpSettingsSchema, McpSettingsSimpleSchema, ServerConfigSchema } from '../settingSchema';
import { readSettingsFile, readConfigFile, existsSettingsFile, mcpSettingDefaultContent } from '../fileHelper';
import { formatJsonError, JsonParseError, safeJsonParse } from './jsonParserHelper';
import { ValidationError } from './validationHelper';
import { ERROR_MESSAGES } from '../messages';

/**
 * MCP settings validation result interface
 */
export interface McpSettingsValidationResult {
  isError: boolean;
  message: string;
  content: z.infer<typeof McpSettingsSimpleSchema> | undefined;
}

/**
 * Read and validate MCP settings file
 * @returns Promise<McpSettingsValidationResult>
 */
export async function readAndValidateMcpSettingsFile(): Promise<McpSettingsValidationResult> {
  try {
    let settingContent = await readSettingsFile();

    // If file doesn't exist, use default content
    if (!await existsSettingsFile()) {
      settingContent = mcpSettingDefaultContent;
    }

    if (!settingContent) {
      return {
        isError: true,
        message: ERROR_MESSAGES.missMcpServersError,
        content: undefined
      };
    }

    const config = safeJsonParse(settingContent);

    if (!config.success) {
      throw formatJsonError(config.error, settingContent, 'MCP settings file');
    }

    const content = McpSettingsSimpleSchema.safeParse(config.data);

    if (!content.success) {
      return {
        isError: true,
        message: ERROR_MESSAGES.missMcpServersError,
        content: undefined
      };
    }

    // Filter invalid items
    Object.keys(content.data.mcpServers).forEach(key => {
      if (key.trim() === '') {
        delete content.data.mcpServers[key];
        return;
      }

      const result = ServerConfigSchema.safeParse(content.data.mcpServers[key]);
      if (!result.success) {
        delete content.data.mcpServers[key];
      }
    });

    return {
      isError: false,
      message: '',
      content: content.data
    };
  } catch (error) {
    if (error instanceof ValidationError || error instanceof JsonParseError) {
      return {
        isError: true,
        message: error.message,
        content: undefined
      };
    }

    return {
      isError: true,
      message: ERROR_MESSAGES.readSettingsFileUnknownError,
      content: undefined
    };
  }
}

/**
 * Read MCP configuration file
 * @returns Promise<z.infer<typeof McpSettingsSchema>>
 */
export async function readMcpConfigFile(): Promise<z.infer<typeof McpSettingsSchema>> {
  try {
    const content = await readConfigFile();
    if (!content) {
      return { mcpServers: {}, mode: 'auto' };
    }

    const config = JSON.parse(content);

    if (!config.mcpServers) {
      return { mcpServers: {}, mode: config.mode || 'auto' };
    }

    return { mcpServers: config.mcpServers, mode: config.mode || 'auto' };
  } catch (error) {
    return { mcpServers: {}, mode: 'auto' };
  }
}

/**
 * Merge MCP configuration and settings
 */
export function mergeMcpConfigAndSettings(
  settings: z.infer<typeof McpSettingsSimpleSchema>,
  config: z.infer<typeof McpSettingsSchema>
): z.infer<typeof McpSettingsSimpleSchema> {
  const mergedSettings = { ...settings };

  if (!settings.mcpServers || !config.mcpServers) {
    return settings;
  }

  for (const serverName in settings.mcpServers) {
    const configServer = config.mcpServers[serverName];
    const settingsServer = settings.mcpServers[serverName];
    if (settingsServer) {
      // Copy settingsServer, remove disabled and timeout fields
      const { disabled: _sDisabled, timeout: _sTimeout, ...restSettings } = settingsServer;
      let mergedServer: Record<string, any> = { ...restSettings };

      if (configServer) {
        // Only add if configServer exists
        if ('disabled' in configServer && typeof configServer.disabled !== 'undefined') {
          mergedServer.disabled = configServer.disabled;
        } else {
          // Default to enabled status
          mergedServer.disabled = false;
        }
        if ('timeout' in configServer && typeof configServer.timeout !== 'undefined') {
          mergedServer.timeout = configServer.timeout;
        }
      }
      mergedSettings.mcpServers[serverName] = mergedServer;
    }
  }

  return mergedSettings;
}
