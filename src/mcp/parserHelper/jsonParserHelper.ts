/**
 * JSON parsing result interface
 * @template T - Parsed data type
 * @property {boolean} success - Whether parsing was successful
 * @property {T} [data] - Data when parsing succeeds
 * @property {unknown} [error] - Error information when parsing fails
 */
export interface JsonParseResult<T> {
  success: boolean;
  data?: T;
  error?: unknown;
}

/**
 * JSON parsing error class
 * Used to represent errors that occur during JSON parsing, containing detailed error location information
 */
export class JsonParseError extends Error {
  /**
   * Create JSON parsing error instance
   * @param {string} message - Error message
   * @param {string} source - Error source (usually filename or context description)
   * @param {number} [position] - Character position where error occurred
   * @param {number} [lineNumber] - Line number where error occurred
   * @param {number} [columnNumber] - Column number where error occurred
   */
  constructor(
    message: string,
    public readonly source: string,
    public readonly position?: number,
    public readonly lineNumber?: number,
    public readonly columnNumber?: number
  ) {
    super(message);
    this.name = 'JsonParseError';
  }
}

/**
 * Safe JSON parsing function
 * Parse string to JSON object and handle potential errors
 *
 * @template T - Expected parsed data type
 * @param {string} text - JSON string to parse
 * @param {string} context - Parsing context (for error messages)
 * @returns {JsonParseResult<T>} Parsing result
 */
export function safeJsonParse<T>(text: string): JsonParseResult<T> {
  try {
    const data = JSON.parse(text) as T;
    return {
      success: true,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: error
    };
  }
}

export function safeJsonStringify<T>(data: T): string {
  try {
    return JSON.stringify(data);
  } catch (error) {
    return '';
  }
}

/**
 * Format JSON error message
 * Convert original error object to more user-friendly error message, containing error location and context
 *
 * @param {unknown} error - Original error object
 * @param {string} text - Original JSON text that caused the error
 * @param {string} context - Context where error occurred
 * @returns {JsonParseError} Formatted error object
 */
export function formatJsonError(error: unknown, text: string, context: string): JsonParseError {
  // Extract error message
  const errorMessage = error instanceof Error ? error.message : String(error);
  let position: number | undefined;
  let lineNumber: number | undefined;
  let columnNumber: number | undefined;

  // Extract position information from error message
  const positionMatch = errorMessage.match(/position (\d+)/);
  if (positionMatch) {
    position = parseInt(positionMatch[1], 10);
    // Calculate line number and column number
    const lines = text.slice(0, position).split('\n');
    lineNumber = lines.length;
    columnNumber = lines[lines.length - 1].length + 1;
  }

  const detailedMessage = `MCP configuration file syntax error (line ${lineNumber} column ${columnNumber})`

  return new JsonParseError(detailedMessage, context, position, lineNumber, columnNumber);
}
