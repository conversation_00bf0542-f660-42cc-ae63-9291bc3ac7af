import { z } from 'zod';

/**
 * MCP服务器默认超时时间（秒）
 * 与Anthropic MCP SDK的默认超时时间保持一致
 */
export const DEFAULT_MCP_TIMEOUT_SECONDS = 60;

/**
 * MCP服务器最小超时时间（秒）
 * 防止设置过短的超时时间导致连接问题
 */
export const MIN_MCP_TIMEOUT_SECONDS = 1;

/**
 * MCP服务器基础配置Schema
 * 定义了所有类型服务器共有的基础配置项
 */
export const BaseConfigSchema = z.object({
  /** 是否禁用该服务器 */
  disabled: z.boolean().optional(),
  /**
   * 服务器超时时间配置
   * 必须大于等于最小超时时间
   * 默认使用DEFAULT_MCP_TIMEOUT_SECONDS
   */
  timeout: z.number().min(MIN_MCP_TIMEOUT_SECONDS).optional().default(DEFAULT_MCP_TIMEOUT_SECONDS)
});

/**
 * 服务器配置Schema
 * 支持三种传输类型：stdio、sse、streamableHttp
 * transportType 为可选字段，如果未指定且有 url，默认为 streamableHttp
 */
export const ServerConfigSchema = BaseConfigSchema.extend({
  /** 传输类型标识（可选） */
  transportType: z.enum(['stdio', 'sse', 'streamableHttp']).optional(),

  /** 传输类型标识（可选） */
  type: z.string().optional(),

  /** 服务器URL地址（用于 sse 和 streamableHttp 类型） */
  url: z.string().url().optional(),

  /** 要执行的命令（用于 stdio 类型） */
  command: z.string().optional(),

  /** 命令行参数列表（用于 stdio 类型） */
  args: z.array(z.string()).optional(),

  /** 环境变量配置（用于 stdio 类型） */
  env: z.record(z.string()).optional(),

  /** 自定义请求头（用于 sse 和 streamableHttp 类型） */
  headers: z.record(z.string()).optional()
}).refine(
  (data) => {
    // 如果指定了 transportType，验证对应字段
    if (data.transportType === 'stdio') {
      return !!data.command;
    }
    if (data.transportType === 'sse' || data.transportType === 'streamableHttp') {
      return !!data.url;
    }

    // 如果未指定 transportType，必须有 command 或 url
    return !!(data.command || data.url);
  },
  {
    message: "Invalid configuration: stdio requires 'command', sse/streamableHttp requires 'url'"
  }
);

/**
 * MCP设置文件Schema
 * 定义了配置文件的总体结构
 */
export const McpSettingsSchema = z.object({
  /** 服务器配置映射表，key为服务器名称 */
  mcpServers: z.record(ServerConfigSchema),
  /** MCP运行模式，默认为auto */
  mode: z.enum(['auto', 'manual']).optional().default('auto')
});

/**
 * 使用Zod Schema推导的MCP服务器配置类型
 */
export type McpServerConfig = z.infer<typeof ServerConfigSchema>;

/**
 * 获取服务器配置的传输类型
 * 如果未指定 transportType，根据配置字段推断：
 * - 有 command：stdio
 * - 有 url：streamableHttp（默认，可通过连接测试降级到 sse）
 */

const allowTypeList: ('stdio' | 'sse' | 'streamableHttp')[] = ['stdio', 'sse', 'streamableHttp'];

export function getTransportType(config: McpServerConfig): 'stdio' | 'sse' | 'streamableHttp' {
  if (config.transportType && allowTypeList.includes(config.transportType)) {
    return config.transportType;
  }

  if (config.type && allowTypeList.includes(config.type as 'stdio' | 'sse' | 'streamableHttp')) {
    return config.type as 'stdio' | 'sse' | 'streamableHttp';
  }

  if (config.command) {
    return 'stdio';
  }

  if (config.url) {
    return 'streamableHttp';
  }

  return 'stdio';
}

/**
 * MCP设置文件Schema
 * 定义了配置文件的总体结构
 */
export const ServerConfigSimpleSchema = z.record(z.unknown());

/**
 * MCP设置文件Schema 简单版本，只校验 mcpServers 字段
 * 定义了配置文件的总体结构
 */
export const McpSettingsSimpleSchema = z.object({
  /** 服务器配置映射表，key为服务器名称 */
  mcpServers: z.record(ServerConfigSimpleSchema),
  /** MCP运行模式，默认为auto */
  mode: z.enum(['auto', 'manual']).optional().default('auto')
});

export type McpServerSimpleConfig = z.infer<typeof ServerConfigSimpleSchema>;
