import fsPromises from 'fs/promises';
import chokidar from 'chokidar';
import { getMcpConfigPath, getMcpSettingsPath } from '@/util/paths';
import { MCPLogger } from '@/util/log';

const logger = new MCPLogger(`mcp-file-helper`);

/**
 * Check if specified path file exists
 * @param filePath - File path to check
 * @returns Promise<boolean> Whether file exists
 */
export async function existsFile(filePath: string) {
  try {
    await fsPromises.access(filePath, fsPromises.constants.F_OK);
    return true;
  } catch (err) {
    logger.error(`Failed to check if file ${filePath} exists`, err);
    return false;
  }
}

/**
 * Ensure specified path file exists
 * If file doesn't exist, create it with provided default content
 * @param filePath - File path
 * @param defaultContent - Default file content
 * @returns Promise<boolean> Whether operation was successful
 */
export async function ensureFileExists(filePath: string, defaultContent: string = '') {
  try {
    const isExists = await existsFile(filePath);
    if (!isExists) {
      await fsPromises.writeFile(filePath, defaultContent);
    }
    return true;
  } catch (error) {
    logger.error(`Failed to ensure file ${filePath} exists`, error);
    return false;
  }
}

/**
 * Read content of specified file
 * @param filePath - File path to read
 * @returns Promise<string | null> File content, returns null if read fails
 */
export async function readFile(filePath: string) {
  try {
    const content = await fsPromises.readFile(filePath, 'utf-8');
    return content;
  } catch (error) {
    logger.error(`Failed to read file ${filePath}`, error);
    return null;
  }
}

/**
 * Write content to specified file
 * @param filePath - Target file path
 * @param content - Content to write
 * @returns Promise<boolean> Whether write operation was successful
 */
export async function writeFile(filePath: string, content: string) {
  try {
    await fsPromises.writeFile(filePath, content);
    return true;
  } catch (error) {
    logger.error(`Failed to write file ${filePath}`, error);
    return false;
  }
}

/**
 * Monitor changes to specified file
 * Use chokidar library to implement file change monitoring, supports atomic writes and write completion waiting
 *
 * @param filePath - File path to monitor
 * @param callback - Callback function when file changes
 * @returns () => void Function to stop monitoring
 */
export function watchFile(filePath: string, callback: () => void) {
  // Monitor file changes
  const watcher = chokidar.watch(filePath, {
    awaitWriteFinish: false,
    atomic: 300,
    ignoreInitial: true
  });

  watcher.on('all', (error) => {
    callback();
  });

  return () => {
    watcher.close();
  };
}

// #region MCP Settings File

/**
 * Check if MCP settings file exists
 * @returns boolean Whether file exists
 */
export async function existsSettingsFile() {
  return await existsFile(getMcpSettingsPath());
}

/**
 * Check and ensure MCP settings file exists
 * If file doesn't exist, create it with default content
 * @returns Promise<string> Complete path of MCP settings file
 * @throws Error when file creation fails
 */
export async function checkMcpSettingsFile() {
  const filePath = getMcpSettingsPath();

  if (!(await ensureFileExists(filePath, mcpSettingDefaultContent))) {
    throw new Error('MCP settings file creation failed!');
  }

  return filePath;
}

/**
 * Write content to MCP settings file
 * @param content - Settings content to write
 * @returns Promise<boolean> Whether write operation was successful
 */
export async function writeSettingsFile(content: string) {
  return await writeFile(getMcpSettingsPath(), content);
}

/**
 * Read MCP settings file content
 * @returns Promise<string | null> Settings file content, returns null if read fails
 */
export async function readSettingsFile(): Promise<string | null> {
  return await readFile(getMcpSettingsPath());
}

// #endregion MCP Settings File

// #region MCP Configuration File

/**
 * Write content to MCP configuration file
 * @param content - Configuration content to write
 * @returns Promise<boolean> Whether write operation was successful
 */
export async function writeConfigFile(content: string) {
  return await writeFile(getMcpConfigPath(), content);
}

/**
 * Read MCP configuration file content
 * @returns Promise<string | null> Configuration file content, returns null if read fails
 */
export async function readConfigFile() {
  return await readFile(getMcpConfigPath());
}

// #endregion MCP Configuration File

/**
 * Default content for MCP settings file
 * Provides an empty server configuration object
 */
export const mcpSettingDefaultContent = `{
  "mcpServers": {
    
  }
}
`;
