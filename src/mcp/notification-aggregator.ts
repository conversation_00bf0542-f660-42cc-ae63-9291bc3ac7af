import { MCPLogger } from '@/util/log';

export interface ConfigChangeNotification {
    type: 'manual_mode_force_disabled';
    serverName: string;
    message: string;
    action: 'add' | 'modify';
}

export interface NotificationMessenger {
    send(channel: string, notification: ConfigChangeNotification): Promise<void>;
}

/**
 * 通知聚合器 - 统一管控和去重通知
 */
export class NotificationAggregator {
    private pendingNotifications = new Map<string, ConfigChangeNotification>();
    private batchTimer: NodeJS.Timeout | null = null;
    private readonly batchDelay: number;
    private readonly logger: InstanceType<typeof MCPLogger>;
    private messenger: NotificationMessenger | null = null;

    constructor(batchDelay: number = 100, logger: InstanceType<typeof MCPLogger>) {
        this.batchDelay = batchDelay;
        this.logger = logger;
    }

    /**
     * 设置消息发送器
     */
    setMessenger(messenger: NotificationMessenger): void {
        this.messenger = messenger;
    }

    /**
     * 添加通知到聚合队列
     * 相同服务器的通知会被去重，保留最新的
     */
    addNotification(notification: ConfigChangeNotification): void {
        const key = this.getNotificationKey(notification);

        // 去重：相同服务器的通知只保留最新的
        if (this.pendingNotifications.has(key)) {
            this.logger.debug(`Deduplicating notification for server: ${notification.serverName}`);
        }

        this.pendingNotifications.set(key, notification);

        // 启动批量发送定时器
        this.scheduleBatchSend();
    }

    /**
     * 立即发送所有待处理的通知
     */
    async flushNotifications(): Promise<void> {
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }

        await this.sendBatchNotifications();
    }

    /**
     * 获取通知的唯一键，用于去重
     */
    private getNotificationKey(notification: ConfigChangeNotification): string {
        return `${notification.type}:${notification.serverName}`;
    }

    /**
     * 调度批量发送
     */
    private scheduleBatchSend(): void {
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
        }

        this.batchTimer = setTimeout(() => {
            this.sendBatchNotifications().catch(error => {
                this.logger.error('Failed to send batch notifications', error);
            });
        }, this.batchDelay);
    }

    /**
     * 发送批量通知
     */
    private async sendBatchNotifications(): Promise<void> {
        if (this.pendingNotifications.size === 0) {
            return;
        }

        if (!this.messenger) {
            this.logger.error('Notification messenger not set, cannot send notifications');
            return;
        }

        const notifications = Array.from(this.pendingNotifications.values());
        this.pendingNotifications.clear();
        this.batchTimer = null;

        this.logger.info(`Sending ${notifications.length} aggregated notifications`);

        // 并行发送所有通知
        const sendPromises = notifications.map(async (notification) => {
            try {
                await this.messenger!.send('mcp/configChangeNotification', notification);
                this.logger.debug(`Sent notification for server: ${notification.serverName} - ${notification.action}`);
            } catch (error) {
                this.logger.error(`Failed to send notification for server: ${notification.serverName}`, error);
            }
        });

        await Promise.allSettled(sendPromises);
    }

    /**
     * 获取当前待处理通知数量
     */
    getPendingCount(): number {
        return this.pendingNotifications.size;
    }

    /**
     * 清空所有待处理通知
     */
    clearPendingNotifications(): void {
        this.pendingNotifications.clear();
        if (this.batchTimer) {
            clearTimeout(this.batchTimer);
            this.batchTimer = null;
        }
    }

    /**
     * 销毁聚合器，清理资源
     */
    destroy(): void {
        this.clearPendingNotifications();
    }
}