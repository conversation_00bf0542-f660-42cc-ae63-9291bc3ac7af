{"name": "binary", "version": "1.0.0902", "author": "kwaipilot Dev, Inc", "description": "", "main": "dist/out/index.js", "bin": "dist/out/index.js", "pkg": {"scripts": ["node_modules/axios/**/*"], "assets": ["node_modules/sqlite3/**/*", "node_modules/onnxruntime-node/**/*"], "targets": ["node18-darwin-arm64"], "outputPath": "bin"}, "scripts": {"preinstall": "node scripts/check-package-manager.js", "project-info": "tsx src/project-info/index.ts", "test": "jest", "start": "tsx src/index.ts", "mcp": "tsx src/mcp/index.ts", "pipeline": "tsx src/pipeline/index.ts", "dev": "npm run inject;KWAIPILOT_DEVELOPMENT=true NODE_ENV=test tsx watch src/index.ts", "dev2": "npm run inject;KWAIPILOT_AGENT_PORT=3001 KWAIPILOT_DEVELOPMENT=true NODE_ENV=test tsx watch src/index.ts", "inject": "node scripts/inject-trace-config.js", "build": "npm run inject && node scripts/build.js", "build:dev": "tsc", "esbuild": "node build.js --esbuild-only", "postinstall": "node scripts/postinstall.js && patch-package", "lint": "eslint src/**/*.{ts,js} --ext .ts,.js", "lint:fix": "eslint src/**/*.{ts,js} --ext .ts,.js --fix", "format": "prettier --write src/**/*.{ts,js,json,md}", "format:check": "prettier --check src/**/*.{ts,js,json,md}", "check": "npm run lint && npm run format:check"}, "license": "Apache-2.0", "devDependencies": {"@babel/preset-env": "^7.24.7", "@chialab/esbuild-plugin-require-resolve": "^0.18.0", "@figma/rest-api-spec": "^0.33.0", "@types/clone-deep": "^4.0.4", "@types/cors": "^2.8.19", "@types/diff": "^5.2.2", "@types/express": "^5.0.3", "@types/get-port": "^4.0.1", "@types/i18n": "^0.13.12", "@types/jest": "^29.5.12", "@types/jsdom": "^21.1.6", "@types/mime-types": "^2.1.4", "@types/node": "^18.19.75", "@types/node-fetch": "^2.6.11", "@types/pg": "^8.11.6", "@types/proper-lockfile": "^4.1.4", "@types/request": "^2.48.12", "@types/uuid": "^9.0.7", "@types/which": "^3.0.4", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vercel/ncc": "^0.38.1", "cross-env": "^7.0.3", "esbuild": "0.17.19", "eslint": "^8", "jest": "^29.7.0", "onnxruntime-common": "1.13.1", "onnxruntime-web": "1.13.1", "patch-package": "^8.0.0", "pkg": "^5.8.1", "prettier": "^3.2.5", "rimraf": "^5.0.7", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "dependencies": {"@fortaine/fetch-event-source": "^3.0.6", "@ks/weblogger": "^3.10.45", "@modelcontextprotocol/sdk": "^1.17.3", "@types/express": "^5.0.3", "@types/lodash-es": "^4.17.12", "@types/nanoid": "^2.1.0", "@types/pidusage": "^2.0.5", "@typescript-eslint/eslint-plugin": "^7.8.0", "@vscode/ripgrep": "^1.15.11", "axios": "^1.6.7", "best-effort-json-parser": "^1.2.1", "chokidar": "^4.0.3", "chromium-bidi": "^7.1.0", "clone-deep": "^4.0.1", "commander": "^12.0.0", "delay": "^6.0.0", "diff": "^8.0.2", "dotenv": "^16.4.5", "execa": "^6.1.0", "express": "^5.1.0", "extract-zip": "^2.0.1", "fast-folder-size": "^2.4.0", "fast-glob": "^3.3.3", "form-data": "^4.0.2", "front-matter": "^4.0.2", "get-port": "^7.1.0", "globby": "11.0.4", "http-proxy-middleware": "^3.0.5", "i18n": "^0.15.1", "ignore": "^5.3.2", "isbinaryfile": "^5.0.4", "jsdom": "^24.0.0", "langfuse": "^3.37.2", "lodash-es": "^4.17.21", "mammoth": "^1.9.0", "nanoid": "^5.0.9", "ncp": "^2.0.0", "node-fetch": "^3.3.2", "npm-run-all": "^4.1.5", "onnxruntime-node": "1.13.1", "os-name": "^6.0.0", "p-timeout": "^6.1.4", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "pidusage": "^4.0.1", "playwright-core": "1.55.0-alpha-2025-08-07", "proper-lockfile": "^4.1.2", "readdirp": "^4.0.2", "request": "^2.88.2", "rotating-file-stream": "^3.2.5", "safe-regex2": "^5.0.0", "serialize-error": "^12.0.0", "simple-git": "^3.27.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "systeminformation": "^5.27.1", "tsx": "^4.19.2", "uuid": "^9.0.1", "which": "^5.0.0", "yaml": "^2.4.2", "zod": "^3.24.3"}, "engine-strict": true, "engines": {"node": ">=18.20.4", "npm": ">=10.7.0"}, "packageManager": "npm@10.8.2"}