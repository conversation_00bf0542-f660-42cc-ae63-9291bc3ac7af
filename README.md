# kwaipilot 核心二进制文件

本文件夹的目的是将 Typescript 代码打包成可以在任何 IDE 或平台上运行的形式。我们首先使用 `esbuild` 进行打包，然后使用 `pkg` 打包成二进制文件。

`pkgJson/package.json` 包含了使用 pkg 构建的说明，需要放在单独的文件夹中，因为 assets 选项没有 CLI 标志（必须在 package.json 中指定），而且 pkg 只认 package.json 这个名字。如果我们使用带有依赖项的相同 package.json，pkg 会自动包含这些依赖项，显著增加二进制文件的大小。

构建过程的其他部分完全在 `scripts/build.js` 中定义。

## 使用方式

使用 npm run build 编译后，将 dist/bin 目录下对应操作系统的文件夹(darwin-arm64, darwin-x64, linux-arm64, linux-x64, win32-x64)拷贝到插件对应的目录中，使用子进程启动

### 原生模块列表

- sqlite3/build/Release/node_sqlite3.node (\*)

### 动态导入模块列表

- esbuild

### .wasm 文件列表

- tree-sitter.wasm
- tree-sitter-wasms/

(\*) = 需要为每个平台手动下载

### scm 文件列表

- src/tree-sitter/code-snippet-queries

## 构建

```bash
npm run build
```

## 测试

### 单元测试

```bash
npm run test
```

### 调试测试

```bash
node test/client.mjs
```
