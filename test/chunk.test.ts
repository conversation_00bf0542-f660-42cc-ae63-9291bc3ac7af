import { IProtocol, ToIdeProtocol } from '../src/protocol/index';
import { IdeCommonMessage, IMessenger } from '../src/protocol/messenger';
import fs from 'fs';
import { ChildProcess, ChildProcessWithoutNullStreams, spawn } from 'node:child_process';
import path from 'path';
import { CoreBinaryMessenger, CoreBinaryTcpMessenger } from '../src/entry/IpcMessenger';
import { ResponseBase, IdeSettings, STATUS } from '../src/protocol/index.d';
import { EditFileResponse, ExecuteCommandResponse } from '@/agent/types/type';
const dirPath = '/Users/<USER>/Documents/work/doraemon';
const commonInfo = {
  pluginVersion: '9.0.1',
  version: '1.0.26', // IDE 版本
  platform: 'vscode', // IDE 平台
  repo: {
    git_url: 'https://github.com/kuaishou/kproxy.git', // 完整git地址
    dir_path: dirPath, // 目录地址
    commit: 'undefined666' // git commit id
  },
  cwd: dirPath
};
// jest.setTimeout(100_000);
class TestMessenger<T extends ToIdeProtocol, U extends IProtocol>
  extends CoreBinaryMessenger<T, U>
  implements IMessenger<T, U> {
  constructor(subprocess: ChildProcess) {
    super(subprocess as ChildProcessWithoutNullStreams);
  }
  getCommonMessage(): IdeCommonMessage {
    return {
      version: '9.0.1',
      platform: 'vscode',
      cwd: dirPath,
      pluginVersion: '19.0.5',
      repo: {
        git_url: 'https://github.com/kuaishou/kproxy.git',
        dir_path: dirPath,
        commit: 'undefined666'
      }
    };
  }
}

const USE_TCP = false;
function autodetectPlatformAndArch() {
  const platform = {
    aix: 'linux',
    darwin: 'darwin',
    freebsd: 'linux',
    linux: 'linux',
    openbsd: 'linux',
    sunos: 'linux',
    win32: 'win32',
    android: 'linux',
    cygwin: 'win32',
    netbsd: 'linux',
    haiku: 'linux'
  }[process.platform];
  const arch = {
    arm: 'arm64',
    arm64: 'arm64',
    ia32: 'x64',
    loong64: 'arm64',
    mips: 'arm64',
    mipsel: 'arm64',
    ppc: 'x64',
    ppc64: 'x64',
    riscv64: 'arm64',
    s390: 'x64',
    s390x: 'x64',
    x64: 'x64'
  }[process.arch];
  return [platform, arch];
}

const KWAIPILOT_GLOBAL_DIR = path.join(__dirname, '..', '.kwaipilot');
if (fs.existsSync(KWAIPILOT_GLOBAL_DIR)) {
  fs.rmSync(KWAIPILOT_GLOBAL_DIR, { recursive: true, force: true });
}
fs.mkdirSync(KWAIPILOT_GLOBAL_DIR);

let messenger: IMessenger<ToIdeProtocol, IProtocol>;
let subprocess: ChildProcess;

const [platform, arch] = autodetectPlatformAndArch();
const binaryDir = path.join(__dirname, '..', 'dist', 'bin', `${platform}-${arch}`);
const exe = platform === 'win32' ? '.exe' : '';
const binaryPath = path.join(binaryDir, `kwaipilot-binary${exe}`);
process.env.KWAIPILOT_AGENT_USE_SOCKET = 'true';
process.env.KWAIPILOT_AGENT_PORT = '3001';
if (USE_TCP) {
  messenger = new CoreBinaryTcpMessenger<ToIdeProtocol, IProtocol>();
} else {
  try {
    subprocess = spawn(binaryPath, {
      env: {
        ...process.env,
        KWAIPILOT_GLOBAL_DIR,
        NODE_ENV: 'production',
        KWAIPILOT_AGENT_USE_SOCKET: 'true',
        KWAIPILOT_AGENT_PORT: '3001'
      },
      stdio: ['pipe', 'pipe', 'pipe', 'ipc']
    });
    console.log('Successfully spawned subprocess');
  } catch (error) {
    console.error('Error spawning subprocess:', error);
    throw error;
  }
  subprocess.on('close', () => {
    console.log('subprocess closed');
  });
  subprocess.on('error', (error) => {
    console.error('Error in subprocess:', error);
  });
  subprocess.on('exit', (code, signal) => {
    console.log('subprocess exited with code:', code, 'and signal:', signal);
  });
  subprocess.on('message', (data) => {
    console.log('subprocess message:', data);
  });
  messenger = new CoreBinaryTcpMessenger<ToIdeProtocol, IProtocol>();
}

const testDir = path.join(__dirname, '..', '.test');
if (!fs.existsSync(testDir)) {
  fs.mkdirSync(testDir);
}

messenger.on('config/getIdeSetting', (message) => {
  console.log(message, '--------------');
  const resp: ResponseBase<IdeSettings> = {
    status: STATUS.OK,
    data: {
      agentPreference: 'speed',
      dirPath: dirPath,
      fileRetryTime: 3,
      modelRetryTime: 3,
      enableRepoIndex: true,
      maxIndexSpace: 10,
      proxyUrl: ''
    }
  };
  return resp;
});
messenger.on('state/ideInfo', (message) => {
  console.log('state/ideInfo', message, '--------------');
  return {
    status: 'ok',
    data: {
      pluginVersion: '8.2.1',
      version: '1.0.26',
      platform: 'vscode',
      cwd: dirPath,
      repoInfo: {
        git_url: 'https://github.com/kuaishou/kproxy.git',
        dir_path: dirPath,
        commit: 'undefined666',
        branch: 'master'
      },
      userInfo: {
        name: 'renbeihai'
      },
      proxyUrl: 'https://kwaipilot.corp.kuaishou.com/',
      maxIndexSpace: 10
    }
  } as ResponseBase<any>;
});
messenger.on('state/ideState', (message) => {
  console.log(message, '--------------');
  return {
    status: 'ok'
  } as ResponseBase<null>;
});
messenger.on('index/progress', (message) => {
  console.log(message, '--------------');
  if (message.data.progress === 1) {
    console.log('index/progress', message, '--------------');
    console.log('索引完成，可以开始搞其他的东西了');
    makeAssistantRequest();
  }
  return undefined;
});
messenger.on('assistant/agent/message', (message) => {
  console.log(message, 'assistant/agent/message--------------');
  return undefined;
});
messenger.on('assistant/agent/messageList', (message) => {
  console.log(message, 'assistant/agent/messageList--------------');
  return undefined;
});
messenger.on('assistant/agent/apiConversationList', (message) => {
  console.log(message, 'assistant/agent/apiConversationList--------------');
  return undefined;
});
messenger.on('assistant/agent/environment', (message) => {
  console.log(message, 'assistant/agent/environment--------------');
  return {
    status: 'ok',
    data: 'success'
  } as ResponseBase<string>;
});
messenger.on('assistant/agent/executeCommand', (message) => {
  console.log(message, 'assistant/agent/executeCommand--------------');
  return {
    status: 'ok',
    data: {
      userSkip: false,
      result: 'success',
      cutRes: 'success'
    }
  } as ResponseBase<ExecuteCommandResponse>;
});
messenger.on('assistant/agent/editFile', (message) => {
  console.log(message, 'assistant/agent/editFile--------------');
  return {
    status: 'ok',
    data: {
      content: 'success',
      path: 'success',
      startLine: 1,
      endLine: 1,
      type: 'editFile'
    }
  } as unknown as ResponseBase<EditFileResponse>;
});
// messenger.on('test/agentBigChunk', (message) => {
//   console.log('binary to ide cost :', Date.now() - message.data.start, 'length: ', message.data.chunk.length);
//   //   console.log(message, 'test/agentBigChunk--------------');
// });
const runTest = async () => {
  messenger.setIsInited(true);
  const resp = await messenger.request('state/agentState', undefined);
  console.log(resp, 'agentState--------------');
  if (resp.status === 'ok') {
    // for (let i = 0; i < 1000; i++) {
    sendBigChunkMessage(1);
    // }
  }
};
runTest();
const sendBigChunkMessage = async (num: number) => {
  const response = await messenger.request('test/ideBigChunk', {
    start: Date.now(),
    chunk:
      '1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456'.repeat(
        1000
      )
  });
  console.log(
    'binary to ide cost :',
    Date.now() - response.data.start,
    'ide to binary cost: ',
    response.data.cost,
    'length: ',
    response.data.chunk.length,
    num
  );
  //   console.log(response.data);
};
async function makeAssistantRequest() {
  const message = {
    type: 'newTask',
    // task: "帮我执行一下删除 readme 文件命令",
    // task: "解释一下indexManager的作用",
    // 帮我修改src/agent/agent.ts文件，在文件的第100行添加一个注释，注释内容为："这是一个测试"
    task: `在src目录中搜索startTask函数的作用`,
    reqData: {
      sessionId: 'rfdxyl3zli01lpdgc55d',
      chatId: 'm8cvoaq8be4tmoccmp5',
      messages: [],
      useSearch: false,
      username: 'liuzhengzheng',
      projectInfo: {
        projectName: 'web-infra/ai-devops/ide-agent',
        gitUrl: '*************************:web-infra/ai-devops/ide-agent.git',
        openedFilePath: 'src/agent/agent.ts'
      },
      deviceInfo: {
        deviceId: '',
        platform: 'kwaipilot-vscode',
        pluginVersion: '8.3.0',
        deviceName: 'liuzhengzhengdeMacBook-Pro-2.local',
        deviceModel: 'darwin',
        deviceOsVersion: '24.3.0',
        deviceOsName: 'macOS',
        ide: 'vscode',
        ideVersion: '1.85.1'
      }
    },
    localMessages: []
  };

  const resp = await messenger.request('assistant/agent/local', message);
  console.log(resp, 'assistant/agent/local--------------');
}
