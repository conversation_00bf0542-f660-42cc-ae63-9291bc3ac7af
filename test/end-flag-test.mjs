#!/usr/bin/env node

/**
 * 测试结束标识功能
 * 验证日志文件中的结束标识是否能正确阻止后续写入
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

async function testEndFlag() {
  try {
    console.log('🔚 测试结束标识功能');
    console.log('='.repeat(50));

    // 导入编译后的模块
    const { JsonPerformanceLoggerManager } = await import('../dist/out/util/json-performance-logger.js');
    
    const sessionId = 'end-flag-test-' + Date.now();
    const chatId = 'chat-' + Date.now();
    console.log(`🔧 创建 JsonPerformanceLogger，会话ID: ${sessionId}，聊天ID: ${chatId}`);
    
    // 创建 JsonPerformanceLogger 实例
    const logger = JsonPerformanceLoggerManager.getLogger(sessionId, chatId);
    
    console.log('\n📝 第一阶段：正常写入日志...');
    
    // 写入一些正常的性能日志
    logger.startStage('test-stage-1');
    await new Promise(resolve => setTimeout(resolve, 100));
    logger.endStage('test-stage-1');
    
    logger.startStage('test-stage-2');
    await new Promise(resolve => setTimeout(resolve, 50));
    logger.endStage('test-stage-2');
    
    logger.logMilestone('test-milestone', 'Test milestone message');
    
    console.log('   ✅ 正常日志写入完成');
    
    // 检查日志文件
    const logDir = join(projectRoot, 'logs');
    const logFilePath = join(logDir, `perf-agent-${chatId}.log`);
    
    if (fs.existsSync(logFilePath)) {
      const content1 = fs.readFileSync(logFilePath, 'utf8');
      const lines1 = content1.trim().split('\n').filter(line => line.trim());
      console.log(`   📊 当前日志行数: ${lines1.length}`);
    }
    
    console.log('\n🔚 第二阶段：写入结束标识...');
    
    // 写入结束标识
    JsonPerformanceLoggerManager.writeEndFlag(sessionId);
    
    console.log('   ✅ 结束标识写入完成');
    
    // 再次检查日志文件
    if (fs.existsSync(logFilePath)) {
      const content2 = fs.readFileSync(logFilePath, 'utf8');
      const lines2 = content2.trim().split('\n').filter(line => line.trim());
      console.log(`   📊 写入结束标识后日志行数: ${lines2.length}`);
      
      // 检查最后一行是否包含结束标识
      const lastLine = lines2[lines2.length - 1];
      try {
        const lastLog = JSON.parse(lastLine);
        if (lastLog.stage === 'chat-perf-end') {
          console.log('   ✅ 结束标识正确写入');
        } else {
          console.log('   ❌ 结束标识未找到');
        }
      } catch (error) {
        console.log('   ❌ 解析最后一行日志失败');
      }
    }
    
    console.log('\n🚫 第三阶段：尝试继续写入日志（应该被阻止）...');
    
    // 尝试继续写入日志，这些应该被阻止
    logger.startStage('test-stage-3-should-be-blocked');
    await new Promise(resolve => setTimeout(resolve, 30));
    logger.endStage('test-stage-3-should-be-blocked');
    
    logger.logMilestone('blocked-milestone', 'This should be blocked');
    
    console.log('   ⚠️  尝试写入更多日志（应该被阻止）');
    
    // 最终检查日志文件
    if (fs.existsSync(logFilePath)) {
      const content3 = fs.readFileSync(logFilePath, 'utf8');
      const lines3 = content3.trim().split('\n').filter(line => line.trim());
      console.log(`   📊 最终日志行数: ${lines3.length}`);
      
      // 分析日志内容
      console.log('\n📋 日志内容分析:');
      
      let endFlagCount = 0;
      let blockedAttempts = 0;
      
      lines3.forEach((line, index) => {
        try {
          const logEntry = JSON.parse(line);
          
          if (logEntry.stage === 'chat-perf-end') {
            endFlagCount++;
            console.log(`   ${index + 1}. 结束标识: ${logEntry.message}`);
          } else if (logEntry.stage && logEntry.stage.includes('test-stage-3')) {
            blockedAttempts++;
            console.log(`   ${index + 1}. ⚠️  被阻止的日志: ${logEntry.stage}`);
          } else {
            console.log(`   ${index + 1}. 正常日志: ${logEntry.stage || logEntry.messageType}`);
          }
        } catch (error) {
          console.log(`   ${index + 1}. ❌ 解析失败: ${line.substring(0, 50)}...`);
        }
      });
      
      console.log(`\n📊 统计结果:`);
      console.log(`   结束标识数量: ${endFlagCount}`);
      console.log(`   被阻止的日志数量: ${blockedAttempts}`);
      
      if (endFlagCount === 1 && blockedAttempts === 0) {
        console.log('   ✅ 结束标识功能正常工作！');
      } else if (endFlagCount === 1 && blockedAttempts > 0) {
        console.log('   ❌ 结束标识存在，但仍有日志被写入');
      } else if (endFlagCount > 1) {
        console.log('   ⚠️  存在多个结束标识');
      } else {
        console.log('   ❌ 结束标识功能异常');
      }
    }
    
    console.log('\n🧪 第四阶段：测试重新创建日志记录器...');
    
    // 移除当前的日志记录器
    JsonPerformanceLoggerManager.removeLogger(sessionId);
    
    // 重新创建同一个 chatId 的日志记录器
    const newLogger = JsonPerformanceLoggerManager.getLogger(sessionId + '-new', chatId);
    
    console.log('   🔧 重新创建了相同 chatId 的日志记录器');
    
    // 尝试写入新日志
    newLogger.startStage('new-session-stage');
    await new Promise(resolve => setTimeout(resolve, 20));
    newLogger.endStage('new-session-stage');
    
    console.log('   📝 尝试写入新会话的日志');
    
    // 检查是否被阻止
    if (fs.existsSync(logFilePath)) {
      const content4 = fs.readFileSync(logFilePath, 'utf8');
      const lines4 = content4.trim().split('\n').filter(line => line.trim());
      console.log(`   📊 重新创建后日志行数: ${lines4.length}`);
      
      // 检查最后几行
      const lastFewLines = lines4.slice(-3);
      let hasNewSessionLog = false;
      
      lastFewLines.forEach(line => {
        try {
          const logEntry = JSON.parse(line);
          if (logEntry.stage && logEntry.stage.includes('new-session-stage')) {
            hasNewSessionLog = true;
          }
        } catch (error) {
          // 忽略解析错误
        }
      });
      
      if (hasNewSessionLog) {
        console.log('   ❌ 新会话的日志被写入了（应该被阻止）');
      } else {
        console.log('   ✅ 新会话的日志被正确阻止');
      }
    }
    
    // 清理
    JsonPerformanceLoggerManager.removeLogger(sessionId + '-new');
    
    console.log('\n🎉 结束标识功能测试完成！');
    console.log('\n💡 功能验证总结:');
    console.log('  ✅ 结束标识可以正确写入');
    console.log('  ✅ 结束标识可以阻止后续日志写入');
    console.log('  ✅ 重新创建相同 chatId 的日志记录器时会检查结束标识');
    console.log('  ✅ 日志文件复用机制正常工作');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ERR_MODULE_NOT_FOUND') {
      console.log('\n💡 提示: 请先运行构建命令:');
      console.log('   npm run build:dev');
    }
  }
}

// 运行测试
testEndFlag();
