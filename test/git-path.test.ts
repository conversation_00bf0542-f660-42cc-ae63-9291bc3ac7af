import { getRelativePath } from '../src/util/git-path';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

describe('getRelativePath', () => {
  let tempDir: string;
  let testRepoDir: string;

  beforeAll(() => {
    // 创建临时测试目录
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'git-path-test-'));
    testRepoDir = path.join(tempDir, 'duet-test');
    
    // 创建测试目录结构
    fs.mkdirSync(testRepoDir, { recursive: true });
    fs.mkdirSync(path.join(testRepoDir, 'src'), { recursive: true });
    fs.mkdirSync(path.join(testRepoDir, 'src', 'src'), { recursive: true });
    
    // 创建 .git 目录来模拟 git 仓库
    fs.mkdirSync(path.join(testRepoDir, '.git'), { recursive: true });
  });

  afterAll(() => {
    // 清理临时目录
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('基本功能测试', () => {
    test('应该返回相对于 git 根目录的路径', async () => {
      const absolutePath = path.join(testRepoDir, 'random-content.md');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe('random-content.md');
    });

    test('应该返回相对于 git 根目录的子目录路径', async () => {
      const absolutePath = path.join(testRepoDir, 'src', 'random-content.md');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'random-content.md'));
    });

    test('应该处理嵌套子目录', async () => {
      const absolutePath = path.join(testRepoDir, 'src', 'src', 'randomApp.go');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'src', 'randomApp.go'));
    });
  });

  describe('根据提供的测试用例', () => {
    test('case 1: vscode 打开 /Users/<USER>/learn/duet-test，在工作区目录创建 random-content.md', async () => {
      // 模拟: vscode 打开 duet-test，在工作区目录创建文件
      const absolutePath = path.join(testRepoDir, 'random-content.md');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe('random-content.md');
    });

    test('case 2: vscode 打开 /Users/<USER>/learn/duet-test，在src目录创建 random-content.md', async () => {
      // 模拟: vscode 打开 duet-test，在src目录创建文件
      const absolutePath = path.join(testRepoDir, 'src', 'random-content.md');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'random-content.md'));
    });

    test('case 3: vscode 打开 /Users/<USER>/learn/duet-test/src，在工作区目录创建 random-content.md', async () => {
      // 模拟: vscode 打开 duet-test/src，在工作区目录创建文件
      const absolutePath = path.join(testRepoDir, 'src', 'random-content.md');
      const workspacePath = path.join(testRepoDir, 'src');
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      // 应该返回相对于 git 根目录的路径，而不是相对于 workspace 的路径
      expect(result).toBe(path.join('src', 'random-content.md'));
    });

    test('case 4: vscode 打开 /Users/<USER>/learn/duet-test/src，在src目录创建 random-content.md', async () => {
      // 模拟: vscode 打开 duet-test/src，在src目录创建文件
      const absolutePath = path.join(testRepoDir, 'src', 'src', 'random-content.md');
      const workspacePath = path.join(testRepoDir, 'src');
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'src', 'random-content.md'));
    });

    test('case 5: vscode 打开 /Users/<USER>/learn/duet-test，在src/src目录创建 randomApp.go', async () => {
      // 模拟: vscode 打开 duet-test，在src/src目录创建文件
      const absolutePath = path.join(testRepoDir, 'src', 'src', 'randomApp.go');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'src', 'randomApp.go'));
    });
  });

  describe('边界情况测试', () => {
    test('当没有找到 .git 目录时，应该返回相对于 workspace 的路径', async () => {
      // 创建一个没有 .git 目录的临时目录
      const nonGitDir = fs.mkdtempSync(path.join(os.tmpdir(), 'non-git-test-'));
      const subDir = path.join(nonGitDir, 'subdir');
      fs.mkdirSync(subDir, { recursive: true });
      
      try {
        const absolutePath = path.join(subDir, 'test.txt');
        const workspacePath = nonGitDir;
        
        const result = await getRelativePath(absolutePath, workspacePath);
        
        expect(result).toBe(path.join('subdir', 'test.txt'));
      } finally {
        // 清理
        fs.rmSync(nonGitDir, { recursive: true, force: true });
      }
    });

    test('当文件路径就在 git 根目录时', async () => {
      const absolutePath = path.join(testRepoDir, 'root-file.txt');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe('root-file.txt');
    });

    test('当 workspace 路径和文件路径相同时', async () => {
      const samePath = testRepoDir;
      
      const result = await getRelativePath(samePath, samePath);
      
      expect(result).toBe('');
    });
  });

  describe('跨平台路径处理', () => {
    test('应该正确处理不同平台的路径分隔符', async () => {
      const absolutePath = path.join(testRepoDir, 'src', 'components', 'Button.tsx');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      // 使用 path.join 确保跨平台兼容性
      expect(result).toBe(path.join('src', 'components', 'Button.tsx'));
    });
  });

  describe('性能和安全测试', () => {
    test('应该在合理时间内完成', async () => {
      const start = Date.now();
      const absolutePath = path.join(testRepoDir, 'src', 'test.txt');
      const workspacePath = testRepoDir;
      
      await getRelativePath(absolutePath, workspacePath);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    test('应该处理深层嵌套目录而不会无限循环', async () => {
      // 创建深层嵌套目录
      const deepDir = path.join(testRepoDir, 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j');
      fs.mkdirSync(deepDir, { recursive: true });
      
      const absolutePath = path.join(deepDir, 'deep-file.txt');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'deep-file.txt'));
    });
  });

  describe('错误处理', () => {
    test('应该处理不存在的路径', async () => {
      const nonExistentPath = path.join(testRepoDir, 'non-existent', 'file.txt');
      const workspacePath = testRepoDir;
      
      // 函数应该不会抛出错误，即使路径不存在
      await expect(async () => {
        await getRelativePath(nonExistentPath, workspacePath);
      }).not.toThrow();
    });

    test('应该处理空字符串路径', async () => {
      await expect(async () => {
        await getRelativePath('', testRepoDir);
      }).not.toThrow();
      
      await expect(async () => {
        await getRelativePath(testRepoDir, '');
      }).not.toThrow();
    });

    test('应该处理极深的目录层级（模拟超过500层的情况）', async () => {
      // 创建一个模拟的极深路径（不实际创建目录）
      const rootPath = process.platform === 'win32' ? 'C:\\' : '/';
      let deepPath = rootPath;
      
      // 构造一个很深的路径字符串
      for (let i = 0; i < 10; i++) {
        deepPath = path.join(deepPath, `level${i}`);
      }
      
      const workspacePath = rootPath;
      
      // 函数应该能够处理这种情况而不会无限循环
      const start = Date.now();
      const result = await getRelativePath(deepPath, workspacePath);
      const duration = Date.now() - start;
      
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
      expect(typeof result).toBe('string');
    });

    test('应该在没有 .git 目录的深层路径中正确回退到 workspace 相对路径', async () => {
      // 创建一个没有 .git 的深层目录结构
      const noGitDir = fs.mkdtempSync(path.join(os.tmpdir(), 'no-git-deep-'));
      const deepPath = path.join(noGitDir, 'a', 'b', 'c', 'd', 'e');
      fs.mkdirSync(deepPath, { recursive: true });
      
      try {
        const filePath = path.join(deepPath, 'test.txt');
        const result = await getRelativePath(filePath, noGitDir);
        
        expect(result).toBe(path.join('a', 'b', 'c', 'd', 'e', 'test.txt'));
      } finally {
        fs.rmSync(noGitDir, { recursive: true, force: true });
      }
    });
  });

  describe('日志记录测试', () => {
    test('应该能够处理日志记录而不影响功能', async () => {
      // 这个测试主要确保日志记录不会破坏函数的正常功能
      const absolutePath = path.join(testRepoDir, 'src', 'test-log.txt');
      const workspacePath = testRepoDir;
      
      const result = await getRelativePath(absolutePath, workspacePath);
      
      expect(result).toBe(path.join('src', 'test-log.txt'));
    });
  });
});