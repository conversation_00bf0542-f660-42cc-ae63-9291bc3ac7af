#!/usr/bin/env node

/**
 * 测试新的 JSON 格式性能日志记录器
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 动态导入编译后的模块
async function testPerfLogger() {
  try {
    console.log('📊 测试 JSON 格式性能日志记录器');
    console.log('='.repeat(50));

    // 导入编译后的模块
    const { PerfLoggerManager } = await import('../dist/out/util/perf-logger.js');
    
    const sessionId = 'test-json-session-' + Date.now();
    console.log(`🔧 创建性能日志记录器，会话ID: ${sessionId}`);
    
    const logger = PerfLoggerManager.getInstance().getLogger(sessionId);
    
    console.log('\n📝 测试各种日志记录方法...');
    
    // 测试 start/end 配对
    console.log('1. 测试 start/end 配对');
    logger.start('testFunction', 'newTask');
    
    // 模拟一些工作
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const duration = logger.end('testFunction', 'newTask');
    console.log(`   ✅ 函数执行耗时: ${duration}ms`);
    
    // 测试 record 方法
    console.log('2. 测试 record 方法');
    logger.record('apiRequest', 250, 'api');
    
    // 测试 milestone 方法
    console.log('3. 测试 milestone 方法');
    logger.milestone('first-token-received', 1850, {
      sessionId: sessionId,
      model: 'claude-3.5-sonnet',
      apiDuration: 1520
    });
    
    // 测试 phaseSummary 方法
    console.log('4. 测试 phaseSummary 方法');
    logger.phaseSummary('preprocessing', [
      { name: 'initTrace', duration: 15 },
      { name: 'getEnvironmentDetails', duration: 680 },
      { name: 'optimizeContext', duration: 180 }
    ], 875);
    
    // 测试错误日志
    console.log('5. 测试错误日志');
    logger.error('apiCall', new Error('Connection timeout'));
    
    // 测试警告日志
    console.log('6. 测试警告日志');
    logger.warn('contextOptimization', 'Context size approaching limit');
    
    // 测试调试日志
    console.log('7. 测试调试日志');
    logger.debug('messageProcessing', 'Processing user message');
    
    // 关闭日志记录器
    console.log('\n🔚 关闭日志记录器');
    PerfLoggerManager.getInstance().closeLogger(sessionId);
    
    // 等待一下确保日志写入完成
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 读取并显示生成的日志文件
    console.log('\n📄 生成的日志文件内容:');
    console.log('='.repeat(50));
    
    const logDir = join(projectRoot, 'logs');
    const logFiles = fs.readdirSync(logDir).filter(file => 
      file.includes(`perf-agent-${sessionId}`) && file.endsWith('.log')
    );
    
    if (logFiles.length > 0) {
      const logFile = join(logDir, logFiles[0]);
      const logContent = fs.readFileSync(logFile, 'utf8');
      
      console.log(`📁 日志文件: ${logFiles[0]}`);
      console.log('📋 内容:');
      
      // 解析并美化显示每行 JSON
      const lines = logContent.trim().split('\n').filter(line => line.trim());
      lines.forEach((line, index) => {
        try {
          const logEntry = JSON.parse(line);
          console.log(`\n${index + 1}. ${logEntry.stage} (${logEntry.messageType})`);
          console.log(`   时间: ${logEntry.localtime}`);
          console.log(`   阶段索引: ${logEntry.stageIndex}`);
          console.log(`   开始: ${logEntry.isStart}, 结束: ${logEntry.isEnd}`);
          console.log(`   级别: ${logEntry.level}`);
          console.log(`   消息: ${logEntry.message}`);
          if (logEntry.duration !== undefined) {
            console.log(`   耗时: ${logEntry.duration}ms`);
          }
        } catch (error) {
          console.log(`   ❌ 解析失败: ${line}`);
        }
      });
      
      console.log('\n📊 JSON 格式验证:');
      console.log('✅ 所有日志条目都是有效的 JSON 格式');
      console.log(`✅ 共生成 ${lines.length} 条日志记录`);
      
      // 验证必需字段
      const requiredFields = ['localtime', 'sessionId', 'stage', 'time', 'messageType', 'stageIndex', 'isStart', 'isEnd', 'level', 'message'];
      let allValid = true;
      
      lines.forEach((line, index) => {
        try {
          const logEntry = JSON.parse(line);
          const missingFields = requiredFields.filter(field => !(field in logEntry));
          if (missingFields.length > 0) {
            console.log(`❌ 第 ${index + 1} 条记录缺少字段: ${missingFields.join(', ')}`);
            allValid = false;
          }
        } catch (error) {
          console.log(`❌ 第 ${index + 1} 条记录不是有效 JSON`);
          allValid = false;
        }
      });
      
      if (allValid) {
        console.log('✅ 所有日志记录都包含必需字段');
      }
      
      // 显示示例 JSON 结构
      if (lines.length > 0) {
        console.log('\n📋 示例 JSON 结构:');
        try {
          const exampleEntry = JSON.parse(lines[0]);
          console.log(JSON.stringify(exampleEntry, null, 2));
        } catch (error) {
          console.log('❌ 无法解析示例条目');
        }
      }
      
    } else {
      console.log('❌ 未找到日志文件');
    }
    
    console.log('\n🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    // 如果是模块导入失败，提示需要先构建
    if (error.code === 'ERR_MODULE_NOT_FOUND') {
      console.log('\n💡 提示: 请先运行构建命令:');
      console.log('   npm run build:dev');
    }
  }
}

// 运行测试
testPerfLogger();
