import { MessageParam } from '../../src/agent/types/type';
import fs from 'fs/promises';
import path from 'path';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('path');

/**
 * 用户文件操作检测逻辑的单元测试
 * 
 * 测试覆盖范围：
 * 1. getUserActionMessage 方法的核心逻辑
 *    - 检测用户删除AI创建的文件
 *    - 检测用户重新创建被删除的文件
 *    - 处理复杂的多文件场景
 *    - 错误处理和边缘情况
 * 
 * 2. handelGetMessagesFileStatus 方法的逻辑
 *    - 收集AI操作过的文件路径
 *    - 文件存在性检查
 *    - 过滤非文件操作
 *    - 错误处理
 * 
 * 3. 用户操作消息格式化
 *    - 删除文件消息格式
 *    - 添加文件消息格式
 */

// 提取用户文件操作检测逻辑的纯函数版本，用于测试
class UserFileActionDetector {
  private cwd: string;
  private loggerManager: any;

  constructor(cwd: string, loggerManager: any) {
    this.cwd = cwd;
    this.loggerManager = loggerManager;
  }

  async getUserActionMessage(messages: MessageParam[]) {
    const fileStatusMap = new Map<string, 'delete' | 'add'>();

    messages.forEach(m => {
      if (m.role === 'user' && m.version === 1) {
        if (Array.isArray(m.content)) {
          m.content.forEach(c => {
            if (c.type === 'text' && 'category' in c && c.category === 'user-action' && 'data' in c) {
              try {
                const actions = c.data;
                const { action, data } = actions;
                if (action === 'user_delete_file') {
                  for (const item of data) {
                    fileStatusMap.set(item, 'delete');
                  }
                } else if (action === 'user_add_file') {
                  for (const item of data) {
                    fileStatusMap.set(item, 'add');
                  }
                }
              } catch (error) {
                this.loggerManager.agentDebug('getUserActionMessage error', error);
              }
            }
          });
        }
      }
    });

    const { deletedFiles, existFiles } = await this.handelGetMessagesFileStatus(messages);

    const userDelete: string[] = [];
    const userAdd: string[] = [];

    deletedFiles.forEach(i => {
      const mapHasFile = fileStatusMap.has(i.filePath);
      if (mapHasFile) {
        const status = fileStatusMap.get(i.filePath);
        if (status === 'add') {
          userDelete.push(i.filePath);
        }
      } else {
        userDelete.push(i.filePath);
      }
    });

    existFiles.forEach(i => {
      const mapHasFile = fileStatusMap.has(i.filePath);
      if (mapHasFile) {
        const status = fileStatusMap.get(i.filePath);
        if (status === 'delete') {
          userAdd.push(i.filePath);
        }
      }
    });

    return { userDelete, userAdd };
  }

  private async handelGetMessagesFileStatus(messages: MessageParam[]) {
    try {
      const modifiedFileList = messages.filter(message => message.role === 'user')
        .flatMap(message => {
          if (typeof message.content === 'string') return [];
          return message.content
            .filter(item =>
              item.type === 'text' &&
              'toolName' in item &&
              (item.toolName === 'write_to_file' || item.toolName === 'replace_in_file' || item.toolName === 'read_file') &&
              'category' in item &&
              item.category === 'tool-response' &&
              'params' in item
            )
            .map(item => {
              const params = (item as any).params;
              return params && typeof params === 'object' && typeof params.path === 'string' ? params.path : null;
            })
            .filter((path): path is string => path !== null);
        }).filter((path, index, array) => array.indexOf(path) === index) || [];

      const checkDeletedFiles = async (fileList: string[]) => {
        const checkPromises = fileList.map(async (filePath) => {
          try {
            await fs.access(path.resolve(this.cwd, filePath));
            return { status: 'exist', filePath };
          } catch (error) {
            return { status: 'delete', filePath };
          }
        });

        const results = await Promise.all(checkPromises);
        return results;
      };

      const statusFiles = await checkDeletedFiles(modifiedFileList);
      const existFiles = statusFiles.filter(i => i.status === 'exist');
      const deletedFiles = statusFiles.filter(i => i.status === 'delete');
      return { deletedFiles, existFiles };
    } catch (error) {
      this.loggerManager.agentDebug('error handling add delete file to message:', error);
      return { deletedFiles: [], existFiles: [] };
    }
  }
}

describe('用户文件操作检测逻辑', () => {
  let detector: UserFileActionDetector;
  let mockLoggerManager: any;
  let mockFs: jest.Mocked<typeof fs>;
  let mockPath: jest.Mocked<typeof path>;

  beforeEach(() => {
    // Mock LoggerManager
    mockLoggerManager = {
      agentDebug: jest.fn()
    };

    // Mock fs and path
    mockFs = fs as jest.Mocked<typeof fs>;
    mockPath = path as jest.Mocked<typeof path>;

    // Create detector instance
    detector = new UserFileActionDetector('/test/project', mockLoggerManager);

    // Mock path.resolve
    mockPath.resolve = jest.fn((base, file) => `${base}/${file}`);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserActionMessage', () => {
    it('应该检测到用户删除了AI之前创建的文件', async () => {
      // 准备测试数据：历史记录显示用户添加了文件，但文件现在不存在
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'AI created file',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File created successfully',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: {
                path: 'test.txt'
              }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'User action data',
              category: 'user-action',
              data: {
                action: 'user_add_file',
                data: ['test.txt']
              }
            }
          ]
        }
      ];

      // Mock fs.access to simulate file not existing
      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toContain('test.txt');
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该检测到用户重新创建了之前被删除的文件', async () => {
      // 准备测试数据：历史记录显示用户删除了文件，但文件现在存在
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'AI read file',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File content here',
              category: 'tool-response',
              toolName: 'read_file',
              params: {
                path: 'existing.txt'
              }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'User deleted file',
              category: 'user-action',
              data: {
                action: 'user_delete_file',
                data: ['existing.txt']
              }
            }
          ]
        }
      ];

      // Mock fs.access to simulate file existing
      mockFs.access = jest.fn().mockResolvedValue(undefined);

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userAdd).toContain('existing.txt');
      expect(result.userDelete).toHaveLength(0);
    });

    it('应该检测到AI操作过但没有用户操作记录的已删除文件', async () => {
      // 准备测试数据：AI操作过文件，但没有用户操作记录，文件现在不存在
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'AI edited file',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File edited successfully',
              category: 'tool-response',
              toolName: 'replace_in_file',
              params: {
                path: 'edited.txt'
              }
            }
          ]
        }
      ];

      // Mock fs.access to simulate file not existing
      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toContain('edited.txt');
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该处理多个文件的复杂场景', async () => {
      const mockMessages: MessageParam[] = [
        // AI操作了多个文件
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'AI operations',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File1 created',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: { path: 'file1.txt' }
            },
            {
              type: 'text',
              text: 'File2 read',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'file2.txt' }
            },
            {
              type: 'text',
              text: 'File3 edited',
              category: 'tool-response',
              toolName: 'replace_in_file',
              params: { path: 'file3.txt' }
            }
          ]
        },
        // 用户操作记录
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'User added file1',
              category: 'user-action',
              data: {
                action: 'user_add_file',
                data: ['file1.txt']
              }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat3',
          content: [
            {
              type: 'text',
              text: 'User deleted file2',
              category: 'user-action',
              data: {
                action: 'user_delete_file',
                data: ['file2.txt']
              }
            }
          ]
        }
      ];

      // Mock fs.access with different results for different files
      mockFs.access = jest.fn().mockImplementation((filePath) => {
        if (filePath.includes('file1.txt')) {
          // file1.txt 不存在（用户删除了之前添加的文件）
          return Promise.reject(new Error('File not found'));
        } else if (filePath.includes('file2.txt')) {
          // file2.txt 存在（用户重新创建了之前删除的文件）
          return Promise.resolve(undefined);
        } else if (filePath.includes('file3.txt')) {
          // file3.txt 存在（正常状态）
          return Promise.resolve(undefined);
        }
        return Promise.reject(new Error('File not found'));
      });

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toContain('file1.txt'); // 用户删除了之前添加的文件
      expect(result.userAdd).toContain('file2.txt');    // 用户重新创建了之前删除的文件
      expect(result.userDelete).not.toContain('file3.txt'); // file3正常，不需要通知
      expect(result.userAdd).not.toContain('file3.txt');
    });

    it('应该处理无效的用户操作数据', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'Invalid user action',
              category: 'user-action',
              data: {
                action: 'user_delete_file',
                data: null // 这会导致 for...of 循环失败
              }
            } as any
          ]
        }
      ];

      mockFs.access = jest.fn().mockResolvedValue(undefined);

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toHaveLength(0);
      expect(result.userAdd).toHaveLength(0);
      expect(mockLoggerManager.agentDebug).toHaveBeenCalledWith(
        'getUserActionMessage error',
        expect.any(Error)
      );
    });

    it('应该处理空的历史记录', async () => {
      const result = await detector.getUserActionMessage([]);

      expect(result.userDelete).toHaveLength(0);
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该正确去重文件路径', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'Multiple operations on same file',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File read',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'duplicate.txt' }
            },
            {
              type: 'text',
              text: 'File edited',
              category: 'tool-response',
              toolName: 'replace_in_file',
              params: { path: 'duplicate.txt' }
            },
            {
              type: 'text',
              text: 'File read again',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'duplicate.txt' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toEqual(['duplicate.txt']); // 应该只有一个，不重复
      expect(mockFs.access).toHaveBeenCalledTimes(1); // 只检查一次
    });

    it('应该处理用户操作状态的覆盖逻辑', async () => {
      // 测试同一文件的多次用户操作，后面的操作应该覆盖前面的
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'AI created file',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: { path: 'test.txt' }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'User deleted file',
              category: 'user-action',
              data: {
                action: 'user_delete_file',
                data: ['test.txt']
              }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat3',
          content: [
            {
              type: 'text',
              text: 'User added file again',
              category: 'user-action',
              data: {
                action: 'user_add_file',
                data: ['test.txt']
              }
            }
          ]
        }
      ];

      // 文件现在不存在
      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      // 最后的状态是 'add'，但文件不存在，所以应该检测为用户删除
      expect(result.userDelete).toContain('test.txt');
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该处理非版本1的用户消息', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 0, // 非版本1
          chatId: 'chat1',
          content: 'Simple string content'
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'AI created file',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: { path: 'test.txt' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toContain('test.txt');
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该处理字符串类型的消息内容', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: 'This is a string content, not array'
        }
      ];

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toHaveLength(0);
      expect(result.userAdd).toHaveLength(0);
    });

    it('应该处理非文本类型的内容项', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'image',
              source: {
                type: 'base64',
                media_type: 'image/png',
                data: 'base64data'
              }
            } as any,
            {
              type: 'text',
              text: 'AI created file',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: { path: 'test.txt' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockRejectedValue(new Error('File not found'));

      const result = await detector.getUserActionMessage(mockMessages);

      expect(result.userDelete).toContain('test.txt');
      expect(result.userAdd).toHaveLength(0);
    });
  });

  describe('handelGetMessagesFileStatus', () => {
    it('应该正确收集AI操作过的文件路径', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'File operations',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File created',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: { path: 'created.txt' }
            },
            {
              type: 'text',
              text: 'File read',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'read.txt' }
            },
            {
              type: 'text',
              text: 'File edited',
              category: 'tool-response',
              toolName: 'replace_in_file',
              params: { path: 'edited.txt' }
            },
            {
              type: 'text',
              text: 'Non-file operation',
              category: 'tool-response',
              toolName: 'grep_search', // 不是文件操作
              params: { query: 'search term' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockImplementation((filePath) => {
        if (filePath.includes('created.txt') || filePath.includes('read.txt')) {
          return Promise.resolve(undefined); // 文件存在
        } else {
          return Promise.reject(new Error('File not found')); // 文件不存在
        }
      });

      const result = await (detector as any).handelGetMessagesFileStatus(mockMessages);

      expect(result.existFiles).toHaveLength(2);
      expect(result.deletedFiles).toHaveLength(1);
      expect(result.existFiles.map((f: any) => f.filePath)).toContain('created.txt');
      expect(result.existFiles.map((f: any) => f.filePath)).toContain('read.txt');
      expect(result.deletedFiles.map((f: any) => f.filePath)).toContain('edited.txt');
    });

    it('应该处理文件系统访问错误', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'File operation',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'test.txt' }
            }
          ]
        }
      ];

      // 创建一个会抛出错误的detector
      const errorDetector = new UserFileActionDetector('/test/project', mockLoggerManager);
      
      // Mock handelGetMessagesFileStatus to throw error by mocking the entire method
      const spy = jest.spyOn(errorDetector as any, 'handelGetMessagesFileStatus').mockImplementation(async () => {
        mockLoggerManager.agentDebug('error handling add delete file to message:', new Error('File system error'));
        return { deletedFiles: [], existFiles: [] };
      });

      const result = await (errorDetector as any).handelGetMessagesFileStatus(mockMessages);

      expect(result.deletedFiles).toHaveLength(0);
      expect(result.existFiles).toHaveLength(0);
      expect(mockLoggerManager.agentDebug).toHaveBeenCalledWith(
        'error handling add delete file to message:',
        expect.any(Error)
      );
      
      spy.mockRestore();
    });

    it('应该过滤掉非文件操作的工具调用', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'Search operation',
              category: 'tool-response',
              toolName: 'grep_search',
              params: { query: 'search term' }
            },
            {
              type: 'text',
              text: 'List files operation',
              category: 'tool-response',
              toolName: 'list_files',
              params: { path: '.' }
            },
            {
              type: 'text',
              text: 'File operation',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'valid.txt' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockResolvedValue(undefined);

      const result = await (detector as any).handelGetMessagesFileStatus(mockMessages);

      expect(result.existFiles).toHaveLength(1);
      expect(result.existFiles[0].filePath).toBe('valid.txt');
      expect(result.deletedFiles).toHaveLength(0);
    });

    it('应该处理缺少params的工具响应', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'user',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'Tool response without params',
              category: 'tool-response',
              toolName: 'read_file'
              // 缺少 params
            } as any,
            {
              type: 'text',
              text: 'Tool response with invalid params',
              category: 'tool-response',
              toolName: 'write_to_file',
              params: null
            } as any,
            {
              type: 'text',
              text: 'Tool response with params but no path',
              category: 'tool-response',
              toolName: 'replace_in_file',
              params: { content: 'some content' }
            } as any
          ]
        }
      ];

      const result = await (detector as any).handelGetMessagesFileStatus(mockMessages);

      expect(result.existFiles).toHaveLength(0);
      expect(result.deletedFiles).toHaveLength(0);
    });

    it('应该处理assistant角色的消息', async () => {
      const mockMessages: MessageParam[] = [
        {
          role: 'assistant',
          version: 1,
          chatId: 'chat1',
          content: [
            {
              type: 'text',
              text: 'Assistant message',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'assistant-file.txt' }
            }
          ]
        },
        {
          role: 'user',
          version: 1,
          chatId: 'chat2',
          content: [
            {
              type: 'text',
              text: 'User message',
              category: 'tool-response',
              toolName: 'read_file',
              params: { path: 'user-file.txt' }
            }
          ]
        }
      ];

      mockFs.access = jest.fn().mockResolvedValue(undefined);

      const result = await (detector as any).handelGetMessagesFileStatus(mockMessages);

      // 应该只处理user角色的消息
      expect(result.existFiles).toHaveLength(1);
      expect(result.existFiles[0].filePath).toBe('user-file.txt');
    });
  });

  describe('用户操作消息格式化', () => {
    it('应该正确格式化用户删除文件的消息', () => {
      const userDelete = ['file1.txt', 'file2.txt'];
      const userContent: any[] = [];

      // 模拟添加删除文件消息的逻辑
      if (userDelete.length) {
        userContent.unshift({
          type: 'text',
          data: {
            action: 'user_delete_file',
            data: userDelete
          },
          category: 'user-action',
          text: JSON.stringify({
            action: 'user_delete_file',
            data: userDelete
          })
        });
      }

      expect(userContent).toHaveLength(1);
      expect(userContent[0].category).toBe('user-action');
      expect(userContent[0].data.action).toBe('user_delete_file');
      expect(userContent[0].data.data).toEqual(['file1.txt', 'file2.txt']);
      expect(JSON.parse(userContent[0].text)).toEqual({
        action: 'user_delete_file',
        data: ['file1.txt', 'file2.txt']
      });
    });

    it('应该正确格式化用户添加文件的消息', () => {
      const userAdd = ['new1.txt', 'new2.txt'];
      const userContent: any[] = [];

      // 模拟添加添加文件消息的逻辑
      if (userAdd.length) {
        userContent.unshift({
          type: 'text',
          text: JSON.stringify({
            action: 'user_add_file',
            data: userAdd
          }),
          data: {
            action: 'user_add_file',
            data: userAdd
          },
          category: 'user-action'
        });
      }

      expect(userContent).toHaveLength(1);
      expect(userContent[0].category).toBe('user-action');
      expect(userContent[0].data.action).toBe('user_add_file');
      expect(userContent[0].data.data).toEqual(['new1.txt', 'new2.txt']);
      expect(JSON.parse(userContent[0].text)).toEqual({
        action: 'user_add_file',
        data: ['new1.txt', 'new2.txt']
      });
    });
  });
});