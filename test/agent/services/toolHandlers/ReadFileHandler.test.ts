import { ReadFileHandler } from '../../../../src/agent/services/toolHandlers/ReadFileHandler';
import { ToolHandlerContext } from '../../../../src/agent/services/ToolHelpers';
import { TextBlockParamVersion1 } from '../../../../src/agent/types/type.d';
import { ToolUse } from '../../../../src/agent/types/message';
import fs from 'fs/promises';
import path from 'path';
import { extractTextFromFile } from '../../../../src/agent/tools/extract-text';
import { listFiles } from '../../../../src/agent/tools/list-files';
import { exec } from 'child_process';

// Mock dependencies
jest.mock('fs/promises');
jest.mock('../../../../src/agent/tools/extract-text');
jest.mock('../../../../src/agent/tools/list-files');
jest.mock('child_process');
jest.mock('serialize-error', () => ({
  serializeError: jest.fn((error) => ({ message: error.message, stack: error.stack }))
}));

const mockFs = fs as jest.Mocked<typeof fs>;
const mockExtractTextFromFile = extractTextFromFile as jest.MockedFunction<typeof extractTextFromFile>;
const mockListFiles = listFiles as jest.MockedFunction<typeof listFiles>;
const mockExec = exec as jest.MockedFunction<typeof exec>;

describe('ReadFileHandler', () => {
  let handler: ReadFileHandler;
  let mockContext: jest.Mocked<ToolHandlerContext>;
  let userMessageContent: TextBlockParamVersion1[];

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock context
    mockContext = {
      stateManager: {
        getState: jest.fn().mockReturnValue({
          consecutiveMistakeCount: 0,
          sessionId: 'test-session',
          chatId: 'test-chat'
        }),
        updateState: jest.fn()
      },
      messageService: {
        say: jest.fn().mockResolvedValue(undefined)
      },
      loggerManager: {
        agentInfo: jest.fn(),
        reportUserAction: jest.fn(),
        perf: jest.fn(),
        getTrace: jest.fn().mockReturnValue({
          generation: jest.fn().mockReturnValue({
            end: jest.fn()
          })
        }),
        getAgentLogger: jest.fn().mockReturnValue({
          info: jest.fn(),
          debug: jest.fn()
        })
      },
      cwd: '/test/project',
      sessionInfo: {},
      messenger: {} as any,
      checkpointService: {} as any,
      agentManager: {} as any
    } as any;

    userMessageContent = [];
    handler = new ReadFileHandler(mockContext);
  });

  describe('handle method', () => {
    it('应该处理部分工具调用', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'test.txt' },
        partial: true
      };

      await handler.handle(block, userMessageContent);

      expect(mockContext.messageService.say).toHaveBeenCalledWith(
        'tool',
        expect.stringContaining('"tool":"readFile"'),
        true
      );
    });

    it('应该处理缺失路径参数的情况', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: {},
        partial: false
      };

      await handler.handle(block, userMessageContent);

      expect(mockContext.stateManager.updateState).toHaveBeenCalledWith({
        consecutiveMistakeCount: 1
      });
      expect(userMessageContent).toHaveLength(2); // tool-title + tool-response
    });

    it('应该成功读取文件', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'test.txt' },
        partial: false
      };

      const mockFileContent = {
        content: 'file content',
        totalLineNum: 10,
        readedLineNum: 10
      };

      // Mock file exists
      mockFs.access.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      mockExtractTextFromFile.mockResolvedValue(mockFileContent);

      await handler.handle(block, userMessageContent);

      expect(mockContext.stateManager.updateState).toHaveBeenCalledWith({
        consecutiveMistakeCount: 0
      });
      expect(mockExtractTextFromFile).toHaveBeenCalledWith(
        path.resolve('/test/project', 'test.txt'),
        undefined
      );
      expect(userMessageContent).toHaveLength(2); // tool-title + tool-response
      expect(userMessageContent[1].text).toBe('file content');
    });

    it('应该处理目录路径', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'src' },
        partial: false
      };

      // Mock directory exists
      mockFs.access.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ isDirectory: () => true } as any);
      mockListFiles.mockResolvedValue([['file1.ts', 'file2.ts'], false]);

      await handler.handle(block, userMessageContent);

      expect(mockListFiles).toHaveBeenCalledWith(
        path.resolve('/test/project', 'src'),
        true,
        200
      );
      expect(userMessageContent[1].text).toContain('Directory path detected');
    });

    it('应该处理指定行范围的文件读取', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: {
          path: 'test.txt',
          start_line_one_indexed: '5',
          end_line_one_indexed: '10',
          should_read_entire_file: 'false'
        },
        partial: false
      };

      const mockFileContent = {
        content: 'partial content',
        totalLineNum: 20,
        readedLineNum: 6
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      mockExtractTextFromFile.mockResolvedValue(mockFileContent);

      await handler.handle(block, userMessageContent);

      expect(mockExtractTextFromFile).toHaveBeenCalledWith(
        path.resolve('/test/project', 'test.txt'),
        { startLine: 5, endLine: 10 }
      );
    });
  });

  describe('normalizePath method', () => {
    it('应该处理绝对路径', async () => {
      const absolutePath = '/absolute/path/file.txt';
      
      // Use reflection to access private method
      const result = await (handler as any).normalizePath(absolutePath);
      
      // The normalizePath method removes leading ./ or /, so absolute paths become relative
      // and then get resolved against cwd
      expect(result.path).toBe(path.resolve('/test/project', 'absolute/path/file.txt'));
      expect(result.amendStrategy).toBeUndefined();
    });

    it('应该处理波浪线路径', async () => {
      const homePath = '~/file.txt';
      const expectedPath = path.resolve(require('os').homedir(), 'file.txt');
      
      const result = await (handler as any).normalizePath(homePath);
      
      expect(result.path).toBe(expectedPath);
    });

    it('应该处理存在的相对路径', async () => {
      const relativePath = 'existing/file.txt';
      const expectedPath = path.resolve('/test/project', relativePath);
      
      mockFs.access.mockResolvedValue(undefined);
      
      const result = await (handler as any).normalizePath(relativePath);
      
      expect(result.path).toBe(expectedPath);
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('路径解析成功')
      );
    });

    it('应该处理不存在的路径并触发智能解析', async () => {
      const relativePath = 'nonexistent/file.txt';
      
      mockFs.access.mockRejectedValue(new Error('File not found'));
      
      // Mock tryPathCompletion to return null quickly to avoid timeout
      jest.spyOn(handler as any, 'tryPathCompletion').mockResolvedValue(null);
      
      const result = await (handler as any).normalizePath(relativePath);
      
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('开始智能路径解析')
      );
    }, 10000);
  });

  describe('intelligentPathResolve method', () => {
    it('应该识别文件和目录', () => {
      const isLikelyFile = (handler as any).isLikelyFile || function(filePath: string) {
        const basename = path.basename(filePath);
        const dotIndex = basename.lastIndexOf('.');
        return dotIndex > 0 && dotIndex < basename.length - 1;
      };

      expect(isLikelyFile('file.txt')).toBe(true);
      expect(isLikelyFile('directory')).toBe(false);
      expect(isLikelyFile('.hidden')).toBe(false);
      expect(isLikelyFile('file.')).toBe(false);
    });

    it('应该对目录返回原始路径', async () => {
      const directoryPath = 'src';
      const fallbackPath = '/test/project/src';
      
      const result = await (handler as any).intelligentPathResolve(directoryPath, fallbackPath);
      
      expect(result.path).toBe(fallbackPath);
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('返回可能是个目录')
      );
    });
  });

  describe('getExtensionPriorityList method', () => {
    it('应该返回正确的扩展名映射', () => {
      const extensionMap = (handler as any).getExtensionPriorityList();
      
      expect(extensionMap['tsx']).toContain('js');
      expect(extensionMap['tsx']).toContain('jsx');
      expect(extensionMap['tsx']).toContain('ts');
      expect(extensionMap['tsx']).not.toContain('tsx'); // 不应包含自身
      
      expect(extensionMap['py']).toContain('pyi');
      expect(extensionMap['py']).toContain('pyc');
      
      expect(extensionMap['css']).toContain('scss');
      expect(extensionMap['css']).toContain('less');
    });
  });

  describe('generateExtensionMap method', () => {
    it('应该正确生成扩展名映射', () => {
      const extensions = [
        ['tsx', 'js', 'jsx'],
        ['py', 'pyi']
      ];
      
      const result = (handler as any).generateExtensionMap(extensions);
      
      expect(result['tsx']).toEqual(['js', 'jsx']);
      expect(result['js']).toEqual(['tsx', 'jsx']);
      expect(result['jsx']).toEqual(['tsx', 'js']);
      expect(result['py']).toEqual(['pyi']);
      expect(result['pyi']).toEqual(['py']);
    });
  });

  describe('findFileWithBash method', () => {
    it('应该使用bash命令查找文件', async () => {
      // Mock successful find command
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(null, '/test/project/src/file.txt\n', '');
        }
        return {} as any;
      });

      const result = await (handler as any).findFileWithBash('file.txt');
      
      expect(result).toEqual({
        path: '/test/project/src/file.txt',
        amendStrategy: 'pathAdd'
      });
    });

    it('应该处理bash命令失败', async () => {
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(new Error('Command failed'), '', 'error');
        }
        return {} as any;
      });

      const result = await (handler as any).findFileWithBash('nonexistent.txt');
      
      expect(result).toBeNull();
    });
  });

  describe('findExactMatch method', () => {
    it('应该查找简单文件名', async () => {
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(null, '/test/project/found.txt\n', '');
        }
        return {} as any;
      });

      const result = await (handler as any).findExactMatch('found.txt', 'found.txt', '.');
      
      expect(result).toBe('/test/project/found.txt');
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('精确匹配成功')
      );
    });

    it('应该查找包含路径的文件', async () => {
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(null, '/test/project/src/components/Button.tsx\n', '');
        }
        return {} as any;
      });

      const result = await (handler as any).findExactMatch('src/components/Button.tsx', 'Button.tsx', 'src/components');
      
      expect(result).toBe('/test/project/src/components/Button.tsx');
    });

    it('应该处理未找到文件的情况', async () => {
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(null, '', '');
        }
        return {} as any;
      });

      const result = await (handler as any).findExactMatch('notfound.txt', 'notfound.txt', '.');
      
      expect(result).toBeNull();
    });
  });

  describe('findWithExtensionCorrection method', () => {
    it('应该查找扩展名修正后的文件', async () => {
      // Mock getExtensionPriorityList to return test data
      jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
        'ts': ['tsx', 'js', 'jsx']
      });
      
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (command.includes('Button.tsx')) {
          if (typeof callback === 'function') {
            callback(null, '/test/project/src/Button.tsx\n', '');
          }
        } else {
          if (typeof callback === 'function') {
            callback(null, '', '');
          }
        }
        return {} as any;
      });

      const result = await (handler as any).findWithExtensionCorrection('Button.ts', 'Button.ts', '.');
      
      expect(result).toBe('/test/project/src/Button.tsx');
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('扩展名修正成功')
      );
    });

    it('应该处理没有扩展名组的情况', async () => {
      jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({});
      
      const result = await (handler as any).findWithExtensionCorrection('file.unknown', 'file.unknown', '.');
      
      expect(result).toBeNull();
      expect(mockContext.loggerManager.agentInfo).toHaveBeenCalledWith(
        expect.stringContaining('未找到 unknown 的扩展名组')
      );
    });
  });

  describe('getExcludedPathsForFindCommand method', () => {
    it('应该生成正确的排除路径命令', () => {
      const result = (handler as any).getExcludedPathsForFindCommand();
      
      expect(result).toContain('-path');
      expect(result).toContain('-prune');
      expect(result).toContain('-o');
    });
  });

  describe('path correction messages', () => {
    it('应该生成路径补全消息', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'file.txt' },
        partial: false
      };
      
      // Mock path not found initially, then found via bash
      mockFs.access.mockRejectedValueOnce(new Error('Not found'));
      mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (typeof callback === 'function') {
          callback(null, '/test/project/src/file.txt\n', '');
        }
        return {} as any;
      });

      mockExtractTextFromFile.mockResolvedValue({
        content: 'file content',
        totalLineNum: 10,
        readedLineNum: 10
      });

      await handler.handle(block, userMessageContent);

      expect(userMessageContent[1].text).toContain('Path Auto-Completion');
    });

    it('应该生成扩展名修正消息', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'Button.ts' },
        partial: false
      };
      
      mockFs.access.mockRejectedValueOnce(new Error('Not found'));
      mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      
      jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
        'ts': ['tsx', 'js', 'jsx']
      });
      
      // Mock exact match fails, extension correction succeeds
      mockExec.mockImplementation((command: string, options: any, callback: any) => {
        if (command.includes('Button.tsx')) {
          if (typeof callback === 'function') {
            callback(null, '/test/project/src/Button.tsx\n', '');
          }
        } else {
          if (typeof callback === 'function') {
            callback(null, '', '');
          }
        }
        return {} as any;
      });

      mockExtractTextFromFile.mockResolvedValue({
        content: 'component content',
        totalLineNum: 50,
        readedLineNum: 50
      });

      await handler.handle(block, userMessageContent);

      expect(userMessageContent[1].text).toContain('File Extension Auto-Correction');
    });
  });

  describe('容错策略测试', () => {
    describe('容错策略1: 相对于cwd下某个目录的文件', () => {
      it('应该能通过 components/Button.tsx 找到 /test/project/src/components/Button.tsx', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'components/Button.tsx' },
          partial: false
        };

        // Mock 初始路径不存在
        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        // Mock bash find 命令找到文件
        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          if (command.includes('components/Button.tsx')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/src/components/Button.tsx\n', '');
            }
          } else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        await handler.handle(block, userMessageContent);

        // 验证显示了路径补全消息
        expect(userMessageContent[1].text).toContain('Path Auto-Completion');
        expect(userMessageContent[1].text).toContain('components/Button.tsx');
        expect(userMessageContent[1].text).toContain('/test/project/src/components/Button.tsx');
        
        // 对于pathAdd策略，不会直接读取文件，而是显示提示消息
        expect(userMessageContent[1].text).toContain('requires manual confirmation to read');
        
        // 验证没有调用extractTextFromFile（因为pathAdd策略需要手动确认）
        expect(mockExtractTextFromFile).not.toHaveBeenCalled();
      });

      it('应该能通过 utils/helper.js 找到 /test/project/lib/utils/helper.js', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'utils/helper.js' },
          partial: false
        };

        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          if (command.includes('utils/helper.js')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/lib/utils/helper.js\n', '');
            }
          } else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        await handler.handle(block, userMessageContent);

        expect(userMessageContent[1].text).toContain('Path Auto-Completion');
        expect(userMessageContent[1].text).toContain('requires manual confirmation to read');
        expect(mockExtractTextFromFile).not.toHaveBeenCalled();
      });
    });

    describe('容错策略2: 路径正确但文件后缀错误', () => {
      it('应该能通过 src/components/Button.ts 找到 /test/project/src/components/Button.tsx', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'src/components/Button.ts' },
          partial: false
        };

        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        // Mock 扩展名优先级列表
        jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
          'ts': ['tsx', 'js', 'jsx']
        });

        // Mock 精确匹配失败，扩展名修正成功
        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          if (command.includes('Button.tsx')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/src/components/Button.tsx\n', '');
            }
          } else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        mockExtractTextFromFile.mockResolvedValue({
          content: 'import React from "react";\nexport const Button: React.FC = () => <button>Click</button>;',
          totalLineNum: 2,
          readedLineNum: 2
        });

        await handler.handle(block, userMessageContent);

        // 验证找到了正确的文件
        expect(mockExtractTextFromFile).toHaveBeenCalledWith(
          '/test/project/src/components/Button.tsx',
          undefined
        );
        
        // 验证显示了扩展名修正消息
        expect(userMessageContent[1].text).toContain('File Extension Auto-Correction');
        expect(userMessageContent[1].text).toContain('Button.ts');
        expect(userMessageContent[1].text).toContain('Button.tsx');
        
        // 验证文件内容正确返回
        expect(userMessageContent[1].text).toContain('import React from "react";');
      });

      it('应该能通过 src/components/Button.js 找到 /test/project/src/components/Button.tsx', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'src/components/Button.js' },
          partial: false
        };

        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
          'js': ['tsx', 'jsx', 'mjs', 'cjs', 'ts']
        });

        // Mock 精确匹配失败，扩展名修正成功
        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          // 精确匹配：查找 src/components/Button.js - 失败
          if (command.includes('*/src/components/Button.js') && !command.includes('Button.tsx')) {
            if (typeof callback === 'function') {
              callback(null, '', ''); // 没找到
            }
          }
          // 扩展名修正：查找 src/components/Button.tsx - 成功
          else if (command.includes('*/src/components/Button.tsx')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/src/components/Button.tsx\n', '');
            }
          }
          // 其他查找都失败
          else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        mockExtractTextFromFile.mockResolvedValue({
          content: 'import React from "react";\nexport const Button: React.FC = () => <button>Click</button>;',
          totalLineNum: 2,
          readedLineNum: 2
        });

        await handler.handle(block, userMessageContent);

        expect(mockExtractTextFromFile).toHaveBeenCalledWith(
          '/test/project/src/components/Button.tsx',
          undefined
        );
        expect(userMessageContent[1].text).toContain('File Extension Auto-Correction');
        expect(userMessageContent[1].text).toContain('Button.js');
        expect(userMessageContent[1].text).toContain('Button.tsx');
      });

      it('应该能通过 styles/main.css 找到 /test/project/styles/main.scss', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'styles/main.css' },
          partial: false
        };

        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
          'css': ['scss', 'sass', 'less', 'styl']
        });

        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          if (command.includes('main.scss')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/styles/main.scss\n', '');
            }
          } else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        mockExtractTextFromFile.mockResolvedValue({
          content: '$primary-color: #007bff;\n.button { color: $primary-color; }',
          totalLineNum: 2,
          readedLineNum: 2
        });

        await handler.handle(block, userMessageContent);

        expect(mockExtractTextFromFile).toHaveBeenCalledWith(
          '/test/project/styles/main.scss',
          undefined
        );
        expect(userMessageContent[1].text).toContain('File Extension Auto-Correction');
        expect(userMessageContent[1].text).toContain('main.css');
        expect(userMessageContent[1].text).toContain('main.scss');
      });
    });

    describe('组合容错策略测试', () => {
      it('应该能通过 Button.ts 找到 /test/project/src/components/Button.tsx (路径补全 + 扩展名修正)', async () => {
        const block: ToolUse = {
          type: 'tool_use' as any,
          id: 'test-id',
          name: 'read_file',
          params: { path: 'Button.ts' },
          partial: false
        };

        mockFs.access.mockRejectedValueOnce(new Error('Not found'));
        mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);

        jest.spyOn(handler as any, 'getExtensionPriorityList').mockReturnValue({
          'ts': ['tsx', 'js', 'jsx']
        });

        // Mock 精确匹配失败，但扩展名修正成功
        mockExec.mockImplementation((command: string, options: any, callback: any) => {
          if (command.includes('Button.tsx')) {
            if (typeof callback === 'function') {
              callback(null, '/test/project/src/components/Button.tsx\n', '');
            }
          } else {
            if (typeof callback === 'function') {
              callback(null, '', '');
            }
          }
          return {} as any;
        });

        mockExtractTextFromFile.mockResolvedValue({
          content: 'export const Button = () => <button>Click</button>;',
          totalLineNum: 1,
          readedLineNum: 1
        });

        await handler.handle(block, userMessageContent);

        expect(mockExtractTextFromFile).toHaveBeenCalledWith(
          '/test/project/src/components/Button.tsx',
          undefined
        );
        
        // 应该显示扩展名修正消息（因为这是最终成功的策略）
        expect(userMessageContent[1].text).toContain('File Extension Auto-Correction');
        expect(userMessageContent[1].text).toContain('Button.ts');
        expect(userMessageContent[1].text).toContain('Button.tsx');
      });
    });
  });

  describe('error handling', () => {
    it('应该处理文件读取错误', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'test.txt' },
        partial: false
      };

      mockFs.access.mockResolvedValue(undefined);
      mockFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      mockExtractTextFromFile.mockRejectedValue(new Error('Read error'));

      await handler.handle(block, userMessageContent);

      expect(mockContext.messageService.say).toHaveBeenCalledWith(
        'tool_error',
        expect.stringContaining('Error reading file')
      );
    });

    it('应该处理路径解析超时', async () => {
      const block: ToolUse = {
        type: 'tool_use' as any,
        id: 'test-id',
        name: 'read_file',
        params: { path: 'slow.txt' },
        partial: false
      };

      mockFs.access.mockRejectedValue(new Error('Not found'));
      
      // Mock timeout in tryPathCompletion
      jest.spyOn(handler as any, 'tryPathCompletion').mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Path completion timeout')), 100);
        });
      });

      await handler.handle(block, userMessageContent);

      // Should fallback to original path and handle gracefully
      expect(userMessageContent).toHaveLength(2);
    });
  });
});