import { formatLlmMessages, formatUserActionMessages, formatUserActionMessagesText } from '../../../src/agent/tools/parse-assistant-message';
import { MessageParam, Chat, UserActionFileData } from '../../../src/agent/types/type.d';

describe('formatLlmMessages', () => {
  describe('基本功能测试', () => {
    it('应该处理空消息数组', () => {
      const messages: MessageParam[] = [];
      const result = formatLlmMessages(messages);
      
      expect(result).toEqual([]);
    });


    it('应该处理简单的用户文本消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: 'Hello, world!',
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Hello, world!'
          }
        ],
        chatId: 'chat1'
      });
    });

    it('应该处理简单的助手文本消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'assistant',
          content: 'Hello back!',
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        role: 'assistant',
        content: [
          {
            type: 'text',
            text: 'Hello back!'
          }
        ],
        chatId: 'chat1'
      });
    });
  });

  describe('复杂内容数组处理', () => {
    it('应该处理包含文本和图片的用户消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Look at this image:',
              category: 'user-input'
            },
            {
              type: 'image',
              source: {
                type: 'url',
                url: 'https://example.com/image.jpg'
              }
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Look at this image:'
          },
          {
            type: 'image',
            source: {
              type: 'url',
              url: 'https://example.com/image.jpg'
            }
          }
        ],
        chatId: 'chat1'
      });
    });

    it('应该处理包含用户操作数据的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'User question',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'User action data',
              category: 'user-action',
              data: {
                action: 'user_add_file',
                data: ['file1.txt', 'file2.txt']
              }
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect(Array.isArray(result[0].content)).toBe(true);
      expect(result[0].content).toHaveLength(2);
      expect(result[0].content[0]).toEqual({
        type: 'text',
        text: 'User question'
      });
      expect(result[0].content[1]).toEqual({
        type: 'text',
        text: '<context_info type="added_files">\nfile1.txt\nfile2.txt\n</context_info>'
      });
    });

    it('应该处理不包含工具调用的复杂消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Complex message without tools',
              category: 'user-input'
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect(result[0].content).toHaveLength(1);
      expect(result[0].content[0]).toEqual({
        type: 'text',
        text: 'Complex message without tools'
      });
    });
  });

  describe('工具调用处理', () => {
    it('应该处理包含工具调用的消息并创建工具调用和工具结果', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Please read this file',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'File content here',
              category: 'tool-response',
              toolId: 'tool_123',
              toolName: 'read_file',
              params: {
                target_file: 'example.txt'
              }
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      // 由于工具调用和工具结果的配对逻辑，可能会被过滤掉
      // 但如果成功处理，应该有2个消息：助手消息（工具调用）和工具结果
      if (result.length > 0) {
        expect(result.length).toBeGreaterThanOrEqual(1);
        
        // 查找助手消息
        const assistantMessage = result.find(msg => msg.role === 'assistant');
        if (assistantMessage && assistantMessage.tool_calls) {
          expect(assistantMessage.tool_calls).toHaveLength(1);
          expect(assistantMessage.tool_calls[0].id).toBe('tool_123');
          expect(assistantMessage.tool_calls[0].function.name).toBe('read_file');
        }

        // 查找工具结果消息
        const toolMessage = result.find(msg => msg.role === 'tool');
        if (toolMessage) {
          expect(toolMessage.tool_call_id).toBe('tool_123');
          expect(toolMessage.content).toHaveLength(1);
          expect((toolMessage.content[0] as Chat.TextBlockContent).text).toBe('File content here');
        }
      }
    });

    it('应该处理MCP工具调用', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Use MCP tool',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'MCP tool result',
              category: 'tool-response',
              toolId: 'mcp_tool_456',
              toolName: 'use_mcp_tool',
              params: {
                server_name: 'test_server',
                arguments: '{"param1": "value1"}'
              }
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      // 测试MCP工具的特殊处理逻辑
      if (result.length > 0) {
        const assistantMessage = result.find(msg => msg.role === 'assistant');
        if (assistantMessage && assistantMessage.tool_calls) {
          expect(assistantMessage.tool_calls[0].function.name).toBe('test_server'); // MCP工具使用server_name
        }
      }
    });

    it('应该处理不包含工具ID的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Regular message',
              category: 'user-input'
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect(result[0].content).toHaveLength(1);
      expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Regular message');
    });
  });

  describe('消息过滤和配对', () => {
    it('应该过滤掉空内容的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: '',
          version: 1,
          chatId: 'chat1'
        },
        {
          role: 'user',
          content: 'Valid message',
          version: 1,
          chatId: 'chat1'
        },
        {
          role: 'assistant',
          content: [],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Valid message');
    });

    it('应该处理包含空文本的消息数组', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: '',
              category: 'user-input'
            },
            {
              type: 'text',
              text: 'Valid text',
              category: 'user-input'
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect(result[0].content).toHaveLength(1); // 空文本应该被过滤掉
      expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Valid text');
    });

    it('应该保留有效的助手消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'assistant',
          content: 'Valid assistant message',
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);
      
      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('assistant');
      expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Valid assistant message');
    });
  });

  describe('边界情况处理', () => {
    it('应该处理混合版本的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: 'Version 0 message',
          chatId: 'chat1'
        } as any, // 模拟版本0消息
        {
          role: 'assistant',
          content: 'Version 1 message',
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(2);
      expect(result[0].role).toBe('user');
      expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Version 0 message');
      expect(result[1].role).toBe('assistant');
      expect((result[1].content[0] as Chat.TextBlockContent).text).toBe('Version 1 message');
    });

    it('应该处理空chatId的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: 'Message with empty chatId',
          version: 1,
          chatId: ''
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].role).toBe('user');
      expect(result[0].chatId).toBe('');
    });

    it('应该处理复杂的嵌套内容结构', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Complex nested content',
              category: 'user-input'
            },
            {
              type: 'image',
              source: {
                type: 'url',
                url: 'https://example.com/image1.jpg'
              }
            },
            {
              type: 'text',
              text: 'More text after image',
              category: 'user-input'
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].content).toHaveLength(3);
      expect(result[0].content[0]).toEqual({
        type: 'text',
        text: 'Complex nested content'
      });
      expect(result[0].content[1]).toEqual({
        type: 'image',
        source: {
          type: 'url',
          url: 'https://example.com/image1.jpg'
        }
      });
      expect(result[0].content[2]).toEqual({
        type: 'text',
        text: 'More text after image'
      });
    });

    it('应该处理只包含图片的消息', () => {
      const messages: MessageParam[] = [
        {
          role: 'user',
          content: [
            {
              type: 'image',
              source: {
                type: 'url',
                url: 'https://example.com/image.jpg'
              }
            }
          ],
          version: 1,
          chatId: 'chat1'
        }
      ];

      const result = formatLlmMessages(messages);

      expect(result).toHaveLength(1);
      expect(result[0].content).toHaveLength(1);
      expect(result[0].content[0]).toEqual({
        type: 'image',
        source: {
          type: 'url',
          url: 'https://example.com/image.jpg'
        }
      });
    });
  });
});

describe('formatUserActionMessages', () => {
  it('应该格式化用户添加文件操作', () => {
    const fileData: UserActionFileData = {
      action: 'user_add_file',
      data: ['file1.txt', 'file2.txt', 'file3.txt']
    };

    const result = formatUserActionMessages(fileData);

    expect(result).toBe('<context_info type="added_files">\n<!-- User has added these files to the workspace. Mention them only if relevant to the current task. -->\nfile1.txt\nfile2.txt\nfile3.txt\n</context_info>');
  });

  it('应该格式化用户删除文件操作', () => {
    const fileData: UserActionFileData = {
      action: 'user_delete_file',
      data: ['deleted1.txt', 'deleted2.txt']
    };

    const result = formatUserActionMessages(fileData);

    expect(result).toBe('<context_info type="deleted_files">\ndeleted1.txt\ndeleted2.txt\n</context_info>');
  });

  it('应该处理空数据数组', () => {
    const fileData: UserActionFileData = {
      action: 'user_add_file',
      data: []
    };

    const result = formatUserActionMessages(fileData);

    expect(result).toBe('');
  });

  it('应该处理未知操作类型', () => {
    const fileData = {
      action: 'unknown_action',
      data: ['file1.txt']
    };

    const result = formatUserActionMessages(fileData as any);

    expect(result).toBe('');
  });

  it('应该处理无效的输入数据', () => {
    const invalidData = {
      invalid: 'data'
    };

    const result = formatUserActionMessages(invalidData as any);

    expect(result).toBe('');
  });
});

describe('formatUserActionMessagesText', () => {
  it('应该解析并格式化JSON字符串中的用户操作', () => {
    const text = JSON.stringify({
      action: 'user_add_file',
      data: ['new1.txt', 'new2.txt']
    });

    const result = formatUserActionMessagesText(text);

    expect(result).toBe('<context_info type="added_files">\nnew1.txt\nnew2.txt\n</context_info>');
  });

  it('应该解析并格式化删除文件操作', () => {
    const text = JSON.stringify({
      action: 'user_delete_file',
      data: ['removed1.txt']
    });

    const result = formatUserActionMessagesText(text);

    expect(result).toBe('<context_info type="deleted_files">\nremoved1.txt\n</context_info>');
  });

  it('应该处理无效的JSON字符串', () => {
    const invalidJson = 'invalid json string';

    const result = formatUserActionMessagesText(invalidJson);

    expect(result).toBe(invalidJson);
  });

  it('应该处理不包含操作信息的JSON', () => {
    const text = JSON.stringify({
      other: 'data',
      not: 'action'
    });

    const result = formatUserActionMessagesText(text);

    expect(result).toBe(text);
  });

  it('应该处理空数据的操作', () => {
    const text = JSON.stringify({
      action: 'user_add_file',
      data: []
    });

    const result = formatUserActionMessagesText(text);

    expect(result).toBe(text);
  });

  it('应该处理包含特殊字符的文件名', () => {
    const text = JSON.stringify({
      action: 'user_add_file',
      data: ['file with spaces.txt', 'file-with-dashes.js', 'file_with_underscores.py']
    });

    const result = formatUserActionMessagesText(text);

    expect(result).toBe('<context_info type="added_files">\nfile with spaces.txt\nfile-with-dashes.js\nfile_with_underscores.py\n</context_info>');
  });

  it('应该处理空字符串输入', () => {
    const result = formatUserActionMessagesText('');

    expect(result).toBe('');
  });

  it('应该处理null和undefined输入', () => {
    expect(formatUserActionMessagesText(null as any)).toBe(null);
    expect(formatUserActionMessagesText(undefined as any)).toBe(undefined);
  });
});

describe('formatLlmMessages - 额外的边缘情况', () => {
  it('应该处理包含多种内容类型的复杂消息', () => {
    const messages: MessageParam[] = [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Here is my question:',
            category: 'user-input'
          },
          {
            type: 'image',
            source: {
              type: 'url',
              url: 'https://example.com/screenshot.png'
            }
          },
          {
            type: 'text',
            text: 'What do you see in this image?',
            category: 'user-input'
          }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = formatLlmMessages(messages);

    expect(result).toHaveLength(1);
    expect(result[0].content).toHaveLength(3);
    expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Here is my question:');
    expect(result[0].content[1].type).toBe('image');
    expect((result[0].content[2] as Chat.TextBlockContent).text).toBe('What do you see in this image?');
  });

  it('应该处理包含多个用户操作的消息', () => {
    const messages: MessageParam[] = [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Multiple user actions',
            category: 'user-input'
          },
          {
            type: 'text',
            text: 'First action',
            category: 'user-action',
            data: {
              action: 'user_add_file',
              data: ['new1.txt']
            }
          },
          {
            type: 'text',
            text: 'Second action',
            category: 'user-action',
            data: {
              action: 'user_delete_file',
              data: ['old1.txt']
            }
          }
        ],
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = formatLlmMessages(messages);

    expect(result).toHaveLength(1);
    expect(result[0].content).toHaveLength(3);
    expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('Multiple user actions');
    expect((result[0].content[1] as Chat.TextBlockContent).text).toBe('<context_info type="added_files">\nnew1.txt\n</context_info>');
    expect((result[0].content[2] as Chat.TextBlockContent).text).toBe('<context_info type="deleted_files">\nold1.txt\n</context_info>');
  });

  it('应该处理大量消息的性能测试', () => {
    const messages: MessageParam[] = [];
    
    // 创建100个简单消息
    for (let i = 0; i < 100; i++) {
      messages.push({
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `Message ${i}`,
        version: 1,
        chatId: `chat${Math.floor(i / 10)}`
      });
    }

    const startTime = Date.now();
    const result = formatLlmMessages(messages);
    const endTime = Date.now();

    expect(result).toHaveLength(100);
    expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
  });

  it('应该处理包含特殊Unicode字符的消息', () => {
    const messages: MessageParam[] = [
      {
        role: 'user',
        content: '你好世界 🌍 こんにちは 🇯🇵 Hello 🇺🇸',
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = formatLlmMessages(messages);

    expect(result).toHaveLength(1);
    expect((result[0].content[0] as Chat.TextBlockContent).text).toBe('你好世界 🌍 こんにちは 🇯🇵 Hello 🇺🇸');
  });

  it('应该处理非常长的消息内容', () => {
    const longText = 'A'.repeat(10000); // 10KB的文本
    const messages: MessageParam[] = [
      {
        role: 'user',
        content: longText,
        version: 1,
        chatId: 'chat1'
      }
    ];

    const result = formatLlmMessages(messages);

    expect(result).toHaveLength(1);
    expect((result[0].content[0] as Chat.TextBlockContent).text).toBe(longText);
    expect((result[0].content[0] as Chat.TextBlockContent).text?.length).toBe(10000);
  });
});