#!/usr/bin/env node

/**
 * 性能数据上报示例
 * 演示如何使用性能收集器获取结构化的性能数据用于图表绘制
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// 模拟性能数据（实际使用时从 LoggerManager 获取）
const mockPerformanceData = {
  sessionId: "test-session-123",
  taskId: "task-456",
  totalDuration: 3250,
  metrics: [
    // 主任务层级 (level 0)
    { label: "handleTaskLoop", duration: 3250, category: "task", level: 0 },
    
    // LLM请求层级 (level 1)
    { label: "recursivelyMakeLLMRequests", duration: 2800, category: "api", level: 1 },
    
    // 预处理层级 (level 2)
    { label: "initTrace", duration: 15, category: "preprocessing", level: 2 },
    { label: "initCheckpointTracker", duration: 120, category: "preprocessing", level: 2 },
    { label: "checkUserModifiedFiles", duration: 85, category: "preprocessing", level: 2 },
    { label: "saveCheckpoint", duration: 45, category: "preprocessing", level: 2 },
    { label: "getWikiList", duration: 8, category: "preprocessing", level: 2 },
    { label: "handleApiRequestLimits", duration: 12, category: "preprocessing", level: 2 },
    { label: "getEnvironmentDetails", duration: 680, category: "preprocessing", level: 2 },
    { label: "getUserActionMessage", duration: 25, category: "preprocessing", level: 2 },
    { label: "addToApiConversationHistory", duration: 35, category: "preprocessing", level: 2 },
    { label: "optimizeMessagesContext", duration: 180, category: "preprocessing", level: 2 },
    { label: "formatMessages", duration: 8, category: "preprocessing", level: 2 },
    { label: "buildRequestParams", duration: 65, category: "preprocessing", level: 2 },
    
    // API请求层级 (level 2)
    { label: "apiRequest", duration: 1520, category: "api", level: 2 },
  ],
  keyMetrics: {
    firstTokenTime: 1850,  // 从开始到第一个字符
    apiRequestTime: 1520,  // API请求到第一个字符
    preProcessingTime: 1278 // 预处理总时间
  }
};

console.log('📊 性能数据上报示例');
console.log('='.repeat(50));

// 1. 显示原始性能数据
console.log('\n📈 原始性能数据:');
console.log(`会话ID: ${mockPerformanceData.sessionId}`);
console.log(`任务ID: ${mockPerformanceData.taskId}`);
console.log(`总耗时: ${mockPerformanceData.totalDuration}ms`);
console.log(`第一个字符耗时: ${mockPerformanceData.keyMetrics.firstTokenTime}ms`);
console.log(`API请求耗时: ${mockPerformanceData.keyMetrics.apiRequestTime}ms`);
console.log(`预处理耗时: ${mockPerformanceData.keyMetrics.preProcessingTime}ms`);

// 2. 按分类分组数据（用于多层柱状图）
console.log('\n📊 按分类分组的性能数据:');
const groupedByCategory = mockPerformanceData.metrics.reduce((acc, metric) => {
  if (!acc[metric.category]) {
    acc[metric.category] = [];
  }
  acc[metric.category].push(metric);
  return acc;
}, {});

Object.entries(groupedByCategory).forEach(([category, metrics]) => {
  console.log(`\n${category.toUpperCase()}:`);
  metrics.forEach(metric => {
    const percentage = ((metric.duration / mockPerformanceData.totalDuration) * 100).toFixed(1);
    console.log(`  ${'  '.repeat(metric.level)}${metric.label}: ${metric.duration}ms (${percentage}%)`);
  });
});

// 3. 生成图表数据结构
console.log('\n📈 图表数据结构:');
const chartData = {
  // 用于多层柱状图的标签
  labels: mockPerformanceData.metrics.map(m => m.label),
  
  // 按层级分组的数据集
  datasets: Object.entries(groupedByCategory).map(([category, metrics]) => ({
    label: category,
    data: mockPerformanceData.metrics.map(metric => 
      metric.category === category ? metric.duration : 0
    ),
    backgroundColor: getCategoryColor(category),
    borderColor: getCategoryColor(category, 0.8),
    borderWidth: 1
  }))
};

console.log(JSON.stringify(chartData, null, 2));

// 4. 生成瀑布图数据
console.log('\n🌊 瀑布图数据结构:');
const waterfallData = {
  labels: ['开始', ...mockPerformanceData.metrics.map(m => m.label), '结束'],
  datasets: [{
    label: '累计耗时',
    data: [
      0,
      ...mockPerformanceData.metrics.reduce((acc, metric) => {
        const lastValue = acc[acc.length - 1] || 0;
        acc.push(lastValue + metric.duration);
        return acc;
      }, []),
      mockPerformanceData.totalDuration
    ],
    backgroundColor: 'rgba(54, 162, 235, 0.5)',
    borderColor: 'rgba(54, 162, 235, 1)',
    borderWidth: 2,
    fill: false
  }]
};

console.log(JSON.stringify(waterfallData, null, 2));

// 5. 生成上报数据（简化版）
console.log('\n📤 上报数据结构:');
const uploadData = {
  sessionId: mockPerformanceData.sessionId,
  taskId: mockPerformanceData.taskId,
  timestamp: Date.now(),
  performance: {
    total: mockPerformanceData.totalDuration,
    firstToken: mockPerformanceData.keyMetrics.firstTokenTime,
    api: mockPerformanceData.keyMetrics.apiRequestTime,
    preprocessing: mockPerformanceData.keyMetrics.preProcessingTime,
    breakdown: mockPerformanceData.metrics.map(m => ({
      name: m.label,
      duration: m.duration,
      category: m.category,
      level: m.level,
      percentage: ((m.duration / mockPerformanceData.totalDuration) * 100).toFixed(1)
    }))
  }
};

console.log(JSON.stringify(uploadData, null, 2));

// 6. 模拟上报到监控系统
console.log('\n📡 模拟上报到监控系统:');
simulateUpload(uploadData);

// 7. 生成性能分析报告
console.log('\n📋 性能分析报告:');
generateAnalysisReport(mockPerformanceData);

// 辅助函数
function getCategoryColor(category, alpha = 0.5) {
  const colors = {
    task: `rgba(255, 99, 132, ${alpha})`,
    api: `rgba(54, 162, 235, ${alpha})`,
    preprocessing: `rgba(255, 205, 86, ${alpha})`,
    postprocessing: `rgba(75, 192, 192, ${alpha})`,
    initialization: `rgba(153, 102, 255, ${alpha})`,
    cleanup: `rgba(255, 159, 64, ${alpha})`
  };
  return colors[category] || `rgba(128, 128, 128, ${alpha})`;
}

function simulateUpload(data) {
  console.log('🚀 正在上报性能数据...');
  console.log(`📊 数据大小: ${JSON.stringify(data).length} 字节`);
  console.log(`⏱️  总耗时: ${data.performance.total}ms`);
  console.log(`🎯 第一个字符: ${data.performance.firstToken}ms`);
  console.log(`📈 性能指标数量: ${data.performance.breakdown.length}`);
  console.log('✅ 上报成功！');
}

function generateAnalysisReport(data) {
  const { metrics, keyMetrics, totalDuration } = data;
  
  // 找出耗时最长的操作
  const slowestOperations = metrics
    .sort((a, b) => b.duration - a.duration)
    .slice(0, 5);
  
  console.log('🐌 耗时最长的操作:');
  slowestOperations.forEach((op, index) => {
    const percentage = ((op.duration / totalDuration) * 100).toFixed(1);
    console.log(`  ${index + 1}. ${op.label}: ${op.duration}ms (${percentage}%)`);
  });
  
  // 分析各阶段占比
  const categoryTotals = metrics.reduce((acc, metric) => {
    acc[metric.category] = (acc[metric.category] || 0) + metric.duration;
    return acc;
  }, {});
  
  console.log('\n📊 各阶段耗时占比:');
  Object.entries(categoryTotals)
    .sort(([,a], [,b]) => b - a)
    .forEach(([category, duration]) => {
      const percentage = ((duration / totalDuration) * 100).toFixed(1);
      console.log(`  ${category}: ${duration}ms (${percentage}%)`);
    });
  
  // 性能建议
  console.log('\n💡 性能优化建议:');
  if (keyMetrics.preProcessingTime > keyMetrics.apiRequestTime) {
    console.log('  - 预处理时间较长，考虑优化环境信息获取和上下文处理');
  }
  if (keyMetrics.apiRequestTime > 1000) {
    console.log('  - API请求时间较长，检查网络连接和模型响应速度');
  }
  
  const envDetailsMetric = metrics.find(m => m.label === 'getEnvironmentDetails');
  if (envDetailsMetric && envDetailsMetric.duration > 500) {
    console.log('  - getEnvironmentDetails 耗时较长，考虑缓存环境信息');
  }
  
  const contextOptimizeMetric = metrics.find(m => m.label === 'optimizeMessagesContext');
  if (contextOptimizeMetric && contextOptimizeMetric.duration > 100) {
    console.log('  - 上下文优化耗时较长，考虑优化算法或减少消息数量');
  }
}

console.log('\n🎉 性能数据分析完成！');
console.log('\n💡 使用说明:');
console.log('  1. 在实际项目中，从 LoggerManager.getPerformanceUploadData() 获取数据');
console.log('  2. 使用 chartData 结构绘制多层柱状图');
console.log('  3. 使用 waterfallData 结构绘制瀑布图');
console.log('  4. 使用 uploadData 结构上报到监控系统');
console.log('  5. 根据分析报告进行性能优化');
