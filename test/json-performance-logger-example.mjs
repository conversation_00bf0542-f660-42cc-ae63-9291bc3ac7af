#!/usr/bin/env node

/**
 * JSON性能日志记录器示例
 * 展示如何生成类似你提供的格式的JSON性能日志
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 模拟 JsonPerformanceLogger 的功能
class MockJsonPerformanceLogger {
  constructor(sessionId) {
    this.sessionId = sessionId;
    this.startTime = Date.now();
    this.stageCount = 0;
    this.logs = [];
  }

  formatLocalTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }

  formatDurationSeconds(durationMs) {
    return (durationMs / 1000).toFixed(3);
  }

  getTotalDuration() {
    return Date.now() - this.startTime;
  }

  log(data) {
    const logEntry = JSON.stringify(data);
    this.logs.push(logEntry);
    console.log(logEntry);
  }

  logNewTask() {
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'newTask',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: 'newTask'
    });
  }

  startStage(stageName) {
    this.stageCount++;
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'stage',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: `newTask_${stageName}`,
      stage: stageName,
      time: Date.now().toString(),
      messageType2: 'newTask'
    });
  }

  endStage(stageName, duration) {
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'stage',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: `newTask_${stageName}`,
      stage: stageName,
      time: Date.now().toString(),
      messageType2: 'newTask',
      duration,
      durationSeconds: this.formatDurationSeconds(duration)
    });
  }

  logFirstToken(apiDuration) {
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'firstToken',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: 'first_token_received',
      duration: apiDuration,
      durationSeconds: this.formatDurationSeconds(apiDuration)
    });
  }

  logMilestone(milestoneName) {
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'milestone',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: milestoneName,
      time: Date.now().toString()
    });
  }

  logTaskComplete() {
    const totalDuration = this.getTotalDuration();
    this.log({
      localtime: this.formatLocalTime(),
      sessionId: this.sessionId,
      messageType: 'taskComplete',
      totalDuration,
      totalDurationSeconds: this.formatDurationSeconds(totalDuration),
      stageCount: this.stageCount,
      message: 'task_completed'
    });
  }

  saveToFile(filename) {
    const content = this.logs.join('\n') + '\n';
    fs.writeFileSync(filename, content, 'utf8');
    console.log(`\n📁 日志已保存到: ${filename}`);
  }
}

// 模拟一个完整的任务执行过程
async function simulateTaskExecution() {
  console.log('📊 JSON性能日志记录器示例');
  console.log('='.repeat(50));
  
  const sessionId = 'dz1tu5ue41dc892yaud';
  const logger = new MockJsonPerformanceLogger(sessionId);
  
  // 1. 任务开始
  logger.logNewTask();
  await sleep(10);
  
  // 2. 初始化阶段
  logger.startStage('init');
  await sleep(15);
  logger.endStage('init', 15);
  
  // 3. 检查点追踪器初始化
  logger.startStage('initCheckpointTracker');
  await sleep(120);
  logger.endStage('initCheckpointTracker', 120);
  
  // 4. 检查用户修改的文件
  logger.startStage('checkUserModifiedFiles');
  await sleep(85);
  logger.endStage('checkUserModifiedFiles', 85);
  
  // 5. 保存检查点
  logger.startStage('saveCheckpoint');
  await sleep(45);
  logger.endStage('saveCheckpoint', 45);
  
  // 6. 获取环境详情
  logger.startStage('getEnvironmentDetails');
  await sleep(680);
  logger.endStage('getEnvironmentDetails', 680);
  
  // 7. 优化消息上下文
  logger.startStage('optimizeMessagesContext');
  await sleep(180);
  logger.endStage('optimizeMessagesContext', 180);
  
  // 8. 构建请求参数
  logger.startStage('buildRequestParams');
  await sleep(65);
  logger.endStage('buildRequestParams', 65);
  
  // 9. API请求开始
  logger.startStage('apiRequest');
  await sleep(1520);
  
  // 10. 第一个字符返回
  logger.logFirstToken(1520);
  
  // 11. API请求结束
  logger.endStage('apiRequest', 1520);
  
  // 12. 里程碑：任务完成
  logger.logMilestone('task_milestone_completed');
  
  // 13. 任务完成
  logger.logTaskComplete();
  
  // 保存到文件
  const logFilename = join(process.cwd(), 'logs', `perf-vsc-${sessionId}.log`);
  
  // 确保logs目录存在
  const logsDir = join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  logger.saveToFile(logFilename);
  
  console.log('\n📋 生成的日志格式说明:');
  console.log('  - localtime: 本地时间戳，精确到毫秒');
  console.log('  - sessionId: 会话ID，用于关联同一次任务的所有日志');
  console.log('  - messageType: 消息类型 (newTask, stage, firstToken, milestone, taskComplete)');
  console.log('  - totalDuration: 从任务开始到当前的总耗时(毫秒)');
  console.log('  - totalDurationSeconds: 总耗时(秒，保留3位小数)');
  console.log('  - stageCount: 阶段计数器');
  console.log('  - message: 消息内容');
  console.log('  - stage: 阶段名称(仅stage类型)');
  console.log('  - duration: 单个阶段耗时(毫秒)');
  console.log('  - durationSeconds: 单个阶段耗时(秒)');
  
  console.log('\n🔍 日志分析示例:');
  analyzeLogFile(logFilename);
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function analyzeLogFile(filename) {
  try {
    const content = fs.readFileSync(filename, 'utf8');
    const lines = content.trim().split('\n');
    const logs = lines.map(line => JSON.parse(line));
    
    console.log(`\n📊 日志分析结果 (共 ${logs.length} 条记录):`);
    
    // 找出所有阶段
    const stages = logs.filter(log => log.messageType === 'stage' && log.duration);
    console.log(`\n⏱️  各阶段耗时:`);
    stages.forEach(stage => {
      console.log(`  ${stage.stage}: ${stage.duration}ms (${stage.durationSeconds}s)`);
    });
    
    // 找出第一个字符时间
    const firstToken = logs.find(log => log.messageType === 'firstToken');
    if (firstToken) {
      console.log(`\n🎯 第一个字符返回: ${firstToken.duration}ms (${firstToken.durationSeconds}s)`);
    }
    
    // 总耗时
    const lastLog = logs[logs.length - 1];
    console.log(`\n📈 任务总耗时: ${lastLog.totalDuration}ms (${lastLog.totalDurationSeconds}s)`);
    
    // 性能占比分析
    console.log(`\n📊 性能占比分析:`);
    const totalDuration = lastLog.totalDuration;
    stages.forEach(stage => {
      const percentage = ((stage.duration / totalDuration) * 100).toFixed(1);
      console.log(`  ${stage.stage}: ${percentage}%`);
    });
    
  } catch (error) {
    console.error('分析日志文件失败:', error.message);
  }
}

console.log('🚀 开始模拟任务执行...\n');
simulateTaskExecution().then(() => {
  console.log('\n✅ 示例执行完成！');
  console.log('\n💡 使用说明:');
  console.log('  1. 在实际项目中，LoggerManager 会自动生成这种格式的日志');
  console.log('  2. 每行都是一个独立的JSON对象，便于解析和分析');
  console.log('  3. 可以通过 messageType 字段过滤不同类型的日志');
  console.log('  4. totalDuration 字段可以用来绘制时间轴图表');
  console.log('  5. duration 字段可以用来分析各阶段的性能瓶颈');
}).catch(console.error);
