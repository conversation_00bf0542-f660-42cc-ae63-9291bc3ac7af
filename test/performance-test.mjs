#!/usr/bin/env node

/**
 * 性能测试脚本
 * 用于测试 handleTaskLoop 到第一个字符返回的耗时统计
 * 所有性能数据将记录到专用的 perf-{sessionId}.log 文件中
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🚀 启动性能测试...');
console.log('📊 将统计以下关键性能指标：');
console.log('  1. handleTaskLoop 总耗时');
console.log('  2. recursivelyMakeLLMRequests 各个异步函数耗时');
console.log('  3. 从任务开始到第一个字符返回的总耗时');
console.log('  4. API 请求到第一个字符的耗时');
console.log('  5. 所有性能数据将记录到 perf-{sessionId}.log 文件中');
console.log('');

// 启动开发服务器
const devProcess = spawn('npm', ['run', 'dev'], {
  cwd: projectRoot,
  stdio: 'pipe',
  shell: true
});

let hasFirstToken = false;
let startTime = Date.now();
let sessionId = null;

// 监控性能日志文件
function watchPerfLogs() {
  const logsDir = join(projectRoot, 'logs');
  if (!fs.existsSync(logsDir)) {
    return;
  }

  const perfLogFiles = fs.readdirSync(logsDir).filter(file => file.startsWith('perf-') && file.endsWith('.log'));
  
  perfLogFiles.forEach(file => {
    const filePath = join(logsDir, file);
    const sessionIdMatch = file.match(/perf-(.+)\.log$/);
    if (sessionIdMatch && !sessionId) {
      sessionId = sessionIdMatch[1];
      console.log(`📁 发现性能日志文件: ${file}`);
      console.log(`🆔 会话ID: ${sessionId}`);
      
      // 监控文件变化
      fs.watchFile(filePath, { interval: 100 }, () => {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.split('\n');
          const newLines = lines.slice(-10); // 只显示最新的10行
          
          newLines.forEach(line => {
            if (line.includes('[MILESTONE]') && line.includes('🎯')) {
              console.log(`🎯 ${line.split('] ')[1]}`);
            } else if (line.includes('[END]') && line.includes('Duration:')) {
              const match = line.match(/\[END\] (.+) - Duration: (\d+)ms/);
              if (match) {
                console.log(`📈 ${match[1]}: ${match[2]}ms`);
              }
            } else if (line.includes('[PHASE_START]')) {
              console.log(`📊 ${line.split('] ')[1]}`);
            } else if (line.includes('[PHASE_DETAIL]')) {
              console.log(`  ${line.split('] ')[1]}`);
            } else if (line.includes('[PHASE_END]')) {
              console.log(`📊 ${line.split('] ')[1]}`);
            }
          });
        } catch (error) {
          // 忽略读取错误
        }
      });
    }
  });
}

// 定期检查性能日志文件
const logWatcher = setInterval(watchPerfLogs, 1000);

devProcess.stdout.on('data', (data) => {
  const output = data.toString();
  
  // 检查关键性能日志
  if (output.includes('=== 任务开始 handleTaskLoop ===')) {
    console.log('✅ 检测到任务开始');
    startTime = Date.now();
  }
  
  if (output.includes('🎯 第一个字符返回！从 recursivelyMakeLLMRequests 开始总耗时:') && !hasFirstToken) {
    const match = output.match(/🎯 第一个字符返回！从 recursivelyMakeLLMRequests 开始总耗时: (\d+)ms/);
    if (match) {
      hasFirstToken = true;
      const totalTime = Date.now() - startTime;
      console.log('');
      console.log('🎉 ===== 关键性能指标 =====');
      console.log(`🎯 第一个字符返回总耗时: ${match[1]}ms`);
      console.log(`⏱️  测试脚本记录的总时间: ${totalTime}ms`);
      console.log('🎉 ========================');
      console.log('');
    }
  }
  
  if (output.includes('🎯 其中 API 请求到第一个字符耗时:')) {
    const match = output.match(/🎯 其中 API 请求到第一个字符耗时: (\d+)ms/);
    if (match) {
      console.log(`🎯 API 请求到第一个字符耗时: ${match[1]}ms`);
    }
  }
  
  if (output.includes('任务结束 handleTaskLoop，总耗时:')) {
    const match = output.match(/任务结束 handleTaskLoop，总耗时: (\d+)ms/);
    if (match) {
      console.log(`✅ 任务完成总耗时: ${match[1]}ms`);
    }
  }
  
  // 显示其他重要日志
  if (output.includes('ERROR') || output.includes('Error')) {
    console.log('❌ 错误:', output.trim());
  }
});

devProcess.stderr.on('data', (data) => {
  const output = data.toString();
  if (output.includes('ERROR') || output.includes('Error')) {
    console.log('❌ 错误:', output.trim());
  }
});

devProcess.on('close', (code) => {
  console.log(`\n🏁 进程结束，退出码: ${code}`);
  clearInterval(logWatcher);
});

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在关闭...');
  clearInterval(logWatcher);
  devProcess.kill('SIGTERM');
  
  // 显示性能日志文件位置
  if (sessionId) {
    const logFile = join(projectRoot, 'logs', `perf-${sessionId}.log`);
    if (fs.existsSync(logFile)) {
      console.log(`📁 性能日志已保存到: ${logFile}`);
      console.log('💡 可以使用以下命令查看详细性能数据:');
      console.log(`   tail -f ${logFile}`);
      console.log(`   grep "MILESTONE" ${logFile}`);
      console.log(`   grep "Duration:" ${logFile}`);
    }
  }
  
  process.exit(0);
});

console.log('📝 提示：');
console.log('  - 启动后请在 IDE 中发送一个消息来触发性能统计');
console.log('  - 按 Ctrl+C 退出测试');
console.log('  - 关注带有 📈 和 🎯 标记的性能数据');
console.log('  - 详细性能数据将保存在 logs/perf-{sessionId}.log 文件中');
console.log('');
