#!/usr/bin/env node

/**
 * 性能数据集成示例
 * 展示如何从 LoggerManager 获取结构化的性能数据用于上报和图表绘制
 */

console.log('📊 性能数据集成示例');
console.log('='.repeat(50));

// 模拟从 LoggerManager 获取的性能数据
const mockLoggerManagerData = {
  // 从 loggerManager.getPerformanceUploadData() 获取
  uploadData: {
    sessionId: "session-abc123",
    taskId: "task-def456", 
    totalDuration: 3250,
    metrics: [
      { label: "handleTaskLoop", duration: 3250, category: "task", level: 0 },
      { label: "recursivelyMakeLLMRequests", duration: 2800, category: "api", level: 1 },
      { label: "initTrace", duration: 15, category: "preprocessing", level: 2 },
      { label: "initCheckpointTracker", duration: 120, category: "preprocessing", level: 2 },
      { label: "checkUserModifiedFiles", duration: 85, category: "preprocessing", level: 2 },
      { label: "getEnvironmentDetails", duration: 680, category: "preprocessing", level: 2 },
      { label: "optimizeMessagesContext", duration: 180, category: "preprocessing", level: 2 },
      { label: "buildRequestParams", duration: 65, category: "preprocessing", level: 2 },
      { label: "apiRequest", duration: 1520, category: "api", level: 2 },
    ],
    keyMetrics: {
      firstTokenTime: 1850,
      apiRequestTime: 1520,
      preProcessingTime: 1145
    }
  },
  
  // 从 loggerManager.getPerformanceChartData() 获取
  chartData: {
    labels: ["handleTaskLoop", "recursivelyMakeLLMRequests", "initTrace", "initCheckpointTracker", "checkUserModifiedFiles", "getEnvironmentDetails", "optimizeMessagesContext", "buildRequestParams", "apiRequest"],
    datasets: [
      {
        label: "task",
        data: [3250, 0, 0, 0, 0, 0, 0, 0, 0],
        category: "task"
      },
      {
        label: "api", 
        data: [0, 2800, 0, 0, 0, 0, 0, 0, 1520],
        category: "api"
      },
      {
        label: "preprocessing",
        data: [0, 0, 15, 120, 85, 680, 180, 65, 0],
        category: "preprocessing"
      }
    ]
  }
};

console.log('\n1️⃣ 原始性能数据:');
console.log(`📋 会话ID: ${mockLoggerManagerData.uploadData.sessionId}`);
console.log(`📋 任务ID: ${mockLoggerManagerData.uploadData.taskId}`);
console.log(`⏱️  总耗时: ${mockLoggerManagerData.uploadData.totalDuration}ms`);
console.log(`🎯 第一个字符: ${mockLoggerManagerData.uploadData.keyMetrics.firstTokenTime}ms`);
console.log(`🌐 API请求: ${mockLoggerManagerData.uploadData.keyMetrics.apiRequestTime}ms`);
console.log(`⚙️  预处理: ${mockLoggerManagerData.uploadData.keyMetrics.preProcessingTime}ms`);

console.log('\n2️⃣ 用于多层柱状图的数据结构:');
const chartDataForVisualization = {
  // Chart.js 格式
  type: 'bar',
  data: {
    labels: mockLoggerManagerData.chartData.labels,
    datasets: mockLoggerManagerData.chartData.datasets.map((dataset, index) => ({
      label: dataset.label,
      data: dataset.data,
      backgroundColor: getColorByCategory(dataset.category, 0.6),
      borderColor: getColorByCategory(dataset.category, 1),
      borderWidth: 1,
      stack: 'performance' // 堆叠柱状图
    }))
  },
  options: {
    responsive: true,
    scales: {
      x: {
        stacked: true,
        title: {
          display: true,
          text: '执行阶段'
        }
      },
      y: {
        stacked: true,
        title: {
          display: true,
          text: '耗时 (ms)'
        }
      }
    },
    plugins: {
      title: {
        display: true,
        text: '性能分析 - 各阶段耗时分布'
      },
      legend: {
        display: true,
        position: 'top'
      }
    }
  }
};

console.log(JSON.stringify(chartDataForVisualization, null, 2));

console.log('\n3️⃣ 用于上报的简化数据结构:');
const uploadPayload = {
  timestamp: Date.now(),
  session: mockLoggerManagerData.uploadData.sessionId,
  task: mockLoggerManagerData.uploadData.taskId,
  performance: {
    // 关键指标
    total_duration: mockLoggerManagerData.uploadData.totalDuration,
    first_token_time: mockLoggerManagerData.uploadData.keyMetrics.firstTokenTime,
    api_request_time: mockLoggerManagerData.uploadData.keyMetrics.apiRequestTime,
    preprocessing_time: mockLoggerManagerData.uploadData.keyMetrics.preProcessingTime,
    
    // 性能比例
    api_percentage: Math.round((mockLoggerManagerData.uploadData.keyMetrics.apiRequestTime / mockLoggerManagerData.uploadData.totalDuration) * 100),
    preprocessing_percentage: Math.round((mockLoggerManagerData.uploadData.keyMetrics.preProcessingTime / mockLoggerManagerData.uploadData.totalDuration) * 100),
    
    // 详细分解（用于钻取分析）
    breakdown: mockLoggerManagerData.uploadData.metrics.map(metric => ({
      name: metric.label,
      duration: metric.duration,
      category: metric.category,
      level: metric.level,
      percentage: Math.round((metric.duration / mockLoggerManagerData.uploadData.totalDuration) * 100)
    })),
    
    // 分类汇总
    category_summary: getCategorySummary(mockLoggerManagerData.uploadData.metrics, mockLoggerManagerData.uploadData.totalDuration)
  }
};

console.log(JSON.stringify(uploadPayload, null, 2));

console.log('\n4️⃣ 实际使用代码示例:');
console.log(`
// 在 AgentManager 中的使用方式：

class AgentManager {
  async handleTaskLoop(userContent, isNewTask) {
    // ... 执行任务 ...
    
    // 任务完成后获取性能数据
    const performanceData = this.loggerManager.getPerformanceUploadData();
    const chartData = this.loggerManager.getPerformanceChartData();
    
    // 上报到监控系统
    await this.uploadPerformanceMetrics(performanceData);
    
    // 发送给前端用于图表展示
    this.messageService.say('performance_data', {
      uploadData: performanceData,
      chartData: chartData
    });
  }
  
  private async uploadPerformanceMetrics(data) {
    try {
      // 上报到你的监控系统
      await fetch('/api/performance/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: Date.now(),
          session: data.sessionId,
          task: data.taskId,
          metrics: data.metrics,
          keyMetrics: data.keyMetrics
        })
      });
      
      this.loggerManager.agentInfo('性能数据上报成功');
    } catch (error) {
      this.loggerManager.agentError('性能数据上报失败:', error);
    }
  }
}
`);

console.log('\n5️⃣ 前端图表集成示例:');
console.log(`
// React + Chart.js 示例
import { Bar } from 'react-chartjs-2';

function PerformanceChart({ performanceData }) {
  const chartData = {
    labels: performanceData.chartData.labels,
    datasets: performanceData.chartData.datasets.map(dataset => ({
      label: dataset.label,
      data: dataset.data,
      backgroundColor: getColorByCategory(dataset.category),
      borderColor: getColorByCategory(dataset.category, 1),
      borderWidth: 1
    }))
  };
  
  const options = {
    responsive: true,
    scales: {
      x: { stacked: true },
      y: { stacked: true }
    },
    plugins: {
      title: {
        display: true,
        text: \`性能分析 - 总耗时: \${performanceData.uploadData.totalDuration}ms\`
      }
    }
  };
  
  return <Bar data={chartData} options={options} />;
}
`);

console.log('\n6️⃣ 性能优化建议:');
analyzePerformance(mockLoggerManagerData.uploadData);

// 辅助函数
function getColorByCategory(category, alpha = 0.6) {
  const colors = {
    task: `rgba(255, 99, 132, ${alpha})`,
    api: `rgba(54, 162, 235, ${alpha})`,
    preprocessing: `rgba(255, 205, 86, ${alpha})`,
    postprocessing: `rgba(75, 192, 192, ${alpha})`,
    initialization: `rgba(153, 102, 255, ${alpha})`,
    cleanup: `rgba(255, 159, 64, ${alpha})`
  };
  return colors[category] || `rgba(128, 128, 128, ${alpha})`;
}

function getCategorySummary(metrics, totalDuration) {
  const summary = {};
  
  metrics.forEach(metric => {
    if (!summary[metric.category]) {
      summary[metric.category] = {
        total_duration: 0,
        count: 0,
        percentage: 0
      };
    }
    
    summary[metric.category].total_duration += metric.duration;
    summary[metric.category].count += 1;
  });
  
  Object.keys(summary).forEach(category => {
    summary[category].percentage = Math.round((summary[category].total_duration / totalDuration) * 100);
    summary[category].average_duration = Math.round(summary[category].total_duration / summary[category].count);
  });
  
  return summary;
}

function analyzePerformance(data) {
  const { metrics, keyMetrics, totalDuration } = data;
  
  console.log('🔍 性能分析结果:');
  
  // 找出最慢的操作
  const slowest = metrics
    .filter(m => m.level > 0) // 排除顶层任务
    .sort((a, b) => b.duration - a.duration)
    .slice(0, 3);
  
  console.log('\n🐌 最耗时的操作:');
  slowest.forEach((op, i) => {
    const pct = ((op.duration / totalDuration) * 100).toFixed(1);
    console.log(`  ${i + 1}. ${op.label}: ${op.duration}ms (${pct}%)`);
  });
  
  // 分析比例
  const apiPct = ((keyMetrics.apiRequestTime / totalDuration) * 100).toFixed(1);
  const prePct = ((keyMetrics.preProcessingTime / totalDuration) * 100).toFixed(1);
  
  console.log('\n📊 时间分布:');
  console.log(`  预处理: ${keyMetrics.preProcessingTime}ms (${prePct}%)`);
  console.log(`  API请求: ${keyMetrics.apiRequestTime}ms (${apiPct}%)`);
  
  // 优化建议
  console.log('\n💡 优化建议:');
  if (keyMetrics.preProcessingTime > keyMetrics.apiRequestTime) {
    console.log('  ⚠️  预处理时间过长，建议优化环境信息获取和上下文处理');
  }
  
  const envMetric = metrics.find(m => m.label === 'getEnvironmentDetails');
  if (envMetric && envMetric.duration > 500) {
    console.log('  ⚠️  环境信息获取耗时过长，建议添加缓存');
  }
  
  const contextMetric = metrics.find(m => m.label === 'optimizeMessagesContext');
  if (contextMetric && contextMetric.duration > 100) {
    console.log('  ⚠️  上下文优化耗时较长，建议优化算法');
  }
  
  if (keyMetrics.firstTokenTime > 2000) {
    console.log('  ⚠️  首字符响应时间过长，建议检查网络和模型性能');
  }
}

console.log('\n✅ 性能数据集成示例完成！');
console.log('\n📝 总结:');
console.log('  1. 使用 loggerManager.getPerformanceUploadData() 获取上报数据');
console.log('  2. 使用 loggerManager.getPerformanceChartData() 获取图表数据');
console.log('  3. 数据结构已优化，便于直接用于 Chart.js 等图表库');
console.log('  4. 包含详细的性能分析和优化建议');
console.log('  5. 支持多层柱状图、瀑布图等多种可视化方式');
