#!/usr/bin/env node

/**
 * 测试 TokenCalculator 的性能记录功能
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

async function testTokenCalculatorPerf() {
  try {
    console.log('🧮 测试 TokenCalculator 性能记录功能');
    console.log('='.repeat(50));

    // 导入编译后的模块
    const { TokenCalculator } = await import('../dist/out/agent/context/TokenCalculator.js');
    const { PerfLoggerManager } = await import('../dist/out/util/perf-logger.js');
    
    const sessionId = 'tokenizer-test-' + Date.now();
    const chatId = 'chat-' + Date.now();
    console.log(`🔧 创建 TokenCalculator，会话ID: ${sessionId}，聊天ID: ${chatId}`);

    // 创建 TokenCalculator 实例
    const tokenCalculator = new TokenCalculator('claude-3.5-sonnet', sessionId, chatId);
    
    console.log('\n📝 测试不同长度的文本 token 计算...');
    
    const testTexts = [
      {
        name: '短文本',
        text: 'Hello, world!'
      },
      {
        name: '中等文本',
        text: `
        这是一个中等长度的测试文本，用来测试 TokenCalculator 的性能记录功能。
        我们需要确保性能日志能够正确记录 tokenizer API 的调用耗时。
        包括请求准备、API调用、响应处理等各个阶段的耗时。
        `
      },
      {
        name: '长文本',
        text: `
        这是一个较长的测试文本，用来模拟实际使用场景中的长文本 token 计算。
        在实际的 AI 代理系统中，我们经常需要计算大量文本的 token 数量，
        特别是在上下文管理和消息优化的过程中。
        
        性能监控对于这类操作非常重要，因为：
        1. tokenizer API 调用可能会有网络延迟
        2. 文本长度会影响处理时间
        3. 我们需要识别性能瓶颈
        4. 监控有助于优化用户体验
        
        通过详细的性能记录，我们可以：
        - 分析各个阶段的耗时分布
        - 识别异常缓慢的 API 调用
        - 优化文本处理流程
        - 提供更好的用户反馈
        
        这个测试将验证所有这些性能记录功能是否正常工作。
        `.repeat(3) // 重复3次使文本更长
      }
    ];
    
    for (let i = 0; i < testTexts.length; i++) {
      const testCase = testTexts[i];
      console.log(`\n${i + 1}. 测试 ${testCase.name} (${testCase.text.length} 字符)`);
      
      try {
        const startTime = Date.now();
        const tokenCount = await tokenCalculator.calculate(testCase.text);
        const endTime = Date.now();
        
        console.log(`   ✅ Token 数量: ${tokenCount}`);
        console.log(`   ⏱️  总耗时: ${endTime - startTime}ms`);
        
        // 添加一些延迟，避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.log(`   ❌ 计算失败: ${error.message}`);
        console.log(`   📝 这可能是因为 tokenizer API 不可用，将使用备用方案`);
      }
    }
    
    // 测试错误情况（模拟网络错误）
    console.log('\n🔥 测试错误处理...');
    try {
      // 创建一个会导致错误的 TokenCalculator（使用无效模型）
      const errorChatId = chatId + '-error';
      const errorTokenCalculator = new TokenCalculator('invalid-model', sessionId + '-error', errorChatId);
      await errorTokenCalculator.calculate('test error handling');
    } catch (error) {
      console.log(`   ✅ 错误处理正常: ${error.message}`);
    }

    // 关闭性能日志记录器
    console.log('\n🔚 关闭性能日志记录器');
    PerfLoggerManager.getInstance().closeLogger(sessionId, chatId);
    PerfLoggerManager.getInstance().closeLogger(sessionId + '-error', chatId + '-error');
    
    // 等待日志写入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 读取并分析性能日志
    console.log('\n📊 分析性能日志...');
    console.log('='.repeat(50));
    
    const logDir = join(projectRoot, 'logs');
    if (!fs.existsSync(logDir)) {
      console.log('❌ 日志目录不存在');
      return;
    }
    
    const logFiles = fs.readdirSync(logDir).filter(file =>
      file.includes(`perf-agent-${chatId}`) && file.endsWith('.log')
    );
    
    if (logFiles.length === 0) {
      console.log('❌ 未找到性能日志文件');
      return;
    }
    
    console.log(`📁 找到日志文件: ${logFiles.join(', ')}`);
    
    for (const logFile of logFiles) {
      console.log(`\n📋 分析日志文件: ${logFile}`);
      const logPath = join(logDir, logFile);
      const logContent = fs.readFileSync(logPath, 'utf8');
      
      const lines = logContent.trim().split('\n').filter(line => line.trim());
      console.log(`📊 总日志条数: ${lines.length}`);
      
      // 分析 tokenizer 相关的性能记录
      const tokenizerLogs = [];
      const milestones = [];
      const errors = [];
      
      lines.forEach(line => {
        try {
          const logEntry = JSON.parse(line);
          
          if (logEntry.messageType === 'tokenizer') {
            tokenizerLogs.push(logEntry);
          } else if (logEntry.messageType === 'milestone') {
            milestones.push(logEntry);
          } else if (logEntry.level === 'error') {
            errors.push(logEntry);
          }
        } catch (error) {
          console.log(`⚠️  解析日志失败: ${line}`);
        }
      });
      
      console.log(`\n🧮 Tokenizer 相关日志: ${tokenizerLogs.length} 条`);
      if (tokenizerLogs.length > 0) {
        console.log('📈 主要阶段:');
        
        const stages = {};
        tokenizerLogs.forEach(log => {
          const stage = log.stage.replace(/^tokenizer_/, '');
          if (!stages[stage]) {
            stages[stage] = { start: [], end: [], durations: [] };
          }
          
          if (log.isStart) {
            stages[stage].start.push(log);
          } else if (log.isEnd && log.duration !== undefined) {
            stages[stage].end.push(log);
            stages[stage].durations.push(log.duration);
          }
        });
        
        Object.entries(stages).forEach(([stage, data]) => {
          if (data.durations.length > 0) {
            const avgDuration = data.durations.reduce((a, b) => a + b, 0) / data.durations.length;
            const maxDuration = Math.max(...data.durations);
            const minDuration = Math.min(...data.durations);
            
            console.log(`   ${stage}:`);
            console.log(`     调用次数: ${data.durations.length}`);
            console.log(`     平均耗时: ${avgDuration.toFixed(1)}ms`);
            console.log(`     最大耗时: ${maxDuration}ms`);
            console.log(`     最小耗时: ${minDuration}ms`);
          }
        });
      }
      
      console.log(`\n🎯 里程碑记录: ${milestones.length} 条`);
      milestones.forEach((milestone, index) => {
        console.log(`   ${index + 1}. ${milestone.stage} (${milestone.duration}ms)`);
        if (milestone.message !== 'milestone') {
          try {
            const details = JSON.parse(milestone.message);
            console.log(`      模型: ${details.model || 'N/A'}`);
            console.log(`      文本长度: ${details.textLength || 'N/A'}`);
            console.log(`      Token数量: ${details.tokenCount || 'N/A'}`);
          } catch (e) {
            console.log(`      详情: ${milestone.message}`);
          }
        }
      });
      
      console.log(`\n❌ 错误记录: ${errors.length} 条`);
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.stage}: ${error.message}`);
      });
    }
    
    console.log('\n🎉 TokenCalculator 性能测试完成！');
    console.log('\n💡 性能记录功能验证:');
    console.log('  ✅ 记录了 token 计算的各个阶段');
    console.log('  ✅ 记录了 API 调用耗时');
    console.log('  ✅ 记录了关键性能里程碑');
    console.log('  ✅ 记录了错误处理情况');
    console.log('  ✅ 所有日志都是 JSON 格式');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.code === 'ERR_MODULE_NOT_FOUND') {
      console.log('\n💡 提示: 请先运行构建命令:');
      console.log('   npm run build:dev');
    }
  }
}

// 运行测试
testTokenCalculatorPerf();
