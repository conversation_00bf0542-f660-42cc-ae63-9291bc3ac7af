# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个IDE代理项目（kwaipilot），将TypeScript代码打包成可在任何IDE或平台上运行的二进制文件。项目采用分层架构，核心功能包括代码搜索、索引管理、MCP协议支持和UI预览。

## 常用命令

### 开发与构建
```bash
# 开发模式
npm run dev                    # 启动开发服务器
npm run dev2                   # 启动开发服务器（端口3001）

# 构建
npm run build                  # 完整构建（注入配置 + 打包）
npm run inject                 # 注入追踪配置
npm run build:dev              # 仅TypeScript编译
node scripts/build.js --esbuild-only  # 仅esbuild打包

# 测试
npm run test                   # 运行所有测试
npm run test -- <test-file>    # 运行特定测试文件

# 代码质量
npm run lint                   # ESLint检查
npm run lint:fix               # 自动修复ESLint问题
npm run format                 # Prettier格式化
npm run format:check           # 检查格式化
npm run check                  # lint + format检查

# 其他
npm run start                  # 直接启动（不推荐用于开发）
npm run mcp                    # 启动MCP服务
npm run pipeline               # 启动管道服务
```

### 测试调试
```bash
# 调试测试客户端
node test/client.mjs

# 单元测试（Jest）
npm test
# 运行特定测试
npm test -- test/agent/context/ContextManager.test.ts
```

## 项目架构

### 主要模块

1. **核心模块 (src/core/)**
   - `Core`: 系统主控制器，协调各个模块
   - `IdeStateManager`: IDE状态管理
   - `agent-message-handler`: 代理消息处理

2. **HTTP通信 (src/http/)**
   - `HttpClient`: 基础HTTP客户端，支持重试、拦截器
   - `Api`: 封装所有API调用（代码搜索、索引等）

3. **索引系统 (src/indexing/ + src/index-manager/)**
   - `SearchManager`: 本地搜索管理
   - `CloudIndexManager`: 云端索引管理
   - 支持文件索引、搜索、构建状态检查

4. **代理系统 (src/agent/)**
   - `AgentManager`: 代理管理
   - `prompt/`: 各种提示模板（Claude、Kwaipilot等）
   - `services/`: 各种服务（日志、消息、工具执行等）
   - `tools/`: 工具实现（文件操作、搜索等）

5. **MCP协议 (src/mcp/)**
   - `McpClient`: MCP客户端实现
   - 支持MCP服务器管理、设置等

6. **UI预览 (src/ui-preview/)**
   - 浏览器预览功能
   - 代理服务器支持

7. **数据库 (src/db/sqlite/)**
   - SQLite数据库管理
   - 表迁移和初始化

### 关键类和技术栈

- **协议通信**: 基于TCP的消息传递系统
- **状态管理**: 全局配置管理（GlobalConfig）
- **多语言支持**: i18n国际化
- **性能监控**: Langfuse集成，性能日志
- **构建系统**: esbuild + pkg打包成二进制
- **测试框架**: Jest + ts-jest

### 文件结构

```
src/
├── core/              # 核心系统
├── http/              # HTTP通信
├── agent/             # 代理功能
├── indexing/          # 本地索引
├── index-manager/     # 索引管理
├── mcp/               # MCP协议
├── ui-preview/        # UI预览
├── db/                # 数据库
├── protocol/          # 通信协议
└── util/              # 工具函数
```

### 重要配置

- **构建配置**: `scripts/build.js` - 主构建脚本
- **打包配置**: `pkgJson/` - pkg打包配置
- **环境变量**: `.env.trace` - 追踪配置
- **测试配置**: `jest.config.js` - Jest配置

### 性能与监控

- 性能日志通过`logger.perf()`记录
- 错误报告通过`logger.error()`和性能监控
- CPU监控每10秒间隔执行

### 注意事项

1. **环境要求**: Node.js >= 18.20.4
2. **构建产物**: 
   - `dist/out/` - esbuild输出
   - `dist/bin/` - pkg打包的二进制文件
3. **测试忽略**: `binary.test.ts`目前无法通过单测
4. **外部依赖**: 包含native模块（sqlite3等），需要跨平台构建

## 开发建议

1. **调试**: 使用`npm run dev`进行开发
2. **测试**: 确保格式化和lint通过后再提交
3. **性能**: 注意性能敏感代码的监控日志
4. **兼容性**: 考虑跨平台兼容性，特别是native模块
5. **协议**: 修改协议时需同步更新消息处理器