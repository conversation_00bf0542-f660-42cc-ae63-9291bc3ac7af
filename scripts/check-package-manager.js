#!/usr/bin/env node

/**
 * 检查包管理器，确保只使用 npm
 */

const userAgent = process.env.npm_config_user_agent || '';

if (!userAgent.startsWith('npm/')) {
  console.error('❌ 错误：此项目必须使用 npm 进行安装！');
  console.error('');
  console.error('检测到的包管理器:', userAgent);
  console.error('');
  console.error('请使用以下命令安装依赖：');
  console.error('  npm install');
  console.error('');
  console.error('如果你想使用其他包管理器，请联系项目维护者。');
  process.exit(1);
}

console.log('✅ 包管理器检查通过：使用 npm');