diff --git a/node_modules/langfuse-core/lib/index.cjs.js b/node_modules/langfuse-core/lib/index.cjs.js
index 1f7e7f9..e8e0eca 100644
--- a/node_modules/langfuse-core/lib/index.cjs.js
+++ b/node_modules/langfuse-core/lib/index.cjs.js
@@ -1669,9 +1669,6 @@ class LangfuseCore extends LangfuseCoreStateless {
     let isObservabilityEnabled = enabled === false ? false : true;
     if (_isLocalEventExportEnabled) {
       isObservabilityEnabled = true;
-    } else if (!secretKey) {
-      isObservabilityEnabled = false;
-      console.warn("Langfuse secret key was not passed to constructor or not set as 'LANGFUSE_SECRET_KEY' environment variable. No observability data will be sent to Langfuse.");
     } else if (!publicKey) {
       isObservabilityEnabled = false;
       console.warn("Langfuse public key was not passed to constructor or not set as 'LANGFUSE_PUBLIC_KEY' environment variable. No observability data will be sent to Langfuse.");
diff --git a/node_modules/langfuse-core/lib/index.mjs b/node_modules/langfuse-core/lib/index.mjs
index 7020f07..57985b0 100644
--- a/node_modules/langfuse-core/lib/index.mjs
+++ b/node_modules/langfuse-core/lib/index.mjs
@@ -1667,9 +1667,6 @@ class LangfuseCore extends LangfuseCoreStateless {
     let isObservabilityEnabled = enabled === false ? false : true;
     if (_isLocalEventExportEnabled) {
       isObservabilityEnabled = true;
-    } else if (!secretKey) {
-      isObservabilityEnabled = false;
-      console.warn("Langfuse secret key was not passed to constructor or not set as 'LANGFUSE_SECRET_KEY' environment variable. No observability data will be sent to Langfuse.");
     } else if (!publicKey) {
       isObservabilityEnabled = false;
       console.warn("Langfuse public key was not passed to constructor or not set as 'LANGFUSE_PUBLIC_KEY' environment variable. No observability data will be sent to Langfuse.");
