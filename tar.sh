#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息函数
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --tag TAG     指定自定义标签 (可选)"
    echo "  -h, --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                使用默认标签"
    echo "  $0 -t v1.0.1      使用自定义标签 v1.0.1"
    echo "  $0 --tag beta     使用自定义标签 beta"
}

# 解析命令行参数
TAG=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查是否在正确的目录
if [[ ! -f "package.json" ]]; then
    print_error "请在项目根目录下运行此脚本"
    exit 1
fi

# 检查dist目录是否存在
if [[ ! -d "dist" ]]; then
    print_error "dist目录不存在，请先运行构建命令"
    exit 1
fi

# 检查dist/bin目录是否存在
if [[ ! -d "dist/bin" ]]; then
    print_error "dist/bin目录不存在，请先运行构建命令"
    exit 1
fi

# 从package.json读取版本号
VERSION=$(node -p "require('./package.json').version")
if [[ -z "$VERSION" ]]; then
    print_error "无法从package.json读取版本号"
    exit 1
fi

print_info "版本号: $VERSION"

# 根据是否有tag来设置压缩包名称
if [[ -n "$TAG" ]]; then
    ARCHIVE_NAME="kwaipilot-binary-$VERSION-$TAG.tar.gz"
    print_info "标签: $TAG"
else
    ARCHIVE_NAME="kwaipilot-binary-$VERSION.tar.gz"
    print_info "未指定标签"
fi

# 进入dist目录
cd dist

# 检查是否已经存在kwaipilot-binary目录
if [[ -d "kwaipilot-binary" ]]; then
    print_warning "kwaipilot-binary目录已存在，将被覆盖"
    rm -rf kwaipilot-binary
fi

# 重命名bin目录为kwaipilot-binary
print_info "重命名bin目录为kwaipilot-binary..."
mv bin kwaipilot-binary

# 创建压缩包
print_info "创建压缩包: $ARCHIVE_NAME"
tar -czvf "$ARCHIVE_NAME" ./kwaipilot-binary

# 检查压缩包是否创建成功
if [[ -f "$ARCHIVE_NAME" ]]; then
    # 获取压缩包大小
    ARCHIVE_SIZE=$(du -h "$ARCHIVE_NAME" | cut -f1)
    print_info "压缩包创建成功: $ARCHIVE_NAME (大小: $ARCHIVE_SIZE)"
else
    print_error "压缩包创建失败"
    exit 1
fi

# 恢复bin目录名称（可选，保持目录结构一致）
print_info "恢复bin目录名称..."
mv kwaipilot-binary bin

print_info "打包完成！"
print_info "压缩包位置: dist/$ARCHIVE_NAME"
